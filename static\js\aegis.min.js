﻿/**
 *  =============================================================================
 * @tencent/aegis-web-sdk-v2@2.5.42 (c) 2025 TencentCloud Real User Monitoring.
 * Last Release Time Tue Apr 01 2025 20:09:46 GMT+0800 (China Standard Time).
 * Released under the MIT License.
 * =============================================================================
 **/
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).Aegis=t()}(this,function(){"use strict";var P,L;function N(e){this.name="__st"+(1e9*Math.random()>>>0)+P+"__",null!=e&&e.forEach(this.add,this),P+=1}Array.prototype.find||Object.defineProperty(Array.prototype,"find",{configurable:!0,writable:!0,value:function(e){if(null===this)throw new TypeError('"this" is null or not defined');var t=Object(this),n=t.length>>>0;if("function"!=typeof e)throw new TypeError("predicate must be a function");for(var r=arguments[1],i=0;i<n;){var o=t[i];if(e.call(r,o,i,t))return o;i+=1}}}),"undefined"==typeof window||window.WeakSet||(P=Date.now()%1e9,N.prototype.add=function(e){var t=this.name;return e[t]||Object.defineProperty(e,t,{value:!0,writable:!0}),this},N.prototype.delete=function(e){return!!e[this.name]&&!(e[this.name]=void 0)},N.prototype.has=function(e){return!!e[this.name]},L=N,Object.defineProperty(window,"WeakSet",{value:function(e){return new L(e)}}));var _=function(e,t){return(_=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(e,t){e.__proto__=t}:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}))(e,t)},G=function(){return(G=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};function x(e,t){var n={};for(i in e)Object.prototype.hasOwnProperty.call(e,i)&&t.indexOf(i)<0&&(n[i]=e[i]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,i=Object.getOwnPropertySymbols(e);r<i.length;r++)t.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(e,i[r])&&(n[i[r]]=e[i[r]]);return n}function K(e,s,a,u){return new(a=a||Promise)(function(n,t){function r(e){try{o(u.next(e))}catch(e){t(e)}}function i(e){try{o(u.throw(e))}catch(e){t(e)}}function o(e){var t;e.done?n(e.value):((t=e.value)instanceof a?t:new a(function(e){e(t)})).then(r,i)}o((u=u.apply(e,s||[])).next())})}function X(r,i){var o,s,a,u={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]},e={next:t(0),throw:t(1),return:t(2)};return"function"==typeof Symbol&&(e[Symbol.iterator]=function(){return this}),e;function t(n){return function(e){var t=[n,e];if(o)throw new TypeError("Generator is already executing.");for(;u;)try{if(o=1,s&&(a=2&t[0]?s.return:t[0]?s.throw||((a=s.return)&&a.call(s),0):s.next)&&!(a=a.call(s,t[1])).done)return a;switch(s=0,(t=a?[2&t[0],a.value]:t)[0]){case 0:case 1:a=t;break;case 4:return u.label++,{value:t[1],done:!1};case 5:u.label++,s=t[1],t=[0];continue;case 7:t=u.ops.pop(),u.trys.pop();continue;default:if(!((a=0<(a=u.trys).length&&a[a.length-1])||6!==t[0]&&2!==t[0])){u=0;continue}if(3===t[0]&&(!a||t[1]>a[0]&&t[1]<a[3]))u.label=t[1];else if(6===t[0]&&u.label<a[1])u.label=a[1],a=t;else{if(!(a&&u.label<a[2])){a[2]&&u.ops.pop(),u.trys.pop();continue}u.label=a[2],u.ops.push(t)}}t=i.call(r,u)}catch(e){t=[6,e],s=0}finally{o=a=0}if(5&t[0])throw t[1];return{value:t[0]?t[1]:void 0,done:!0}}}}function D(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;for(var r=Array(e),i=0,t=0;t<n;t++)for(var o=arguments[t],s=0,a=o.length;s<a;s++,i++)r[i]=o[s];return r}Object.assign||Object.defineProperty(Object,"assign",{enumerable:!1,configurable:!0,writable:!0,value:function(e){if(null==e)throw new TypeError("Cannot convert first argument to object");for(var t=Object(e),n=1;n<arguments.length;n++)if(null!=(r=arguments[n]))for(var r=Object(r),i=Object.keys(Object(r)),o=0,s=i.length;o<s;o++){var a=i[o],u=Object.getOwnPropertyDescriptor(r,a);null!=u&&u.enumerable&&(t[a]=r[a])}return t}});function M(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)})}var V,z,q,r,H,b,B={snapshootInfo:void 0,type:void 0,level:void 0,plugin:void 0,originEvent:void 0},v={PV:"pv",AID:"aid",F_ID:"fId",ERROR:"error",DEVICE:"device",CLOSE:"close",PAGE_PERFORMANCE:"pagePerformance",WEB_VITALS:"webVitals",IE:"ie",SPA:"spa",API:"api",ASSET_SPEED:"assetSpeed",CUSTOM:"custom",SESSION:"session",BRIDGE_SPEED:"bridgeSpeed",LOAD_PACKAGE:"loadPackageSpeed",BLANK_SCREEN:"blankScreen",WEBSOCKET:"websocket"},F=["aegis.qq.com","tamaegis.com","/aegis-sdk","rumt-","/flog.core.min.js","pingfore.qq.com","pingfore.tencent.com","zhiyan.tencent-cloud.net","h.trace.qq.com","btrace.qq.com","beacon.qq.com","dmplog.qq.com","qq.com/report","svibeacon.onezapp.com","cube.weixinbridge.com","doubleclick.net","pcmgrmonitor.3g.qq.com","tdm.qq.com","report.qqweb.qq.com","tpstelemetry.tencent.com","galileotelemetry.tencent.com","insight.cloud.tencent.com","facebook.com","facebook.net","google","yahoo.com","twitter.com","ga-audiences","report.idqqimg.com","arms-retcode.aliyuncs.com","px.effirst.com","sentry","baidu.com","hot-update.json","u.c.b.r.o.w.s.e.r","report.url.cn","sockjs-node","m3u8","flv"],j=["ResizeObserver loop limit exceeded","ResizeObserver loop completed with undelivered notifications","Failed to execute 'transaction'","window.indexedDB.deleteDatabase is not a function"],U=["level","trace","tag","seq","code"],W=["static","fetch"],Q=((i=V=V||{}).INFO="info",i.ERROR="error",i.SDK_ERROR="sdk_error",i.PROMISE_ERROR="promise_error",i.AJAX_ERROR="ajax_error",i.SCRIPT_ERROR="script_error",i.WEBSOCKET_ERROR="websocket_error",i.IMAGE_ERROR="image_error",i.CSS_ERROR="css_error",i.MEDIA_ERROR="media_error",i.REPORT="report",i.RET_ERROR="ret_error",i.BRIDGE_ERROR="brige_error",i.BLANK_SCREEN="blank_screen",i.CUSTOM_ERROR="custom_error",i.PAGE_NOT_FOUND_ERROR="page_not_found_error",i.LAZY_LOAD_ERROR="lazy_load_error",(i=z=z||{}).NORMAL="normal",i.PV="pv",i.API="api",i.CUSTOM_TIME="custom_time",i.CUSTOM_EVENT="custom_event",i.ASSETS_SPEED="assets_speed",i.PAGE_PERFORMANCE="page_performance",i.WEB_VITALS="web_vitals",i.SDK_ERROR="sdk_error",i.SESSION="session",i.BRIDGE="bridge",i.WEBSOCKET="websocket",i.SSE="sse",(i=q=q||{}).android="android",i.ios="iOS",i.windows="windows",i.macos="macOS",i.linux="linux",i.devtools="devtools",i.other="other",(i=r=r||{}).unknown="unknown",i.wifi="wifi",i.net2g="2G",i.net3g="3G",i.net4g="4G",i.net5g="5G",i.net6g="6G",0,(i=H=H||{}).PROD="production",i.DEV="development",i.GRAY="gray",i.PRE="pre",i.DAILY="daily",i.LOCAL="local",i.TEST="test",i.OTHER="others",0,0,(i=b=b||{}).init="init",i.sampleChange="sampleChange",i.destroy="destroy",i.configChange="configChange",i.errorOccurred="errorOccurred",Y.prototype.indexOf=function(e,t){for(var n=0;n<e.length;n++)if(e[n].callback===t)return n;return-1},Y.prototype.on=function(e,t,n){var r;if(void 0===n&&(n=0),this)return(r=this.eventsList[e])||(this.eventsList[e]=[],r=this.eventsList[e]),-1===this.indexOf(r,t)&&r.push({name:e,type:n||0,callback:t}),this},Y.prototype.one=function(e,t){this.on(e,t,1)},Y.prototype.remove=function(e,t){if(this){var n=this.eventsList[e];if(n){if(t)return n.length&&(t=this.indexOf(n,t),n.splice(t,1)),this;try{delete this.eventsList[e]}catch(e){}}return null}},Y.prototype.clear=function(){this.eventsList={}},Y);function Y(){var s=this;this.emit=function(e,t){if(s){var n;if(null!=(r=s.eventsList[e])&&r.length)for(var r=r.slice(),i=0;i<r.length;i++){n=r[i];try{var o=n.callback.apply(s,[t]);if(1===n.type&&s.remove(e,n.callback),!1===o)break}catch(e){throw e}}return s}},this.eventsList={}}function $(){return([1e7]+1e3+4e3+8e3+1e11).replace(/[018]/g,function(e){return(e^(16*Math.random()&15)>>e/4).toString(16)})}var y={generateTraceId:ee(16),generateSpanId:ee(8)},Z=Array(32);function ee(t){return function(){for(var e=0;e<2*t;e++)Z[e]=Math.floor(16*Math.random())+48,58<=Z[e]&&(Z[e]+=39);return String.fromCharCode.apply(null,Z.slice(0,2*t))}}function te(e){var t,n=["",""];return n="object"==typeof e&&(t=(e=function(e){for(var t=0,n=Object.keys(ne);t<n.length;t++){var r=n[t],i=e[r]||"function"==typeof e.get&&e.get(r);if(i)return[r,i]}return["",""]}(e))[0],e=e[1],t)?ne[t](e):n}var a,ne={sw8:function(e){var e=e.split("-"),t=e[1],e=e[2];return t?[atob(t),atob(e)]:["",""]},traceparent:function(e){e=e.split("-");return[e[1],e[2]]},b3:function(e){e=e.split("-");return[e[0],e[1]]},"sentry-trace":function(e){e=e.split("-");return[e[0],e[1]]}},re=(e.prototype.generate=function(e,t){if(void 0===t&&(t={}),this.url=e,!this.isUrlIgnored()&&this.isUrlInTraceUrls()&&this.traceType){switch(this.traceType){case"traceparent":this.traceId=this.createTraceparent();break;case"b3":this.traceId=this.createB3();break;case"sw8":this.traceId=this.createSw8();break;case"sentry-trace":this.traceId=this.createSentryTrace();break;default:return console.warn("this trace key "+this.traceType+" is not supported"),void(this.traceId="")}return t[this.traceType]&&(this.traceId=t[this.traceType]),{name:this.traceType,value:this.traceId}}},e.prototype.createTraceparent=function(){var e=y.generateSpanId();return"00-"+y.generateTraceId()+"-"+e+"-0"+Number(1).toString(16)},e.prototype.createB3=function(){var e=y.generateSpanId();return y.generateTraceId()+"-"+e+"-1"},e.prototype.createSw8=function(){var e=new URL(location.href),t=M(),n=M();return"1-"+String(btoa(n))+"-"+String(btoa(t))+"-1-"+String(btoa("aegis"))+"-"+String(btoa("2.5.42"))+"-"+String(btoa(encodeURI(location.pathname)))+"-"+String(btoa(e.host))},e.prototype.createSentryTrace=function(){var e=$().substring(16);return $()+"-"+e+"-1"},e.prototype.isUrlIgnored=function(){if(Array.isArray(this.ignoreUrls)&&0!==this.ignoreUrls.length)for(var e=0,t=this.ignoreUrls;e<t.length;e++){var n=t[e];if(this.urlMatches(this.url,n))return!0}return!1},e.prototype.isUrlInTraceUrls=function(){if(!this.urls)return!0;if(Array.isArray(this.urls)){if(0===this.urls.length)return!1;for(var e=0,t=this.urls;e<t.length;e++){var n=t[e];if(this.urlMatches(this.url,n))return!0}}return!1},e.prototype.urlMatches=function(e,t){return"string"==typeof t?e===t:!!e.match(t)},e);function e(e,t,n){void 0===n&&(n=null),this.traceType=e,this.ignoreUrls=t,this.urls=n}(i=a=a||{})[i.number=-1]="number",i.string="";function A(e,t){return"string"==typeof e?e.split("?")[t?1:0]||"":e}function ie(e,t){return void 0===t&&(t=2048),null==(e=String(e).split("?")[0])?void 0:e.slice(0,t)}function oe(e){return"string"==typeof e&&/^\//.test(e)?"https:"===(null===location||void 0===location?void 0:location.protocol):/^https/.test(e)}function se(o,s,a){return K(void 0,void 0,void 0,function(){var t,n,r,i;return X(this,function(e){switch(e.label){case 0:return e.trys.push([0,3,,4]),"function"==typeof(null==s?void 0:s.retCodeHandlerAsync)?[2,new Promise(function(n){s.retCodeHandlerAsync(o,null==a?void 0:a.url,null==a?void 0:a.ctx,function(e){var t=e.code,e=e.isErr;n({code:void 0===t?"unknown":t,isErr:e})})})]:"function"!=typeof(null==s?void 0:s.retCodeHandler)?[3,2]:[4,s.retCodeHandler(o,null==a?void 0:a.url,null==a?void 0:a.ctx,null==a?void 0:a.payload)];case 1:return r=e.sent()||{},n=r.code,r=r.isErr,[2,{code:void 0===n?"unknown":n,isErr:r}];case 2:return(o="string"==typeof o?JSON.parse(o):o)&&(i="function"==typeof(null==(r=null==s?void 0:s.ret)?void 0:r.join),t=i?null==(i=null==s?void 0:s.ret)?void 0:i.map(function(e){return e.toLowerCase()}):he,i=Object.getOwnPropertyNames(o),(i=i.filter(function(e){return-1!==t.indexOf(e.toLowerCase())})).length)?[2,{code:""+(n="未知"!==(n=o[i[0]])&&""!==n?n:"unknown"),isErr:0!==n&&"0"!==n&&"unknown"!==n}]:[2,me];case 3:return e.sent(),[2,me];case 4:return[2]}})})}function ae(e,t,n){try{var r="function"==typeof t?t(e,null==n?void 0:n.url)||"":e;return ve(r).slice(0,102400)}catch(e){return""}}function c(e){if("string"==typeof e)return e;try{return e instanceof Error?(JSON.stringify(e,ge())||"undefined").replace(/"/gim,""):JSON.stringify(e,ge())||"undefined"}catch(e){return"error happen when aegis stringify: \n "+e.message+" \n "+e.stack}}function ue(e){if("string"==typeof e)return e;try{return JSON.stringify(e,ge())||"undefined"}catch(e){return"error happen when aegis stringify: \n "+e.message+" \n "+e.stack}}function ce(t,n){var e;return"string"!=typeof t||!t||(e=!1,e=n?"string"==typeof n?-1<t.indexOf(n):Object.keys(n).some(function(e){return-1<t.indexOf(n[e])}):e)||be.test(t)||F.some(function(e){return-1<t.indexOf(e)})}function f(t,n,r){return K(void 0,void 0,void 0,function(){return X(this,function(e){switch(e.label){case 0:return"function"!=typeof t?[3,2]:[4,t.call(n,r,n)];case 1:return[2,e.sent()];case 2:return[2,r||[]]}})})}function k(e,t){if("string"==typeof t){var t=t.split("."),n=t.pop();if(n)return"object"==typeof(t=t.reduce(function(e,t){return null==e?void 0:e[t]},e))?t[n]:t}}function le(e,t,n,r){return"url: "+e+"\nresdata: "+t+"\nstatus: "+n+" \nretcode: "+r}function fe(e){return[V.ERROR,V.PROMISE_ERROR,V.AJAX_ERROR,V.SCRIPT_ERROR,V.IMAGE_ERROR,V.CSS_ERROR,V.MEDIA_ERROR,V.WEBSOCKET_ERROR,V.BRIDGE_ERROR,V.SDK_ERROR,V.BLANK_SCREEN,V.RET_ERROR,V.PAGE_NOT_FOUND_ERROR,V.LAZY_LOAD_ERROR,V.CUSTOM_ERROR].includes(e.level)}var d,de=["application/xhtml+xml","application/xml","application/pdf","application/pkcs12","application/javascript","application/x-javascript","application/ecmascript","application/vnd.mspowerpoint","application/vnd.apple.mpegurl","application/ogg","text/css","text/javascript","image","audio","video","video/mp2t"],pe=/\.(json|js|css|jpg|jpeg|png|svg|apng|webp|gif|bmp|mp4|mp3|ts|mpeg|wav|webm|ogg|flv|m3u8|ttf|woff2|otf|eot|woff|html|htm|shtml|shtm|)$/gi,he=["ret","retcode","code","errcode"],me={code:"unknown",isErr:!1},ge=function(){var n=new WeakSet;return function(e,t){if(t instanceof Error)return"Error.message: "+t.message+" \n  Error.stack: "+t.stack;if("object"==typeof t&&null!==t){if(n.has(t))return"[Circular "+(e||"root")+"]";n.add(t)}return t}},ve=function(n,r){void 0===r&&(r=3);var i,o,s,a="";return Array.isArray(n)?(a+="[",i=n.length,n.forEach(function(e,t){a=(a+="object"==typeof e&&1<r?ve(e,r-1):we(e))+(t===i-1?"":",")}),a+="]"):n instanceof Object?(a="{",o=Object.keys(n),s=o.length,o.forEach(function(e,t){"object"==typeof n[e]&&1<r?a+='"'+e+'":'+ve(n[e],r-1):a+=ye(e,n[e]),a+=t===s-1||t<s-1&&void 0===n[o[t+1]]?"":","}),a+="}"):a+=n,a},ye=function(e,t){var n=typeof t,r="";return"string"==n||"object"==n?r+='"'+e+'":"'+t+'"':"function"==typeof t?r+='"'+e+'":"function '+t.name+'"':"symbol"==typeof t?r+='"'+e+'":"symbol"':"number"!=typeof t&&"boolean"!=n||(r+='"'+e+'": '+t),r},we=function(e){var t=typeof e;return""+("undefined"==t||"symbol"==t||"function"==t?"null":"string"==t||"object"==t?'"'+e+'"':e)},be=/data:(image|text|application|font)\/.*;base64/,J=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];if(!t.length)return e;var r,i=t.shift();for(r in i)"object"==typeof i[r]&&null!==i[r]&&e.hasOwnProperty(r)&&"object"==typeof e[r]&&null!==e[r]?J(e[r],i[r]):e[r]=i[r];return J.apply(void 0,D([e],t))},Ee=(t.prototype.patch=function(e){this.canUse(e)&&this.exist(e)&&(this.plugins.push(e),this.triggerInit(e),this.triggerOnNewAegis(e))},t.prototype.unPatch=function(e){var t=this.plugins.indexOf(e);-1!==t&&(this.plugins.splice(t,1),0===this.plugins.length)&&this.uninstall(e)},t.prototype.uninstall=function(e){var t;null!=(t=null==(t=e.option)?void 0:t.destroy)&&t.call(e.option,this.aegis,this.getConfig(e))},t.prototype.walk=function(t){var n=this;this.plugins.forEach(function(e){n.canUse(e)&&t(e,n.getConfig(e))})},t.prototype.canUse=function(e){return!e||!(!(e=this.getConfig(e))||"object"!=typeof e)||!!e},t.prototype.getConfig=function(e){var t;return null!=(t=null==(t=this.config)?void 0:t[e.name])?t:null==(t=this.aegis.config)?void 0:t[e.name]},t.prototype.exist=function(e){return-1===this.plugins.indexOf(e)},t.prototype.triggerInit=function(e){var t;this.initializedPlugin[e.name]||(e.option.aegis=this.aegis,e.aegis=this.aegis,this.initializedPlugin[e.name]=!0,null==(t=null==(t=e.option)?void 0:t.init))||t.call(e.option,this.aegis,this.getConfig(e))},t.prototype.triggerOnNewAegis=function(e){var t;f(this.aegis.config.onBeforeCollect,this.aegis),null!=(t=null==(t=e.option)?void 0:t.onNewAegis)&&t.call(e.option,this.aegis,this.getConfig(e))},t);function t(e,t){this.plugins=[],this.initializedPlugin={},this.aegis=e,this.config=t}(i=d=d||{})[i.hasNotFetched=0]="hasNotFetched",i[i.fetching=1]="fetching",i[i.alreadyFetched=2]="alreadyFetched";function Se(e){return e.filter(function(n,r){return"static"!==(null==n?void 0:n.requestType)||!e.find(function(e,t){return(null==n?void 0:n.url)===e.url&&200===(null==n?void 0:n.status)&&r<t})})}function Re(t){return new Promise(function(e){return Array.isArray(t)?e(t.map(function(e){return t=G(G({},e),{msg:"string"==typeof e.msg?e.msg:[].concat(e.msg).map(c).join(" ")}),U.forEach(function(e){t[e]||delete t[e]}),t;var t})):e([G(G({},t),{msg:"string"==typeof t.msg?t.msg:c(t.msg)})])})}function Te(s){if(Array.isArray(s)&&0!==s.length)return function(e){return new Promise(function(i){var o=function(n,r){return K(void 0,void 0,void 0,function(){var t;return X(this,function(e){switch(e.label){case 0:return n<s.length?[4,s[n](r)]:[3,2];case 1:return(t=e.sent()).length?o(n+1,t):null!=i&&i([]),[3,3];case 2:null!=i&&i(r),e.label=3;case 3:return[2]}})})};o(0,e)})};throw new TypeError("createPipeline needs at least one function param")}function Oe(t,n){Object.getOwnPropertyNames(t).forEach(function(e){"function"==typeof t[e]&&"constructor"!==e&&(n?n[e]="sendPipeline"===e?function(){return function(){}}:function(){}:t[e]=function(){})})}function Ce(e){return Promise.resolve(e)}var n,Ie={name:"sample",create:function(u){function c(){u.fetchSampleStatus=d.alreadyFetched,a.length&&u.send(a.splice(0,a.length))}function s(){return u.fetchSampleStatus=d.fetching,new Promise(function(s){var a=Date.now();u.request({url:k(u.config,"hostUrl.whiteListUrl")+"?uid="+t+"&topic="+u.config.id,payload:{},method:"GET"},function(e){var t,e=e.data||JSON.parse(e),n=e.is_in_white_list,r=e.sample_map,i=void 0===r?{}:r,r=e.server_time,o=e.start_server_time;0!==e.code?(u.sampleMap=l,u.event.emit(b.sampleChange)):(u.isWhiteList=!!n,n&&u.config.whiteList?Object.keys(v).forEach(function(e){l[v[e]]=1}):(t={},Object.keys(i).forEach(function(e){t[e.replace(/(_\w)/g,function(e){return e[1].toUpperCase()})]=i[e]/100}),J(l,t)),u.sampleMap=l,u.event.emit(b.sampleChange),n=((e=Date.now())-a-(Number(r)-Number(o)))/2,o=Number(r)+n,u.serverTimeGap=Math.floor(o-e)),c(),s()},function(){u.sampleMap=l,u.event.emit(b.sampleChange),c(),s()})})}n=u,r={},Object.keys(v).forEach(function(e){var e=v[e],t=k(n.config,"sample."+e);r[e]=t});var e,n,r,l=r,t=null==(e=u.config)?void 0:e.uid,a=[];return u.event.on(b.configChange,function(e){e.uid!==t&&(t=e.uid,s())}),function(o){return new Promise(function(i){return K(void 0,void 0,void 0,function(){var t,n,r;return X(this,function(e){switch(e.label){case 0:return d.alreadyFetched===u.fetchSampleStatus?(t=u.sampleMap,n=u.config.forceReportErrorLog,r=o.filter(function(e){return!e.plugin||!((!n||!fe(e))&&(e=e.plugin===v.SPA?v.PV:e.plugin,void 0!==(e=t[e]))&&Math.random()>e)}),[2,i(r)]):(a=a.concat(o),d.hasNotFetched!==u.fetchSampleStatus?[3,2]:[4,s()]);case 1:return e.sent(),[2,i([])];case 2:return d.fetching===u.fetchSampleStatus?[2,i([])]:[2]}})})})}}},Pe={name:"throttle",create:function(n,r){var i,o=[],s=n.config;return n.event.on(b.destroy,function(){o.length=0}),function(t){return new Promise(function(e){if(o=o.concat(t),r&&o.length>=r||n.sendNow&&0<o.length)return o=Se(o),i&&clearTimeout(i),e(o.splice(0,!n.sendNow&&r||o.length));i&&clearTimeout(i),i=setTimeout(function(){i=null,o=Se(o),e(o.splice(0,o.length))},s.delay)})}}},Le={name:"format",create:function(){return Re}},Ae={name:"lengthLimit",create:function(n){return function(t){return K(void 0,void 0,void 0,function(){var r;return X(this,function(e){return r=n.config,t=t.map(function(e){var t,n=r.maxLength||102400;try{if(!e.msg||e.msg.length<=n)return e;e.msg=null==(t=e.msg)?void 0:t.substring(0,n)}catch(t){e.msg=null==(n=c(e.msg))?void 0:n.substring(0,r.maxLength)}return e}),[2,Promise.resolve(t)]})})}}},ke={},Ne={},_e={name:"repeatLimit",create:function(e){return n=e.config,function(t){return new Promise(function(e){var r="number"==typeof n.repeat?n.repeat:60;if(!k(n,"sample.speed")||r<=0)return e(t);var i=(null==n?void 0:n.id)||"0",o=Ne[i]||{};e(t.filter(function(e){var t,n=!o[e.url]||o[e.url]<r;return n?(o[e.url]=1+~~o[e.url],Ne[i]=o):ke[i]||(ke[t=i]||(ke[t]=setTimeout(function(){Ne[t]={},ke[t]=null},6e4))),n}))})};var n}},u={},xe={},De={name:"errorLogCollectLimit",create:function(e){var t=e.config,t=void 0===t?{}:t,i="number"==typeof t.repeat?t.repeat:60,o=(null!=(t=null==t?void 0:t.id)?t:0)+"_error",s=xe[o]||{};return xe[o]=s,e.event.on(b.destroy,function(){delete xe[o],u[o]&&(clearTimeout(u[o]),delete u[o])}),function(t){return new Promise(function(e){if(i<=0)return e(t);e(t.filter(function(e){var t,n=e.level,r=e.msg;return!(n&&r&&fe(e))||(e=n+"_"+(null==r?void 0:r.slice(0,200).replace(/[\r\n]+/g,"")),(n=(r=null!=(n=s[e])?n:0)<i)?s[e]=r+1:u[o]||(u[t=o]||(u[t]=setTimeout(function(){xe[t]={},u[t]=null},6e4)),u[t]),n)}))})}}},Me=((i=n=n||{})[i.ERROR=0]="ERROR",i[i.WARN=1]="WARN",i[i.INFO=2]="INFO",i[i.DEBUG=3]="DEBUG",He.prototype.debug=function(e){this.log(n.DEBUG,e)},He.prototype.info=function(e){this.log(n.INFO,e)},He.prototype.warn=function(e){this.log(n.WARN,e)},He.prototype.error=function(e){this.log(n.ERROR,e)},He.prototype.log=function(e,t){e<=this.logLevel&&console.log("["+n[e]+"] "+t)},He),i=(l.prototype.init=function(){var t=this;this.logger=new Me(this.config.env===H.PROD?n.ERROR:n.DEBUG),this.pluginController=new Ee(this,this.config.plugin),l.installedPlugins.forEach(function(e){t.pluginController.patch(e)}),this.event.emit(b.init)},l.prototype.setConfig=function(e){var t;return J(this.config,e),this.tempContext.isInTempContext?(t=G({},this.tempContext.originBean),t=this.getNewBean(e,t),this.tempContext.originBean=t):this.extendBean(this.config),this.event.emit(b.configChange,e),f(this.config.onConfigChange,this),this.config},l.prototype.setTempConfig=function(t,n){return K(this,void 0,void 0,function(){return X(this,function(e){switch(e.label){case 0:this.sendNow=!0,this.tempContext.isInTempContext=!0,this.tempContext.originBean=G({},this.bean),this.extendBean(t),e.label=1;case 1:return e.trys.push([1,3,,4]),[4,n()];case 2:case 3:return e.sent(),[3,4];case 4:return this.tempContext.isInTempContext=!1,this.sendNow=!1,this.extendBean(this.tempContext.originBean),this.send(this.tempContext.catchLogs.splice(0,this.tempContext.catchLogs.length)),[2]}})})},l.use=function(e){-1===l.installedPlugins.indexOf(e)&&l.installedPlugins.push(e)},l.unuse=function(e){e=l.installedPlugins.indexOf(e);-1!==e&&l.installedPlugins.splice(e,1)},l.prototype.sendCustomLogs=function(i,o,s,u){return void 0===o&&(o=V.INFO),void 0===s&&(s=z.NORMAL),void 0===u&&(u=!1),K(this,void 0,void 0,function(){var t,n,r=this;return X(this,function(e){switch(e.label){case 0:return this.canUseCustomPlugin()?[4,f(this.config.onBeforeCollect,this)]:[2];case 1:return e.sent(),a={logLevel:o,logType:s,canCustom:u},t=i.map(function(e){var t=a.canCustom,n=a.logLevel,r=a.logType,i="object"==typeof e?e:{msg:e},o=i.msg||"",s=void 0;return n===V.CUSTOM_ERROR&&(s=o),i instanceof Error&&(o=c(i),s=i.message),G(G({},i),{msg:o,errorMsg:s,level:t?e.level:n,type:t?e.type:r,plugin:v.CUSTOM})}).map(function(e){var t;return G(G({},e),{aegisv2_goto:y.generateSpanId(),timestamp:Date.now(),snapshootInfo:ue(G(G({},r.snapshootInfo),{type:null!=(t=e.type)?t:z.NORMAL,level:null!=(t=e.level)?t:V.INFO,plugin:v.CUSTOM}))})}),[4,f(this.config.onCollected,this,t)];case 2:return 0<(t=e.sent()).length?this.tempContext.isInTempContext?[4,this.immediatelySend(t)]:[3,4]:[3,7];case 3:return n=e.sent(),[3,6];case 4:return[4,this.send(t)];case 5:n=e.sent(),e.label=6;case 6:return[2,n];case 7:return[2]}var a})})},l.prototype.info=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return K(this,void 0,void 0,function(){return X(this,function(e){switch(e.label){case 0:return[4,this.sendCustomLogs(t,V.INFO)];case 1:return e.sent(),[2]}})})},l.prototype.report=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return K(this,void 0,void 0,function(){return X(this,function(e){switch(e.label){case 0:return[4,this.sendCustomLogs(t,V.REPORT,z.NORMAL,!0)];case 1:return e.sent(),[2]}})})},l.prototype.error=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return K(this,void 0,void 0,function(){return X(this,function(e){switch(e.label){case 0:return[4,this.sendCustomLogs(t,V.CUSTOM_ERROR)];case 1:return e.sent(),[2]}})})},l.prototype.clearPluginCache=function(){this.pluginController.plugins.forEach(function(e){var t;null!=(t=null==(t=e.option)?void 0:t.clear)&&t.call(e.option,e)})},l.prototype.clearThrottleCache=function(){this.sendNow=!0,this.send([]),this.sendNow=!1},l.prototype.reportEvent=function(n){return K(this,void 0,void 0,function(){var t;return X(this,function(e){switch(e.label){case 0:return n?(t="string"==typeof n?{name:n,msg:"report_event",level:V.INFO,plugin:v.CUSTOM}:n).name?("string"!=typeof t.name&&(this.logger.warn("reportEvent params name must be string"),t.name=String(t.name)),[4,this.sendCustomLogs([t],V.INFO,z.CUSTOM_EVENT)]):(this.logger.warn("reportEvent params error"),[2]):[2];case 1:return e.sent(),[2]}})})},l.prototype.canUseCustomPlugin=function(){var e;return!1!==(null==(e=this.config.plugin)?void 0:e[v.CUSTOM])},l.prototype.reportTime=function(t,n){return K(this,void 0,void 0,function(){return X(this,function(e){switch(e.label){case 0:return"object"!=typeof t?[3,2]:[4,this.reportT(t)];case 1:return[2,e.sent()];case 2:return"string"!=typeof t?(this.logger.warn("reportTime: first param must be a string"),[2]):"number"!=typeof n?(this.logger.warn("reportTime: second param must be number"),[2]):n<0||6e4<n?(this.logger.warn("reportTime: duration must between 0 and 60000"),[2]):[4,this.submitCustomTime(t,n)];case 3:return e.sent(),[2]}})})},l.prototype.reportT=function(i){return K(this,void 0,void 0,function(){var t,n,r;return X(this,function(e){switch(e.label){case 0:return t=i.name,n=i.duration,r=x(i,["name","duration"]),"string"!=typeof t||"number"!=typeof n?(this.logger.warn("reportTime: params error"),[2]):n<0||6e4<n?(this.logger.warn("reportTime: duration must between 0 and 60000"),[2]):[4,this.submitCustomTime(t,n,r)];case 1:return[2,e.sent()]}})})},l.prototype.time=function(e,t){"string"==typeof e?this.timeMap[e]?this.logger.warn("Timer "+e+" already exists"):this.timeMap[e]={startTime:Date.now(),customParams:t}:this.logger.warn("time: first param must be a string")},l.prototype.timeEnd=function(t,n){return K(this,void 0,void 0,function(){return X(this,function(e){switch(e.label){case 0:return"string"!=typeof t?(this.logger.warn("timeEnd: first param must be a string"),[2]):this.timeMap[t]?[4,this.submitCustomTime(t,Date.now()-this.timeMap[t].startTime,G(G({},this.timeMap[t].customParams),n))]:[3,2];case 1:return e.sent(),delete this.timeMap[t],[3,3];case 2:this.logger.warn("Timer "+t+" does not exist"),e.label=3;case 3:return[2]}})})},l.prototype.submitCustomTime=function(t,n,r){return K(this,void 0,void 0,function(){return X(this,function(e){switch(e.label){case 0:return[4,this.sendCustomLogs([G({name:t,duration:n,msg:"custom_time",plugin:v.CUSTOM},r)],V.INFO,z.CUSTOM_TIME)];case 1:return e.sent(),[2]}})})},l.prototype.updateSnapshootInfo=function(e){J(this.snapshootInfo,e)},l.prototype.extendBean=function(e){e=this.getNewBean(e,this.bean);J(this.bean,e)},l.prototype.generateRequestOptionsByLogs=function(e){var t,n,r=this,e=(e=e.map(function(e){var t=e.snapshootInfo;return{message:ue(G(G(G({},e),B),{timestamp:e.timestamp+r.serverTimeGap})),fields:t}}),n={},e.forEach(function(e){var t=e.fields;n[t]||(n[t]={fields:e.fields,message:[]}),n[t].message.push(e.message)}),Object.keys(n).map(function(e){return n[e]}));return{payload:{topic:this.bean.id||"",bean:G(G({},this.bean),{id:void 0}),ext:ue((null==(t=this.config)?void 0:t.extField)||{}),scheme:"v2",d2:e,v:"2.5.42"},url:k(this.config,"hostUrl.url"),method:"POST"}},l.prototype.send=function(e,i,o){var t,r,s,a,u=this;if(!this.tempContext.isInTempContext)return this.sendPipeline||(this.sendPipeline=Te([(s=[],(r=this).event.on(b.destroy,function(){s.length=0}),a=!0,function(n){return new Promise(function(e){var t;!a||null!=(t=null==r?void 0:r.canProceedLogs)&&t.call(r)?(0<s.length&&(n.push.apply(n,s),s.length=0),a=!1,e(n)):(s=s.concat(n),e([]))})}),Pe.create(this,400),Ie.create(this),De.create(this),function(r){return K(u,void 0,void 0,function(){var t,n;return X(this,function(e){switch(e.label){case 0:return n=(t=Promise).resolve,[4,f(this.config.onBeforeSend,this,r)];case 1:return[2,n.apply(t,[e.sent()])]}})})},function(r){return new Promise(function(n){if(0===r.length)return n([]);u.sendLogs(r,function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];u.failRequestCount=0,n(D(r,[{isErr:!1,result:e}])),null!=i&&i.apply(void 0,e)},function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];60<=++u.failRequestCount&&u.destroy(),n(D(r,[{isErr:!0,result:e}])),null!=o&&o.apply(void 0,e)})})},function(r){return K(u,void 0,void 0,function(){var t,n;return X(this,function(e){switch(e.label){case 0:return n=(t=Promise).resolve,[4,f(this.config.onSended,this,r)];case 1:return[2,n.apply(t,[e.sent()])]}})})}])),this.sendPipeline(e);(t=this.tempContext.catchLogs).push.apply(t,e)},l.prototype.immediatelySend=function(e,i,o){var s=this;return Te([function(r){return K(s,void 0,void 0,function(){var t,n;return X(this,function(e){switch(e.label){case 0:return n=(t=Promise).resolve,[4,f(this.config.onBeforeSend,this,r)];case 1:return[2,n.apply(t,[e.sent()])]}})})},function(r){return new Promise(function(n){if(0===r.length)return n([]);s.sendLogs(r,function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];s.failRequestCount=0,n(D(r,[{isErr:!1,result:e}])),null!=i&&i.apply(void 0,e)},function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];60<=++s.failRequestCount&&s.destroy(),n(D(r,[{isErr:!0,result:e}])),null!=o&&o.apply(void 0,e)})})},function(r){return K(s,void 0,void 0,function(){var t,n;return X(this,function(e){switch(e.label){case 0:return n=(t=Promise).resolve,[4,f(this.config.onSended,this,r)];case 1:return[2,n.apply(t,[e.sent()])]}})})}])(e)},l.prototype.performRequest=function(e,t,n){throw new Error('You need to override "performRequest" method')},l.prototype.ready=function(){function o(){var e,t,n,r,i;s.reportRequestQueue.length&&(t=(e=s.reportRequestQueue.shift()).logs,n=e.success,r=e.fail,i=s.generateRequestOptionsByLogs(t),s.request(i,function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];try{return null==n?void 0:n.apply(i,e)}finally{o()}},function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];try{return null==r?void 0:r.apply(i,e)}finally{o()}}))}var s=this;o(),this.isReportReady=!0},l.prototype.sendLogs=function(e,t,n){this.config.reportImmediately||this.isReportReady?this.request(this.generateRequestOptionsByLogs(e),t,n):this.reportRequestQueue.push({logs:e,success:t,fail:n})},l.prototype.request=function(e,t,n){return e&&this.bean.id?e.url?void this.performRequest(e,t,n):this.logger.error("request url is empty"):this.logger.error("Invalid parameters or missing project ID, request forbidden")},l.prototype.sendSDKError=function(n){return K(this,void 0,void 0,function(){var t=this;return X(this,function(e){switch(e.label){case 0:return[4,this.setTempConfig({id:"SDK-88b1f242f91b60885f0c"},function(){return t.sendCustomLogs([G(G({},n),{errorToken:t.config.id,aegisSDKVersion:"2.5.42",errorConfig:ue(t.config)})],V.SDK_ERROR,z.SDK_ERROR)})];case 1:return e.sent(),[2]}})})},l.prototype.destroy=function(a){return void 0===a&&(a=!1),K(this,void 0,void 0,function(){var r,i,o,s;return X(this,function(e){switch(e.label){case 0:return[4,f(this.config.onBeforeDestroy,this)];case 1:e.sent(),-1!==(r=l.instances.indexOf(this))&&l.instances.splice(r,1),r=l.installedPlugins.length-1,e.label=2;case 2:if(!(0<=r))return[3,7];e.label=3;case 3:return e.trys.push([3,4,,6]),i=l.installedPlugins[r],this.pluginController.unPatch(i),[3,6];case 4:return i=e.sent(),[4,this.sendSDKError({msg:c(i)})];case 5:return e.sent(),[3,6];case 6:return r--,[3,2];case 7:if(this.event.emit("destroy"),this.event.clear(),a)t=this,n=Object.getOwnPropertyDescriptors(t),Object.keys(n).forEach(function(e){n[e].writable&&(t[e]=null)}),Object.setPrototypeOf(this,null);else{for(o=this;o.constructor!==Object&&Oe(o,this),o=Object.getPrototypeOf(o););0===l.instances.length&&(s=Object.getPrototypeOf(this).constructor,Oe(s),Oe(l))}return f(this.config.onDestroyed,this),[2]}var t,n})})},l.prototype.canProceedLogs=function(){return!0},l.version="2.5.42",l.instances=[],l.logLevel=V,l.environment=H,l.installedPlugins=[],l),o=(qe.prototype.getConfig=function(){var e;return null!=(e=null==(e=this.aegis.config)?void 0:e[this.name])?e:null==(e=this.aegis.config)?void 0:e[this.name]},qe.prototype.publish=function(u,c){var l;return K(this,void 0,void 0,function(){var i,o,t,n,r,s,a=this;return X(this,function(e){switch(e.label){case 0:return i=c||this.aegis,o=Date.now(),i.pluginController.canUse(this)?(t=(Array.isArray(u)?u:[u]).map(function(e){var t=a.name===v.API||a.name===v.PAGE_PERFORMANCE,n=(null==i?void 0:i.snapshootInfo).action,r=G({},i.snapshootInfo);return t&&n&&(t=o-(null!=(t=null==e?void 0:e.duration)?t:0),(null==n?void 0:n.timestamp)>t)&&delete r.action,G(G({},e),{plugin:a.name,aegisv2_goto:y.generateSpanId(),type:null!=(n=e.type)?n:z.NORMAL,level:null!=(t=e.level)?t:V.INFO,timestamp:o,snapshootInfo:ue(G(G({},r),{type:null!=(n=e.type)?n:z.NORMAL,level:null!=(t=e.level)?t:V.INFO,plugin:a.name}))})}),[4,f(i.config.onCollected,i,t)]):[2,!1];case 1:return 0===(t=e.sent()).length?[2,!1]:((n=t.filter(fe)).length&&i.event.emit(b.errorOccurred,n),null!=(l=this.option.pipes)&&l.length?(this.pipeline||(n=this.option.pipes.map(function(e){return a.wrapPipe(e,i)}),s=function(r){return K(a,void 0,void 0,function(){var t,n;return X(this,function(e){switch(e.label){case 0:return n=(t=Promise).resolve,[4,f(i.config.onBeforeProcess,i,r)];case 1:return[2,n.apply(t,[e.sent()])]}})})},r=function(r){return K(a,void 0,void 0,function(){var t,n;return X(this,function(e){switch(e.label){case 0:return n=(t=Promise).resolve,[4,f(i.config.onProcessed,i,r)];case 1:return[2,n.apply(t,[e.sent()])]}})})},n.unshift(s),n.push(r),s=function(t){return new Promise(function(e){i.send(t),e(t)})},n.push(s),this.pipeline=Te(n)),this.pipeline(t)):i.send(t),[2,!0])}})})},qe.prototype.wrapPipe=function(t,e){var n,r,i,o;return"string"==typeof t?null!=(i=null==(i=e.pipes.find(function(e){return e.name===t}))?void 0:i.create(e))?i:Ce:(i=null!=(i=t)?i:{},n=i.name,r=i.option,i=i.create,"string"!=typeof n?t:(o=e.pipes.find(function(e){return e.name===n}),null!=(i=null!=(i=null==i?void 0:i(e,r))?i:null==o?void 0:o.create(e,r))?i:Ce))},qe);function qe(e){var t;this.option=e,this.name=e.name,this.init=null==(t=e.init)?void 0:t.bind(this),this.onNewAegis=null==(t=e.onNewAegis)?void 0:t.bind(this),this.destroy=null==(t=e.destroy)?void 0:t.bind(this),this.option.$getConfig=this.getConfig.bind(this),this.option.publish=this.publish.bind(this)}function l(e){var t;this.pipes=[Ie,Pe,Le,Ae,_e,De],this.fetchSampleStatus=d.hasNotFetched,this.config={id:"",uid:"",delay:1e3,repeat:60,sample:1,env:"production",maxLength:102400,whiteList:!0,hostUrl:{url:"https://galileotelemetry.tencent.com/collect",whiteListUrl:"https://galileotelemetry.tencent.com/aegiscontrol/whitelist"},plugin:{pv:!0,aid:!0,error:!0,device:!0,close:!0,pagePerformance:!0,webVitals:!0,custom:!0,fId:!1,ie:!1,spa:!1,api:!1,assetSpeed:!1,session:!1,websocket:!1,blankScreen:!1},reportImmediately:!0,forceReportErrorLog:!1},this.event=new Q,this.bean={},this.snapshootInfo={},this.serverTimeGap=0,this.sampleMap={},this.isWhiteList=!1,this.tempContext={isInTempContext:!1,catchLogs:[],originBean:{}},this.isReportReady=!1,this.reportRequestQueue=[],this.timeMap={},this.failRequestCount=0,this.getNewBean=function(e,t){var n;return{id:null!=(n=e.id)?n:t.id,uid:null!=(n=e.uid)?n:t.uid,version:null!=(n=e.version)?n:t.version,aid:null!=(n=e.aid)?n:t.aid,env:null!=(n=e.env)?n:t.env,platform:null!=(n=e.platform)?n:t.platform,netType:null!=(n=e.netType)?n:t.netType,vp:null!=(n=e.vp)?n:t.vp,sr:null!=(n=e.sr)?n:t.sr,sessionId:null!=(n=e.sessionId)?n:t.sessionId,from:null!=(n=e.from)?n:t.from,referer:null!=(n=e.referer)?n:t.referer}},e.hostUrl=void 0===(t=e.hostUrl)?{}:"string"==typeof t?{url:t}:t,this.setConfig(e),l.instances.push(this),f(this.config.onNewAegis,this)}function He(e){void 0===e&&(e=n.INFO),this.logLevel=e}function Be(e){for(var t=1;t<arguments.length;t++){var n,r=arguments[t];for(n in r)e[n]=r[n]}return e}function Fe(t,n){function r(e){"pagehide"!==e.type&&"hidden"!==document.visibilityState||(t(e),n&&(removeEventListener("visibilitychange",r,!0),removeEventListener("pagehide",r,!0)))}addEventListener("visibilitychange",r,!0),addEventListener("pagehide",r,!0)}function je(){var e;return!(null==(e=window.external)||!e.assetsGC)}function Ue(e){var t=(e=e||{}).name,n=e.initiatorType;if(Xe.test(t))return"js";if(Ve.test(t))return"css";switch(n){case"script":return"js";case"link":return"css";default:return n}}function We(e){var t=e.domainLookupStart,n=e.domainLookupEnd,r=e.fetchStart,i=e.connectEnd,o=e.connectStart,s=e.requestStart,a=e.secureConnectionStart,u=e.responseStart,c=e.responseEnd,e=e.duration,l=0;return{preHandleTime:l=0!==t?Number((t-r).toFixed(2)):l,duration:Number(e.toFixed(2)),domainLookup:Number((n-t).toFixed(2)),connectTime:Number((i-o).toFixed(2)),tlsTime:Number((s-a).toFixed(2)),tcpAndRequestGap:Number((s-i).toFixed(2)),requestTime:Number((u-s).toFixed(2)),responseTime:Number((u?c-u:e).toFixed(2))}}var Ge,w=function t(a,o){function n(e,t,n){if("undefined"!=typeof document){"number"==typeof(n=Be({},o,n)).expires&&(n.expires=new Date(Date.now()+864e5*n.expires)),n.expires&&(n.expires=n.expires.toUTCString()),e=encodeURIComponent(e).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var r,i="";for(r in n)n[r]&&(i+="; "+r,!0!==n[r])&&(i+="="+n[r].split(";")[0]);return document.cookie=e+"="+a.write(t,e)+i}}return Object.create({set:n,get:function(e){if("undefined"!=typeof document&&(!arguments.length||e)){for(var t=document.cookie?document.cookie.split("; "):[],n={},r=0;r<t.length;r++){var i=t[r].split("="),o=i.slice(1).join("=");try{var s=decodeURIComponent(i[0]);if(n[s]=a.read(o,s),e===s)break}catch(e){}}return e?n[e]:n}},remove:function(e,t){n(e,"",Be({},t,{expires:-1}))},withAttributes:function(e){return t(this.converter,Be({},this.attributes,e))},withConverter:function(e){return t(Be({},this.converter,e),this.attributes)}},{attributes:{value:Object.freeze(o)},converter:{value:Object.freeze(a)}})}({read:function(e){return(e='"'===e[0]?e.slice(1,-1):e).replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(e){return encodeURIComponent(e).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}},{path:"/"}),Ke=new o({name:v.AID,aid:"",init:function(r){var i;return K(this,void 0,void 0,function(){var n;return X(this,function(e){switch(e.label){case 0:return e.trys.push([0,1,,3]),n=null==(i=r.config.plugin)?void 0:i.aid,(n="string"==typeof n?n:window.localStorage?window.localStorage.getItem("AEGIS_ID"):w.get("AEGIS_ID"))||(t=n=M(),window.localStorage?window.localStorage.setItem("AEGIS_ID",t):w.set("AEGIS_ID",t)),this.aid=n,[3,3];case 1:return n=e.sent(),[4,r.sendSDKError({msg:c(n)})];case 2:return e.sent(),[3,3];case 3:return[2]}var t})})},onNewAegis:function(e){e.bean.aid=this.aid,e.config.aid=this.aid}}),Xe=/\.js(\?|$)/i,Ve=/\.css(\?|$)/i,ze=function(){return void 0!==window.performance&&"function"==typeof performance.getEntriesByType&&"function"==typeof performance.now},Je=(new o({name:v.ASSET_SPEED}),new o({name:v.ASSET_SPEED,pipes:["repeatLimit","networkRefresh"],collectCur:0,collectEntryType:"resource",ASSETS_INITIATOR_TYPE:["img","css","script","link","audio","video"],observer:null,onNewAegis:function(e){var t=this;ze()&&(this.collectSuccessLog(e),this.collectFailLog(e),performance.onresourcetimingbufferfull=function(){"function"==typeof performance.clearResourceTimings&&(t.collectCur=0,performance.clearResourceTimings())})},publishMany:function(e,t){for(var n=t.config,r=0,i=e.length;r<i;r++){var o=e[r];-1===this.ASSETS_INITIATOR_TYPE.indexOf(o.initiatorType)||ce(o.name,k(n,"hostUrl"))||this.publish(this.generateLog(o,n),t)}},collectSuccessLog:function(n){var e,r=this;"function"==typeof(null===window||void 0===window?void 0:window.PerformanceObserver)?(this.publishMany(performance.getEntriesByType(this.collectEntryType),n),this.observer=new window.PerformanceObserver(function(e){r.publishMany(e.getEntries(),n)}),this.observer.observe({entryTypes:[this.collectEntryType]}),n.event.on(b.destroy,function(){var e;null!=(e=r.observer)&&e.disconnect()})):(e=setInterval(function(){var e=performance.getEntriesByType(r.collectEntryType),t=e.slice(r.collectCur);r.collectCur=e.length,r.publishMany(t,n)},3e3),n.event.on(b.destroy,function(){clearInterval(e)}))},collectFailLog:function(i){function e(e){var t,n,r;e&&(e=e.target||e.srcElement,!(t=(null==e?void 0:e.src)||(null==e?void 0:e.href))||"string"!=typeof t||-1<window.location.href.indexOf(t)||(e=performance.getEntriesByType(o.collectEntryType).find(function(e){return e.name===t}),n="function"==typeof(n=(null==(n=s.plugin)?void 0:n.api).resourceTypeHandler)?n(t):Ue(e),ce(t,k(s,"hostUrl")))||(r="function"==typeof(r=(null==(r=s.plugin)?void 0:r.assetSpeed).urlHandler)?r(t):t,r={msg:"static_fail",errorMsg:"load "+ie(t)+" fail",url:ie(r),status:400,duration:Number(((null==e?void 0:e.duration)||0).toFixed(2)),method:"get",assetType:n||"static",isHttps:oe(t),urlQuery:A(t,!0),nextHopProtocol:"",domainLookup:a.number,connectTime:a.number,tlsTime:a.number,requestTime:a.number,responseTime:a.number,preHandleTime:a.number,tcpAndRequestGap:a.number,type:z.ASSETS_SPEED},o.publish(r,i)))}var o=this,s=i.config;window.document.addEventListener("error",e,!0),i.event.on(b.destroy,function(){window.document.removeEventListener("error",e,!0)})},generateLog:function(e,t){var n=(null==(n=t.plugin)?void 0:n.api).resourceTypeHandler,t=(null==(t=t.plugin)?void 0:t.assetSpeed).urlHandler,t="function"==typeof t?t(e.name):e.name,n="function"==typeof n?n(e.name):Ue(e);return G({msg:"asset_speed",url:ie(t),status:200,assetType:n||"static",isHttps:oe(e.name),nextHopProtocol:e.nextHopProtocol||"",urlQuery:A(e.name,!0),transferSize:0<e.transferSize?e.transferSize:-1,method:"get",type:z.ASSETS_SPEED},We(e))},collectNotReportedLog:function(e){var t,n;ze()&&(t=(n=performance.getEntriesByType(this.collectEntryType)).length,"function"!=typeof window.PerformanceObserver)&&this.collectCur!==t&&(n=n.slice(this.collectCur),this.collectCur=t,this.publishMany(n,e))},destroy:function(){var e;this.publish=function(){},null!=(e=this.observer)&&e.disconnect()}}));(wt=Ge=Ge||{}).unknown="unknown",wt.normal="normal",wt.weak="weak",wt.disconnected="disconnected";function Qe(t){window.WebSocket=Et;var e=g.findIndex(function(e){return e.key===t.key});-1!==e&&g.splice(e,1)}function Ye(e){var t,n,r;window.Proxy&&window.WebSocket&&(t=window.WebSocket,window&&!t.isHack&&(n=new Proxy(WebSocket,St),t.isHack=!0,window.WebSocket=n),r=e,g.find(function(e){return e.key===r.key})||r&&g.push(r))}function s(e,t,n,r){return void 0===n&&(n=15e3),void 0===r&&(r=0),(t=void 0===t?0:t)<=e&&e<=n?e:r}function p(e,t){var n=Pt(),r="navigate";return 0<=Ct?r="back-forward-cache":n&&(document.prerendering||0<Lt()?r="prerender":document.wasDiscarded?r="restore":n.type&&(r=n.type.replace(/_/g,"-"))),{name:e,value:void 0===t?-1:t,rating:"good",delta:0,entries:[],id:"v4-".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:r}}function h(t,n,r,i){var o,s;return function(e){0<=n.value&&(e||i)&&((s=n.value-(o||0))||void 0===o)&&(o=n.value,n.delta=s,n.rating=(e=n.value)>r[1]?"poor":e>r[0]?"needs-improvement":"good",t(n))}}function $e(e){requestAnimationFrame(function(){return requestAnimationFrame(function(){return e()})})}function Ze(e){document.addEventListener("visibilitychange",function(){"hidden"===document.visibilityState&&e()})}function et(e){var t=!1;return function(){t||(e(),t=!0)}}function tt(){return kt<0&&(kt=Nt(),xt(),It(function(){setTimeout(function(){kt=Nt(),xt()},0)})),{get firstHiddenTime(){return kt}}}function nt(e){document.prerendering?addEventListener("prerenderingchange",function(){return e()},!0):e()}function rt(o,s){s=s||{},nt(function(){var t,n=tt(),r=p("FCP"),i=At("paint",function(e){e.forEach(function(e){"first-contentful-paint"===e.name&&(i.disconnect(),e.startTime<n.firstHiddenTime)&&(r.value=Math.max(e.startTime-Lt(),0),r.entries.push(e),t(!0))})});i&&(t=h(o,r,Dt,s.reportAllChanges),It(function(e){r=p("FCP"),t=h(o,r,Dt,s.reportAllChanges),$e(function(){r.value=performance.now()-e.timeStamp,t(!0)})}))})}function it(){return pt?qt:performance.interactionCount||0}function ot(t){var e,n;Wt.forEach(function(e){return e(t)}),!t.interactionId&&"first-input"!==t.entryType||(n=S[S.length-1],((e=jt.get(t.interactionId))||S.length<10||t.duration>n.latency)&&(e?t.duration>e.latency?(e.entries=[t],e.latency=t.duration):t.duration===e.latency&&t.startTime===e.entries[0].startTime&&e.entries.push(t):(n={id:t.interactionId,latency:t.duration,entries:[t]},jt.set(n.id,n),S.push(n)),S.sort(function(e,t){return t.latency-e.latency}),10<S.length)&&S.splice(10).forEach(function(e){return jt.delete(e.id)}))}function st(e){var t=self.requestIdleCallback||self.setTimeout,n=-1;return e=et(e),"hidden"===document.visibilityState?e():(n=t(e),Ze(e)),n}function at(o,s){"PerformanceEventTiming"in self&&"interactionId"in PerformanceEventTiming.prototype&&(s=s||{},nt(function(){var e;"interactionCount"in performance||(pt=pt||At("event",Ft,{type:"event",buffered:!0,durationThreshold:0}));function t(t){st(function(){t.forEach(ot);e=Math.min(S.length-1,Math.floor((it()-Ut)/50));var e=S[e];e&&e.latency!==n.value&&(n.value=e.latency,n.entries=e.entries,i())})}var n=p("INP"),r=At("event",t,{durationThreshold:null!=(e=s.durationThreshold)?e:40}),i=h(o,n,Gt,s.reportAllChanges);r&&(r.observe({type:"first-input",buffered:!0}),Ze(function(){t(r.takeRecords()),i(!0)}),It(function(){Ut=it(),S.length=0,jt.clear(),n=p("INP"),i=h(o,n,Gt,s.reportAllChanges)}))}))}function ut(){var t;0<=ht&&ht<mt-zt&&(t={entryType:"first-input",name:m.type,target:m.target,cancelable:m.cancelable,startTime:m.timeStamp,processingStart:m.timeStamp+ht},gt.forEach(function(e){e(t)}),gt=[])}function ct(e){var t,n,r,i;function o(){Jt(n,r),i()}function s(){i()}e.cancelable&&(t=(1e12<e.timeStamp?new Date:performance.now())-e.timeStamp,"pointerdown"==e.type?(n=t,r=e,i=function(){removeEventListener("pointerup",o,Vt),removeEventListener("pointercancel",s,Vt)},addEventListener("pointerup",o,Vt),addEventListener("pointercancel",s,Vt)):Jt(t,e))}function lt(t){["mousedown","keydown","touchstart","pointerdown"].forEach(function(e){return t(e,ct,Vt)})}function ft(e){var t=e.name;0<(e=e.value)&&(Yt[t]=e)}var dt,pt,m,ht,mt,gt,vt,yt,wt=new o({name:v.DEVICE,onNewAegis:function(e){e.extendBean({platform:this.getPlatform(),netType:r.unknown}),this.getDpi(e),this.getWechatVersion(e),this.refreshNetworkTypeToBean(e),this.refreshNetworkStatusToBean(e)},getDpi:function(e){e.extendBean({vp:Math.round(window.innerWidth)+" * "+Math.round(window.innerHeight)}),window.screen&&e.extendBean({sr:Math.round(window.screen.width)+" * "+Math.round(window.screen.height)})},getPlatform:function(){var t={android:/\bAndroid\s*([^;]+)/,ios:/\b(iPad|iPhone|iPod)\b.*? OS ([\d_]+)/,windows:/\b(Windows NT|Windows)/,macos:/\b(Mac OS|OSX)/,linux:/\b(Linux)/i},e=Object.keys(t).find(function(e){return t[e].test(navigator.userAgent)});return e?q[e]:q.other},getWechatVersion:function(e){var t=navigator.userAgent,t=/Micromessenger/i.test(t)?(null==(t=t.match(/MicroMessenger\/([\d.]+)/i))?void 0:t[1])||"":void 0;e.setConfig({extField:{wechatVersion:t}})},refreshNetworkTypeToBean:function(e){var n=this,t=e.config;t&&("function"==typeof t.getNetworkType?t.getNetworkType:bt)(function(t){Object.keys(r).some(function(e){return r[e]===t})||(t=r.unknown),e.extendBean({netType:t}),n.NetworkRefreshTimer=setTimeout(function(){n.refreshNetworkTypeToBean(e),clearTimeout(n.NetworkRefreshTimer)},1e4)})},refreshNetworkStatusToBean:function(e){var t,n=this,r=e.config;r&&null!=(t="function"==typeof r.getNetworkStatus?r.getNetworkStatus:t)&&t(function(t){Object.keys(Ge).some(function(e){return Ge[e]===t})||(t=Ge.unknown),e.extendBean({netStatus:t}),n.NetworkStatusRefreshTimer=setTimeout(function(){n.refreshNetworkStatusToBean(e),clearTimeout(n.NetworkStatusRefreshTimer)},1e4)})}}),bt=function(e){var t="",n=navigator.userAgent.match(/NetType\/(\w+)/);n?t=n[1]:navigator.connection&&(t=navigator.connection.effectiveType||navigator.connection.type),e((n=t=t||"unknown",0<=(n=String(n).toLowerCase()).indexOf("4g")?r.net4g:0<=n.indexOf("wifi")?r.wifi:0<=n.indexOf("5g")?r.net5g:0<=n.indexOf("6g")?r.net6g:0<=n.indexOf("3g")?r.net3g:0<=n.indexOf("2g")?r.net2g:r.unknown))},Et="undefined"!=typeof window?window.WebSocket:void 0,g=[],E={},St={construct:function(e,t){var i=t[0],s=new e(i,t[1]),o=(s.originSend=s.send,Date.now()),n=!1;return s.addEventListener("open",function(){var e,r=Date.now();E[i]=null!=(e=E[i])?e:{},g.forEach(function(e){var t,n=e.sendConnectInfo,e=e.key;E[i][e]=null!=(t=E[i][e])&&t,null!=n&&n({msg:"WebSocket connection succeeded",url:i,successFlag:!0,duration:r-o,retryFlag:E[i][e]}),E[i][e]=!1}),n=!0}),s.addEventListener("error",function(e){var t=((null==e?void 0:e.currentTarget)||{}).readyState;null!=g&&g.forEach(function(e){e=e.onErr;null!=e&&e({msg:"Unable to retrieve specific error information, please check the browser console!",readyState:t,connectUrl:i})})}),s.addEventListener("close",function(r){var e;E[i]=null!=(e=E[i])?e:{},n||1e3===r.code?(n=!1,g.forEach(function(e){e=e.key;E[i][e]=!1})):(g.forEach(function(e){var t,n=e.sendConnectInfo,e=e.key;E[i][e]=null!=(t=E[i][e])&&t,null!=n&&n(G(G({},r),{msg:"WebSocket connection failed",url:i,successFlag:!1,retryFlag:E[i][e]})),E[i][e]=!0}),n=!1)}),Object.defineProperty(s,"send",{get:function(){return function(e){null!=(t=s.originSend)&&t.call(s,e);var t=s.readyState,e=WebSocket.OPEN,n=WebSocket.CLOSED,r=WebSocket.CONNECTING,i=WebSocket.CLOSING;if(t!==e){var o={readyState:t,connectUrl:s.url};switch(t){case n:g.forEach(function(e){e=e.sendErr;null!=e&&e(G({msg:"Message sending failed, the connection is closed!"},o))});break;case r:g.forEach(function(e){(0,e.sendErr)(G({msg:"Message sending failed, connecting..."},o))});break;case i:g.forEach(function(e){(0,e.sendErr)(G({msg:"Message sending failed, connection is closing!"},o))})}}}}}),s}},Rt=(new o({name:v.ERROR}),new o({name:v.ERROR,onNewAegis:function(e){this.startListen(e)},startListen:function(o){function e(e){var t,n=e&&c(e.reason);n&&s.publishErrorLog({msg:"PROMISE_ERROR: "+n,errorMsg:null==(n=null==(t=(null==(t=e.reason)?void 0:t.message)||(null==(t=e.reason)?void 0:t.errMsg)||n)?void 0:t.slice)?void 0:n.call(t,0,150),level:V.PROMISE_ERROR,type:z.NORMAL,originEvent:e},o)}function t(e){var t;if(r=(null==e?void 0:e.target)||(null==e?void 0:e.srcElement)){var n=r.src||r.href||"",r=r.tagName,r=void 0===r?"script":r;if(!(ce(t=n,k(o.config,"hostUrl"))||-1<window.location.href.indexOf(t))){var i={msg:r+" load fail: "+n,level:V.INFO,type:z.NORMAL,originEvent:e};if(/\.js$/.test(n))i.level=V.SCRIPT_ERROR;else if(/\.css$/.test(n))i.level=V.CSS_ERROR;else switch(r.toLowerCase()){case"script":i.level=V.SCRIPT_ERROR;break;case"link":i.level=V.CSS_ERROR;break;case"img":i.level=V.IMAGE_ERROR;break;case"audio":case"video":i.level=V.MEDIA_ERROR;break;default:return}s.publishErrorLog(i,o)}}}var n,s=this,i=window.onerror;window.onerror=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n,r=c(e[0]);"string"!=typeof(n=r)||!n||j.some(function(e){return-1<n.indexOf(e)})||F.some(function(e){return-1<n.indexOf(e)})||s.publishErrorLog({msg:(r||"")+" @ ("+(c(e[1])||"")+":"+(e[2]||0)+":"+(e[3]||0)+")\n          \n"+c(e[4]||""),errorMsg:r,level:V.ERROR,type:z.NORMAL,originEvent:e[4]},o),null!=i&&i.call.apply(i,D([window],e))},window.addEventListener("unhandledrejection",e);window.document.addEventListener("error",t,!0),o.event.on(b.destroy,function(){window.document.removeEventListener("unhandledrejection",e),window.document.removeEventListener("error",t,!0)}),o.config.websocketHack&&(n={key:o.config.id+"-"+this.name,onErr:function(e){var t;null!=(t=s.publishWsErrorLog)&&t.call(s,e,o)},sendErr:function(e){var t;null!=(t=s.publishWsErrorLog)&&t.call(s,e,o)}},this.hackWebsocketConfig=n,Ye(this.hackWebsocketConfig))},publishErrorLog:function(e,t){var n=e.type,r=e.level,e=x(e,["type","level"]);this.publish(G({level:r||V.ERROR,type:n||z.NORMAL},e),t)},publishWsErrorLog:function(e,t){var n=e.connectUrl,r=e.msg,e=e.readyState;this.publishErrorLog({msg:"WEBSOCKET_ERROR: \n              connect: "+n+"\n              readyState: "+e+"\n              msg: "+r,level:V.WEBSOCKET_ERROR,type:z.NORMAL},t)},destroy:function(){this.publishErrorLog=function(){},this.hackWebsocketConfig&&Qe(this.hackWebsocketConfig)}})),Tt=(new o({name:v.PAGE_PERFORMANCE}),3),Ot=new o({name:v.PAGE_PERFORMANCE,performanceMap:{},onNewAegis:function(e){ze()&&(dt?this.publish(dt,e):this.startCalcPerformance(e))},startCalcPerformance:function(n){var r=this;try{this.getFirstScreenTiming(n,function(e){var t=performance.timing;t&&(dt={msg:"page_performance",type:z.PAGE_PERFORMANCE,dnsLookup:s(t.domainLookupEnd-t.domainLookupStart),tcp:s(t.connectEnd-t.connectStart),ssl:s(0===t.secureConnectionStart?0:t.requestStart-t.secureConnectionStart),ttfb:s(t.responseStart-t.requestStart),contentDownload:s(t.responseEnd-t.responseStart),domParse:s(t.domInteractive-t.domLoading,0,15e3,1070),resourceDownload:s(t.loadEventStart-t.domInteractive,0,15e3,1070),firstScreenTiming:s(Math.floor(e),0,15e3,15e3)},(t=n.config).extraPerformanceData&&"{}"!==JSON.stringify(t.extraPerformanceData)&&(t=(e=t.extraPerformanceData).engineInit,e=e.bundleLoad,dt=G(G({},dt),{engineInit:s(t,0,1e4),bundleLoad:s(e,0,1e4)})),r.publish(dt,n))})}catch(n){}},getFirstScreenTiming:function(a,u){var c=this;a.event.on(b.destroy,function(){g&&clearTimeout(g)});var l,f=this,n=["script","style","link","br"],d=[],p={},h=(-1<(null==(e=null==(e=window.PerformanceObserver)?void 0:e.supportedEntryTypes)?void 0:e.indexOf("paint"))&&(l=new PerformanceObserver(function(e){e.getEntries().forEach(function(e){var t;"paint"===e.entryType&&"first-contentful-paint"===e.name&&0<(t=document.querySelectorAll("[AEGIS-FIRST-SCREEN-TIMING]")).length&&a.config.id&&!c.performanceMap[a.config.id]&&(c.setFirstScreenInfo(a,e.startTime,t[0],t),null!=u&&u(e.startTime),h.disconnect(),l.disconnect(),g)&&clearTimeout(g)})})).observe({entryTypes:["paint"]}),new MutationObserver(function(e){var t={roots:[],ignores:[],rootsDomNum:[],time:performance.now()};e.forEach(function(e){null!=e&&e.addedNodes&&Array.prototype.slice.call(e.addedNodes).forEach(function(e){f.isEleInArray(e,t.ignores)?t.ignores.push(e):1===e.nodeType&&e.hasAttribute("AEGIS-FIRST-SCREEN-TIMING")?(Object.prototype.hasOwnProperty.apply(p,[t.time])||(p[t.time]=[]),p[t.time].push(e)):1===e.nodeType&&(f.hasAncestorOrSelfWithAttribute(e,"AEGIS-IGNORE-FIRST-SCREEN-TIMING")?t.ignores.push(e):-1!==n.indexOf(e.nodeName.toLocaleLowerCase())||f.isEleInArray(e,t.roots)||(t.roots.push(e),t.rootsDomNum.push(f.walkAndCount(e)||0)))})}),t.roots.length&&d.push(t)}));h.observe(document,{childList:!0,subtree:!0});var e,m=function(n){(n=void 0===n?0:n)||(r=0,(e=Object.keys(p)).length?(n=Math.max.apply(null,e),c.setFirstScreenInfo(a,n,null==(e=p[n])?void 0:e[0],p)):d.forEach(function(e){for(var t=0;t<e.roots.length;t++)e.rootsDomNum[t]>r&&f.isInFirstScreen(e.roots[t])&&(r=e.rootsDomNum[t],n=e.time,c.setFirstScreenInfo(a,n,e.roots[t]))}),d.length=0,Object.keys(p).forEach(function(e){p[e]=p[e].map(function(e){var t={tagName:e.tagName},n=e.attributes;if(!n)return e;for(var r=0;r<n.length;r++){var i=n[r];i.name&&(t[i.name]=e.getAttribute(i.name))}return t})}));var r,e=performance.timing,t=e.domInteractive-e.domLoading,e=e.loadEventStart-e.domInteractive,i=n;g=null;for(var o=0,s=[t,e,i];o<s.length;o++)if(s[o]<=0&&0<Tt){g=setTimeout(function(){return m(i)},3e3);break}g?--Tt:(h.disconnect(),null!=l&&l.disconnect(),null!=u&&u(n))},g=setTimeout(function(){return m()},3e3)},setFirstScreenInfo:function(e,t,n,r){var i;e.config.id&&this.performanceMap[e.config.id]||(e.config.id&&(this.performanceMap[e.config.id]=!0),("object"!=typeof(null==(i=e.config)?void 0:i.pagePerformance)||null!=(i=e.config.pagePerformance)&&i.firstScreenInfo)&&(e.firstScreenInfo={element:n,timing:t,markDoms:r}))},isEleInArray:function(e,t){return!(!e||e===document.documentElement)&&(-1!==t.indexOf(e)||this.isEleInArray(e.parentElement,t))},isInFirstScreen:function(e){var t,n;return!(!e||"function"!=typeof e.getBoundingClientRect)&&(e=e.getBoundingClientRect(),t=window.innerHeight,n=window.innerWidth,0<=e.left)&&e.left<n&&0<=e.top&&e.top<t&&0<e.width&&0<e.height},walkAndCount:function(e){var t=0;if(e&&1===e.nodeType){t+=1;var n=e.children;if(null!=n&&n.length)for(var r=0;r<n.length;r++)1===n[r].nodeType&&n[r].hasAttribute("AEGIS-IGNORE-FIRST-SCREEN-TIMING")||(t+=this.walkAndCount(n[r]))}return t},hasAncestorOrSelfWithAttribute:function(e,t){for(var n=e;n&&n!==document.body;){if(n.hasAttribute(t))return!0;n=n.parentElement}return!1}}),Ct=-1,It=function(t){addEventListener("pageshow",function(e){e.persisted&&(Ct=e.timeStamp,t(e))},!0)},Pt=function(){var e=self.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0];if(e&&0<e.responseStart&&e.responseStart<performance.now())return e},Lt=function(){var e=Pt();return e&&e.activationStart||0},At=function(e,t,n){try{var r;if(PerformanceObserver.supportedEntryTypes.includes(e))return(r=new PerformanceObserver(function(e){Promise.resolve().then(function(){t(e.getEntries())})})).observe(Object.assign({type:e,buffered:!0},n||{})),r}catch(e){}},kt=-1,Nt=function(){return"hidden"!==document.visibilityState||document.prerendering?1/0:0},_t=function(e){"hidden"===document.visibilityState&&-1<kt&&(kt="visibilitychange"===e.type?e.timeStamp:0,removeEventListener("visibilitychange",_t,!0),removeEventListener("prerenderingchange",_t,!0))},xt=function(){addEventListener("visibilitychange",_t,!0),addEventListener("prerenderingchange",_t,!0)},Dt=[1800,3e3],Mt=[.1,.25],qt=0,Ht=1/0,Bt=0,Ft=function(e){e.forEach(function(e){e.interactionId&&(Ht=Math.min(Ht,e.interactionId),Bt=Math.max(Bt,e.interactionId),qt=Bt?(Bt-Ht)/7+1:0)})},S=[],jt=new Map,Ut=0,Wt=[],Gt=[200,500],Kt=[2500,4e3],Xt={},Vt={passive:!0,capture:!0},zt=new Date,Jt=function(e,t){m||(m=t,ht=e,mt=new Date,lt(removeEventListener),ut())},Qt=[100,300],Yt=(new o({name:v.WEB_VITALS}),{FCP:-1,LCP:-1,FID:-1,CLS:-1,INP:-1}),$t=new o({name:v.WEB_VITALS,onNewAegis:function(e){if(ze()&&"function"==typeof window.PerformanceObserver&&"function"==typeof performance.getEntriesByName)try{rt(ft),l=ft,f={},nt(function(){function e(e){(e=f.reportAllChanges?e:e.slice(-1)).forEach(function(e){e.startTime<r.firstHiddenTime&&(i.value=Math.max(e.startTime-Lt(),0),i.entries=[e],t())})}var t,n,r=tt(),i=p("LCP"),o=At("largest-contentful-paint",e);o&&(t=h(l,i,Kt,f.reportAllChanges),n=et(function(){Xt[i.id]||(e(o.takeRecords()),o.disconnect(),Xt[i.id]=!0,t(!0))}),["keydown","click"].forEach(function(e){addEventListener(e,function(){return st(n)},{once:!0,capture:!0})}),Ze(n),It(function(e){i=p("LCP"),t=h(l,i,Kt,f.reportAllChanges),$e(function(){i.value=performance.now()-e.timeStamp,Xt[i.id]=!0,t(!0)})}))}),u=ft,c={},nt(function(){function t(e){e.startTime<n.firstHiddenTime&&(r.value=e.processingStart-e.startTime,r.entries.push(e),o(!0))}function e(e){e.forEach(t)}var n=tt(),r=p("FID"),i=At("first-input",e),o=h(u,r,Qt,c.reportAllChanges);i&&(Ze(et(function(){e(i.takeRecords()),i.disconnect()})),It(function(){r=p("FID"),o=h(u,r,Qt,c.reportAllChanges),gt=[],ht=-1,m=null,lt(addEventListener),gt.push(t),ut()}))}),s=ft,a={},rt(et(function(){function e(e){e.forEach(function(e){var t,n;e.hadRecentInput||(t=i[0],n=i[i.length-1],r&&e.startTime-n.startTime<1e3&&e.startTime-t.startTime<5e3?(r+=e.value,i.push(e)):(r=e.value,i=[e]))}),r>n.value&&(n.value=r,n.entries=i,t())}var t,n=p("CLS",0),r=0,i=[],o=At("layout-shift",e);o&&(t=h(s,n,Mt,a.reportAllChanges),Ze(function(){e(o.takeRecords()),t(!0)}),It(function(){n=p("CLS",r=0),t=h(s,n,Mt,a.reportAllChanges),$e(function(){return t()})}),setTimeout(t,0))})),at(ft),Fe(this.publishVitals.bind(this,e),!0)}catch(e){}var s,a,u,c,l,f},publishVitals:function(e){var t=this;setTimeout(function(){t.publish(G({type:z.WEB_VITALS,msg:"web_vitals"},Yt),e)},10)},destroy:function(){this.publish=function(){}}}),Zt=(new o({name:v.SPA}),new o({name:v.SPA,originFireUrl:"",onNewAegis:function(t){var e,n,r,i=this;this.sendPv=this.sendPv.bind(this),null!=(e=null==(e=t.config)?void 0:e.plugin)&&e.spa&&this.sendPv(t);try{null!=(n=Object.getOwnPropertyDescriptor(History.prototype,"pushState"))&&n.writable&&(history.pushState=this.wr("pushState")||history.pushState),null!=(r=Object.getOwnPropertyDescriptor(History.prototype,"replaceState"))&&r.writable&&(history.replaceState=this.wr("replaceState")||history.replaceState)}catch(t){}["replaceState","pushState","hashchange","popstate"].forEach(function(e){return window.addEventListener(e,function(){return i.sendPv.call(i,t)})})},wr:function(n){var r=history[n],e="__"+n+"__hasWrittenByTamSpa";return"function"==typeof r&&!history[e]&&(Object.defineProperty(history,e,{value:!0,enumerable:!1}),function(){var e=r.apply(this,arguments),t=null;return"function"==typeof Event?t=new Event(n):(t=document.createEvent("HTMLEvents")).initEvent(n,!1,!0),window.dispatchEvent(t),e})},sendPv:function(t){var n=this;setTimeout(function(){var e=location.pathname+location.hash+t.config.id;e&&e!==t.originFireUrl&&(t.updateSnapshootInfo({from:t.getCurrentPageUrl()}),t.timeOrigin=Date.now(),n.publish({msg:"spa",type:z.PV},t),t.originFireUrl=e)},0)},destroy:function(){this.sendPv=function(){}}})),en={name:"networkRefresh",create:function(r){return function(n){return new Promise(function(t){bt(function(e){r.extendBean({netType:e}),t(n)})})}}},i=(_(yt=tn,i=vt=i),yt.prototype=null===i?Object.create(i):(nn.prototype=i.prototype,new nn),tn.prototype.getCurrentPageUrl=function(){var e=this.config.pageUrl||location.href||"";return(e="function"==typeof this.config.urlHandler?this.config.urlHandler():e).slice(0,2048)},tn.prototype.performRequest=function(e,t,n){var r,i,o,s,a=e.method,u=e.url,e=e.payload;if(this.sendNow&&u===k(this.config,"hostUrl.url")&&"function"==typeof(null===navigator||void 0===navigator?void 0:navigator.sendBeacon))return(r=navigator.sendBeacon(u,ue(e)))?null==t?void 0:t(r):null==n?void 0:n(r);u=(r={url:u,payload:e,method:a,success:t,fail:n}).url,e=r.method,a=r.payload,i=r.fail,o=r.success,(s=new XMLHttpRequest).sendByAegis=!0,s.addEventListener("readystatechange",function(){4===s.readyState&&(400<=s.status||0===s.status?null!=i&&i(s.response):null!=o&&o(s.response))}),s.open(e||"post",u),je()&&s.setRequestHeader("content-type","text/plain;charset=UTF-8"),s.send(JSON.stringify(a))},tn.sessionID="session-"+Date.now(),tn);function tn(t){var e,t=vt.call(this,t)||this;t.sendNow=!1,t.originFireUrl="",t.timeOrigin=null!=(e="undefined"!=typeof performance&&(null===performance||void 0===performance?void 0:performance.timeOrigin))?e:Date.now(),t.pipes.push(en);try{t.updateSnapshootInfo({from:t.getCurrentPageUrl()}),"undefined"!=typeof document&&t.extendBean({referer:document.referrer||""}),t.init()}catch(e){t.logger.warn(e),t.sendSDKError({msg:c(e)})}return t}function nn(){this.constructor=yt}new o({name:v.IE});function R(e){return-1!==navigator.userAgent.toLowerCase().indexOf(e)}var T={},rn=(T.macos=function(){return R("mac")},T.ios=function(){return T.iphone()||T.ipod()||T.ipad()},T.iphone=function(){return!T.windows()&&R("iphone")},T.ipod=function(){return R("ipod")},T.ipad=function(){var e="MacIntel"===navigator.platform&&1<navigator.maxTouchPoints;return R("ipad")||e},T.android=function(){return!T.windows()&&R("android")},T.androidPhone=function(){return T.android()&&R("mobile")},T.androidTablet=function(){return T.android()&&!R("mobile")},T.blackberry=function(){return R("blackberry")||R("bb10")},T.blackberryPhone=function(){return T.blackberry()&&!R("tablet")},T.blackberryTablet=function(){return T.blackberry()&&R("tablet")},T.windows=function(){return R("windows")},T.windowsPhone=function(){return T.windows()&&R("phone")},T.windowsTablet=function(){return T.windows()&&R("touch")&&!T.windowsPhone()},T.fxos=function(){return(R("(mobile")||R("(tablet"))&&R(" rv:")},T.fxosPhone=function(){return T.fxos()&&R("mobile")},T.fxosTablet=function(){return T.fxos()&&R("tablet")},T.meego=function(){return R("meego")},T.cordova=function(){return window.cordova&&"file:"===location.protocol},T.nodeWebkit=function(){return"object"==typeof window.process},T.mobile=function(){return T.androidPhone()||T.iphone()||T.ipod()||T.windowsPhone()||T.blackberryPhone()||T.fxosPhone()||T.meego()},T.tablet=function(){return T.ipad()||T.androidTablet()||T.blackberryTablet()||T.windowsTablet()||T.fxosTablet()},T.desktop=function(){return!T.tablet()&&!T.mobile()},T.isIE=function(){return"ActiveXObject"in window},new o({name:v.CLOSE}),new o({name:v.CLOSE,onNewAegis:function(n){var r,i=this;T.desktop()?(r=window.onunload,window.onunload=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];i.publishNotReportedLog(n),null!=r&&r.call.apply(r,D([window],e))}):Fe(this.publishNotReportedLog.bind(this,n),!0)},publishNotReportedLog:function(e){e.clearPluginCache(),e.clearThrottleCache()},publishThrottlePipeLogs:function(){}}));new o({name:v.F_ID});function on(c,l){void 0===l&&(l=/\.flv(\?|$)/i),I.find(function(e){return e.name===c.name})||(I.push(c),!gn&&window.fetch&&(gn=!0,fn=window.fetch,window.fetch=function(e,i){void 0===i&&(i={});var o="string"==typeof(r=e)?r:r instanceof Request?r.url:r instanceof URL?r.href:"";if(null!=(r=null==l?void 0:l.test)&&r.call(l,o))return fn(o,i);mn.test(o)&&(o=""+location.origin+o);var t,n,r=(c||{}).traceRequestHeader;r&&(t=function(e,t){try{var n={};return t.headers?new Headers(t.headers).forEach(function(e,t){n[t]=e}):e instanceof Request&&e.headers.forEach(function(e,t){n[t]=e}),n}catch(e){return console.log("failed to get header: "+JSON.stringify(e)+" }"),{}}}(e,i),n=(r=r.generate(o,t)||{}).name,r=r.value)&&n&&(i.headers=Object.assign(t,((t={})[n]=r,t)));for(var s=0;s<I.length;s++){var a=I[s];try{"function"==typeof a.beforeFetch&&a.beforeFetch(o,i)}catch(e){}}var u=Date.now();return fn(e,i).then(function(e){for(var t=e.clone(),n=0;n<I.length;n++){var r=I[n];try{"function"==typeof r.then&&r.then(t,Date.now()-u,o,i)}catch(e){}}return t}).catch(function(e){for(var t=0;t<I.length;t++){var n=I[t];try{"function"==typeof n.catch&&n.catch(e,Date.now()-u,o,i)}catch(e){}}throw e})}))}function sn(r,e,i){return null!=e&&e.length&&"object"==typeof r?e.reduce(function(e,t){var n=r instanceof Headers?r.get(t):r[t];return n?e+(""===e?"\n":"\n\n")+i+" header "+t+": "+n:e},""):""}function an(c,l){return K(void 0,void 0,void 0,function(){var i,o,s,a,u;return X(this,function(e){switch(e.label){case 0:i=Date.now(),o=new Uint8Array(0),s=0,a=l,e.label=1;case 1:return[4,c.read()];case 2:return!(u=e.sent()).done&&u.value?(0===o.length&&(a+=Date.now()-i),s+=1,t=o,n=u.value,(r=new Uint8Array(t.length+n.length)).set(t),r.set(n,t.length),o=r,[3,1]):[3,3];case 3:return[2,{dataDuration:u=Date.now()-i,dataChunkCount:s,firstChunkTime:a,totalDuration:u+l,totalDataSize:o.length,connectTime:l}]}var t,n,r})})}var un,cn,ln,fn,dn,O,pn=new o({name:v.PV,init:function(e){var t;null!=(t=null==(t=e.config)?void 0:t.plugin)&&t.spa||this.publish({msg:"pv",type:z.PV},e)}}),hn=!1,C=[],mn=/^\/[^/]/,gn=!1,I=[],vn=(new o({name:v.API}),new o({name:v.API,pipes:["repeatLimit","networkRefresh"],override:!1,onNewAegis:function(e){var t,n=((null==(n=null==e?void 0:e.config)?void 0:n.plugin)||{}).api;null!=n&&n.injectTraceHeader&&(this.traceRequestHeader=new re(n.injectTraceHeader,null!=(t=null==n?void 0:n.injectTraceIgnoreUrls)?t:[],null==n?void 0:n.injectTraceUrls)),this.overrideFetch(e.config,e),this.overrideXhr(e.config,e)},getRequestType:function(e,t,n){void 0===t&&(t="");var r,e=null==(e=e.plugin)?void 0:e.api,e="function"==typeof(null==e?void 0:e.resourceTypeHandler)?null==e?void 0:e.resourceTypeHandler(n):"";return-1===W.indexOf(e)&&(r=void 0===t?"":t,t=(void 0===n?"":n).split("?")[0],t=pe.test(t),pe.lastIndex=0,e=t||de.some(function(e){return-1!==String(r).indexOf(e)})?"static":"fetch"),e},overrideFetch:function(b,E){var h=this,o=((null==b?void 0:b.plugin)||{}).api,e={name:b.id,traceRequestHeader:null!=o&&o.injectTraceHeader?this.traceRequestHeader:null,then:function(g,v,y,w){return K(h,void 0,void 0,function(){var m,t,n,r,i=this;return X(this,function(e){switch(e.label){case 0:return ce(y,k(b,"hostUrl"))?[2]:(r=g.headers?g.headers.get("content-type"):"","fetch"!==(m=this.getRequestType(b,r,y))?[3,3]:null!=r&&r.includes("text/event-stream")?[4,an(null==(r=null==(r=g.clone())?void 0:r.body)?void 0:r.getReader(),v)]:[3,2]);case 1:return r=e.sent(),t=this.getPerformanceEntryByUrl({url:y,duration:r.totalDuration}).performanceTiming,n=o.urlHandler,n="function"==typeof n?n(y,null==w?void 0:w.body):y,this.publish(G(G({url:A(n),type:z.SSE,level:V.INFO},r),{totalDuration:t.duration})),[2];case 2:return g.clone().text().then(function(r){return K(i,void 0,void 0,function(){var i,o,s,a,u,c,l,f,t,d,p,n,h=this;return X(this,function(e){switch(e.label){case 0:return i=g.status<=0||400<=g.status,o=null==(n=b.plugin)?void 0:n.api,n=(null==o?void 0:o.reqHeaders)||[],s=sn(null==w?void 0:w.headers,n,"req"),n=(null==o?void 0:o.resHeaders)||[],a=sn(g.headers,n,"res"),n=te(null==w?void 0:w.headers),u=n[0],c=n[1],[4,se(r,o,{url:y,ctx:g,payload:null==w?void 0:w.body})];case 1:return n=e.sent(),l=n.code,f=n.isErr,t=null==o?void 0:o.apiDetail,d=t?ae(null==w?void 0:w.body,null==o?void 0:o.reqParamHandler,{url:y}):"",p=t?ae(r,null==o?void 0:o.resBodyHandler,{url:y,ctx:g}):"",setTimeout(function(){var e=h.getPerformanceEntryByUrl({url:y,duration:v}),t=e.performanceTiming,e=e.nextHopProtocol,n=""+(i?"FETCH_ERROR":le(y,p,g.status,l)),r=(o||{}).urlHandler,r="function"==typeof r?r(y,null==w?void 0:w.body):y;h.publish(G(G({},t),{msg:n,errorMsg:i||f?y+","+l:void 0,url:A(r),status:g.status||0,method:(null==w?void 0:w.method)||"get",ret:l,isErr:f,param:d,data:p,reqHeaders:s,resHeaders:a,level:i?V.AJAX_ERROR:f?V.ERROR:V.INFO,type:z.API,trace:u,span:c,nextHopProtocol:e,requestType:m}),E)},0),[2]}})})}),[3,4];case 3:setTimeout(function(){var e=i.getPerformanceEntryByUrl({url:y,duration:v}),t=e.performanceTiming,e=x(e,["performanceTiming"]);i.publish(G(G(G({},t),e),{url:A(y),status:g.status,type:z.ASSETS_SPEED}),E)},0),e.label=4;case 4:return[2]}})})},catch:function(i,o,s,a){var u,c,l,e,f,d,p;throw ce(s,k(b,"hostUrl"))||(u=h.getRequestType(b,"",s),e=(null==(c=null==(e=b.plugin)?void 0:e.api)?void 0:c.reqHeaders)||[],l=sn(null==a?void 0:a.headers,e,"req"),e=te(null==a?void 0:a.headers),f=e[0],d=e[1],p=null!=c&&c.apiDetail?ae(null==a?void 0:a.body,null==c?void 0:c.reqParamHandler,{url:s}):"",setTimeout(function(){var e,t,n=h.getPerformanceEntryByUrl({url:s,duration:o}),r=n.performanceTiming,n=x(n,["performanceTiming"]);"fetch"===u?(e="FETCH_ERROR: "+i+" \nurl: "+s+"\nresdata: \nstatus: 0\nretcode: unknown\n ",t="function"==typeof(t=(c||{}).urlHandler)?t(s,null==a?void 0:a.body):s,h.publish(G(G({},r),{msg:e,errorMsg:s+",0",url:A(t),status:0,method:(null==a?void 0:a.method)||"get",param:p,reqHeaders:l,level:V.AJAX_ERROR,type:z.API,ret:"unknown",trace:f,span:d,nextHopProtocol:n.nextHopProtocol,requestType:u}),E)):h.publish(G(G(G({},r),n),{url:A(s),status:400,type:z.ASSETS_SPEED}),E)},0)),i}};this.hackFetchOptions=e,on(this.hackFetchOptions)},overrideXhr:function(I,P){var r,c=this,L=((null==I?void 0:I.plugin)||{}).api,e={traceRequestHeader:null!=L&&L.injectTraceHeader?this.traceRequestHeader:null,name:I.id,send:function(S,R){var e,t,u=Date.now(),T=(null!=L&&L.injectTraceHeader&&(e=(t=c.traceRequestHeader.generate(S.aegisUrl)||{}).name,t=t.value,e)&&t&&S.setRequestHeader(e,t),S.sseConfig={isSSE:void 0,dataChunkCount:0,sseConnectTime:0,firstChunkTime:0,totalDataSize:0},S.addEventListener("readystatechange",function(){var e=S,t=u;switch(e.readyState){case XMLHttpRequest.HEADERS_RECEIVED:var n=e.sseConfig,r=n.isSSE,n=n.sseConnectTime;(r=void 0===r?Boolean(null==(i=e.getResponseHeader("content-type"))?void 0:i.includes("text/event-stream")):r)&&(n=Date.now()-t),J(e.sseConfig,{isSSE:r,sseConnectTime:n});break;case XMLHttpRequest.LOADING:var i=e.sseConfig,o=i.dataChunkCount,s=i.firstChunkTime,a=i.totalDataSize;e.sseConfig.isSSE&&(1===(o+=1)&&(s=Date.now()-t),a=(null==(i=e.responseText)?void 0:i.length)||0,J(e.sseConfig,{dataChunkCount:o,firstChunkTime:s,totalDataSize:a}));break;case XMLHttpRequest.DONE:var i=e.sseConfig,r=i.isSSE,o=i.dataChunkCount,s=i.firstChunkTime,n=i.sseConnectTime,a=i.totalDataSize;r&&(i=Date.now(),i={totalDuration:i-t,dataDuration:i-(t+n),connectTime:n,firstChunkTime:s,dataChunkCount:o,totalDataSize:(null==(r=e.responseText)?void 0:r.length)||a},n=S.aegisUrl||"",s=c.getPerformanceEntryByUrl({url:n,duration:i.totalDuration}).performanceTiming,o=(L||{}).urlHandler,r="function"==typeof o?o(n,R):n,c.publish(G(G({url:A(r),type:z.SSE,level:V.INFO},i),{totalDuration:s.duration})))}}),S.addEventListener("loadend",function(){var y,w,b,e,E;S.sseConfig.isSSE||(y=S.aegisUrl||"",e=!(null!=L&&L.reportAbort)&&"abort"===S.failType,ce(y,k(I,"hostUrl")))||e||(w="",(S.failType||!S.status||400<=S.status)&&(w=S.failType||"failed"),b=Date.now()-u,e=S.getResponseHeader("content-type"),E=c.getRequestType(I,e,y),setTimeout(function(){return K(c,void 0,void 0,function(){var n,r,i,o,s,a,u,c,l,f,d,p,h,m,g,v;return X(this,function(e){switch(e.label){case 0:if(r=this.getPerformanceEntryByUrl({url:y,duration:b}),n=r.performanceTiming,r=x(r,["performanceTiming"]),i=S.aegisMethod||"get",o=S.status,"fetch"!==E)return[3,5];s=(null==L?void 0:L.reqHeaders)||[],s=sn(S.aegisXhrReqHeader,s,"req"),c=(null==L?void 0:L.resHeaders)||[],t=S.getAllResponseHeaders(),a=t.split("\r\n").reduce(function(e,t){t=t.split(": ");return t[0]&&t[1]&&(e[t[0]]=t[1]),e},{}),a=sn(a,c,"res"),c=te(S.aegisXhrReqHeader),u=c[0],c=c[1],f=null==L?void 0:L.apiDetail,l=f?ae(R,null==L?void 0:L.reqParamHandler,{url:y}):"",f=f?ae(S.response,null==L?void 0:L.resBodyHandler,{url:y}):"",e.label=1;case 1:return e.trys.push([1,3,,4]),[4,se(S.response,null==(v=I.plugin)?void 0:v.api,{url:y,ctx:S,payload:R})];case 2:return v=e.sent(),d=v.code,p=v.isErr,h=w?"AJAX_ERROR: request "+w:le(y,f,o,d),m=w?V.AJAX_ERROR:p?V.ERROR:V.INFO,g=(L||{}).urlHandler,g="function"==typeof g?g(y,R):y,this.publish(G(G({},n),{msg:h,errorMsg:w||p?y+","+d:void 0,url:A(g),status:o,method:i,param:l,data:f,reqHeaders:s,resHeaders:a,level:m,ret:d,isErr:p,trace:u,span:c,type:z.API,nextHopProtocol:r.nextHopProtocol,requestType:E}),P),[3,4];case 3:return e.sent(),[3,4];case 4:return[3,6];case 5:this.publish(G(G(G({},n),r),{status:o,url:A(y),type:z.ASSETS_SPEED}),P),e.label=6;case 6:return S.removeEventListener("abort",T),S.removeEventListener("error",O),S.removeEventListener("timeout",C),[2]}var t})})},0))}),function(){S.failType="abort"}),O=function(){S.failType="error"},C=function(){S.failType="timeout"};S.addEventListener("abort",T),S.addEventListener("error",O),S.addEventListener("timeout",C)}};this.hackXHROptions=e,r=this.hackXHROptions,null!=(e=Object.getOwnPropertyDescriptor(XMLHttpRequest.prototype,"open"))&&e.writable&&!C.find(function(e){return e.name===r.name})&&(C.push(r),!hn&&window.XMLHttpRequest)&&(un=window.XMLHttpRequest.prototype.send,cn=window.XMLHttpRequest.prototype.open,ln=window.XMLHttpRequest.prototype.setRequestHeader,hn=!0,window.XMLHttpRequest.prototype.open=function(){this.aegisMethod=arguments[0];var e=arguments[1];if(mn.test(e)&&(e=""+location.origin+e),this.aegisUrl=e,this.aegisXhrStartTime=Date.now(),this.sendByAegis)T.isIE()||(this.timeout=5e3);else for(var t=0;t<C.length;t++){var n=C[t];try{"function"==typeof n.open&&n.open(this)}catch(e){}}return cn.apply(this,arguments)},window.XMLHttpRequest.prototype.setRequestHeader=function(){var e,t=arguments[0],n=arguments[1];if(this.aegisXhrReqHeader=null!=(e=this.aegisXhrReqHeader)?e:{},!(null!=r&&r.traceRequestHeader&&-1<["traceparent","b3","sw8","sentry-trace"].indexOf(t)&&(this.aegisXhrReqHeader[t]||(arguments[1]=n),this.aegisXhrReqHeader[t])))return this.aegisXhrReqHeader[t]=arguments[1],ln.apply(this,arguments)},window.XMLHttpRequest.prototype.send=function(){if(!this.sendByAegis)for(var e=0;e<C.length;e++){var t=C[e];try{"function"==typeof t.send&&t.send(this,arguments[0])}catch(e){}}return un.apply(this,arguments)})},getPerformanceEntryByUrl:function(e){var t=e.url,e=e.duration;if("string"==typeof t&&"function"==typeof performance.getEntriesByName){var n=null==(n=performance.getEntriesByName(t))?void 0:n.pop();if(n)return{msg:"api",type:z.API,isHttps:oe(t),nextHopProtocol:n.nextHopProtocol||"",urlQuery:A(t,!0),transferSize:0<n.transferSize?n.transferSize:-1,performanceTiming:We(n)}}return{msg:"api",type:z.API,isHttps:oe(t),nextHopProtocol:"",urlQuery:A(t,!0),performanceTiming:{preHandleTime:a.number,duration:Number(e.toFixed(2)),domainLookup:a.number,connectTime:a.number,tlsTime:a.number,tcpAndRequestGap:a.number,requestTime:a.number,responseTime:a.number}}},destroy:function(){var t,n,e;this.publish=function(){},this.hackXHROptions&&(t=this.hackXHROptions,-1!==(e=C.findIndex(function(e){return e.name===t.name})))&&C.splice(e,1),this.hackFetchOptions&&(n=this.hackFetchOptions,-1!==(e=I.findIndex(function(e){return e.name===n.name})))&&I.splice(e,1),this.override=!1}})),yn=((Pn=dn=dn||{}).INITIAL_LOAD="initial_load",Pn.ROUTE_CHANGE="route_change",(Pn=O=O||{}).SELF_GENETATE="self_generate",Pn.SERVER_INJECT="server_inject",Pn.URL_QUERY="url_query",Pn.LOCAL_STORAGE="local_storage",Pn.COOKIES="cookies",function(){});function wn(e,t,n){var r,i,o,s,a,u=n.before,c=n.after;return s=(r=e)[i=t],o=s,a=function(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return u&&u.apply(this,t),"function"==typeof o&&(e=o.apply(this,t)),c&&c.apply(this,[e]),e},r[i]=l,{stop:function(){r[i]===l?r[i]=s:a=s}};function l(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];if("function"==typeof a)return a.apply(this,e)}}function bn(e){return function(n){for(var e=[],t=1;t<arguments.length;t++)e[t-1]=arguments[t];return e.forEach(function(e){for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(n[t]=e[t])}),n}({},e)}function En(n){return["ancestorOrigins","href","origin","protocol","host","hostname","port","pathname","search","hash","assign","reload","replace","toString"].reduce(function(e,t){return e[t]=n[t],e},{})}function Sn(){return(new Date).getTime()}function Rn(e){var t;return e?(t=null==e?void 0:e.indexOf("?"))<0?e:null==e?void 0:e.slice(0,t):""}function Tn(e,t){function n(e,t){for(var n=e,r=0;r<=10&&n&&!["BODY","HTML","HEAD"].includes(n.nodeName);){for(var i=0,o=t;i<o.length;i++){var s=(0,o[i])(n);if("string"==typeof s&&s.trim())return a(s.trim().replace(/\s+/g," "))}if("FORM"===n.nodeName)break;n=n.parentElement,r+=1}return""}function a(e){return 100<e.length?(void 0===n&&(n=""),r=55296<=(r=(t=e).charCodeAt(99))&&r<=56319?101:100,(t.length<=r?t:""+(null==t?void 0:t.slice(0,r))+n)+" [...]"):e;var t,n,r}var r,i=[function(r){var e;if(c()){if("labels"in r&&r.labels&&0<r.labels.length)return u(r.labels[0])}else if(r.id)return(e=r.ownerDocument&&function(e){for(var t=0;t<e.length;t+=1){var n=e[t];if(n.htmlFor===r.id)return n}}(r.ownerDocument.querySelectorAll("label")))&&u(e);return""},function(e){if("INPUT"===e.nodeName){var t=e.getAttribute("type");if(["button","submit","reset"].includes(t||""))return e.value}return""},function(e){return["BUTTON","LABEL"].includes(e.nodeName)||"button"===e.getAttribute("role")?u(e):""},function(e){return e.getAttribute("aria-label")},function(t){var e=t.getAttribute("aria-labelledby");return e?e.split(/\s+/).map(function(e){return s(t,e)}).filter(function(e){return Boolean(e)}).map(function(e){return u(e)}).join(" "):""},function(e){return e.getAttribute("alt")},function(e){return e.getAttribute("name")},function(e){return e.getAttribute("title")},function(e){return e.getAttribute("placeholder")},function(e){return"options"in e&&0<e.options.length?u(e.options[0]):""}],o=[function(e){return u(e)}],s=function(e,t){return e.ownerDocument?e.ownerDocument.getElementById(t):null},u=function(e){if(!e.isContentEditable)return"innerText"in e?e.innerText:e.textContent},c=function(){return r=void 0===r?"labels"in HTMLInputElement.prototype:r};return function(e,t){var n;if("closest"in HTMLElement.prototype)n=e.closest("["+t+"]");else for(var r=e;r;){if(r.hasAttribute(t)){n=r;break}r=function(e){if(e.parentElement)return e.parentElement;for(;e.parentNode;){if(e.parentNode.nodeType===Node.ELEMENT_NODE)return e.parentNode;e=e.parentNode}return null}(r)}if(n)return e=n.getAttribute(t),a(e.trim().replace(/\s+/g," "))}(e,t||"data-aegis-action-name")||n(e,i)||n(e,o)||""}function On(e){var t=e.onStart,i=e.onChange,o=je()?En:bn;function n(){var e,t,n,r;s.href!==window.location.href&&(e=o(window.location),n=e,(t=s).pathname===n.pathname&&(r=n.hash,document.getElementById(null==r?void 0:r.substring(1))||Rn(n.hash)===Rn(t.hash))||(i(s,e),s=o(e)))}t();var r,s=o(window.location),a=(r=n,window.addEventListener("hashchange",r),function(){window.removeEventListener("hashchange",r)}),u=function(e){var t,n,r=yn,i=yn;try{null!=(t=Object.getOwnPropertyDescriptor(History.prototype,"pushState"))&&t.writable&&(r=wn(history,"pushState",{after:e}).stop),null!=(n=Object.getOwnPropertyDescriptor(History.prototype,"replaceState"))&&n.writable&&(i=wn(history,"replaceState",{after:e}).stop)}catch(e){}return{stop:function(){r(),i(),window.removeEventListener("popstate",e)}}}(n).stop;return function(){a(),u()}}kn.prototype.subscribe=function(t){var e=this;return!this.observers.length&&this.onFirstSubscribe&&(this.onLastUnsubscribe=this.onFirstSubscribe(this)||void 0),this.observers.push(t),{unsubscribe:function(){e.observers=e.observers.filter(function(e){return t!==e}),!e.observers.length&&e.onLastUnsubscribe&&e.onLastUnsubscribe()}}},kn.prototype.notify=function(t){this.observers.forEach(function(e){return e(t)})};var Cn,In=kn,Pn=new o({name:v.SESSION,onNewAegis:function(i){var o,e,t,u,c,l,f,n=this,s=O.SELF_GENETATE,a=(i.event.on(b.sampleChange,function(){Math.random()>i.sampleMap[v.SESSION]&&n.stopSessionPlugin()}),function(e,t){n.publishSessionDataWithAegis({session_type:e,is_active:t,session_from:s,link_session_id:null==o?void 0:o.linkSessionId,link_span_id:null==o?void 0:o.linkSpanId},i)}),r=function(e){function t(e){void 0===e&&(e=!1),a=Sn(),u=Sn()+9e5,o(e),i=setInterval(r,1e3)}function n(){c()?u=Sn()+9e5:t(!0)}function r(){c()||(s(),clearInterval(i))}var i,o=e.onCreateOrRebuild,s=e.onExpire,a=Sn(),u=Sn()+9e5,c=function(){return Sn()-Number(a)<144e5&&Sn()<Number(u)};t();var l;(l=["click","touchstart","keydown","scroll"]).forEach(function(e){return window.addEventListener.call(window,e,n,!0)});return function(){clearInterval(i),l.forEach(function(e){return window.removeEventListener.call(window,e,n)})}}({onCreateOrRebuild:function(e){var t=function(e,t){if(!e){var n,r=function(){for(var e=document.getElementsByTagName("meta"),t=0;t<e.length;t++){var n=e[t];if("traceparent"===n.getAttribute("name"))return n}return null}(),r=(null==r?void 0:r.content)||"";if(r)return{sessionId:r.split("-")[1],sessionFrom:O.SERVER_INJECT};if(r=(null==(r=null==(r=window.location)?void 0:r.search)?void 0:r.substring(1)).split("&"),n="",r.some(function(e){e=e.split("=");return"aegis_session_id"===e[0]&&(n=decodeURIComponent(e[1]),!0)}),n)return{sessionId:n,sessionFrom:O.URL_QUERY};var r={sessionId:w.get("aegis_session_id"),linkSessionId:w.get("link_session_id"),linkSpanId:w.get("link_span_id")},i=r.sessionId,o=r.linkSessionId,r=r.linkSpanId;if(i)return w.remove("aegis_session_id"),w.remove("link_session_id"),w.remove("link_span_id"),{sessionId:i,sessionFrom:O.COOKIES,linkSessionId:o,linkSpanId:r};try{var s=localStorage.getItem("aegis_session_id");if(null!==s){var a=JSON.parse(s),u=a.sessionId;if(t!==a.id)return{sessionId:u,sessionFrom:O.LOCAL_STORAGE}}}catch(e){}}return{sessionId:y.generateTraceId(),sessionFrom:O.SELF_GENETATE}}(e,i.config.id),n=t.sessionId,r=t.sessionFrom,t=x(t,["sessionId","sessionFrom"]);s=r,o=t;try{localStorage.setItem("aegis_session_id",JSON.stringify({id:i.config.id,sessionId:n}))}catch(e){}i.updateSnapshootInfo({session:{id:n}}),a("session",!0),e&&(i.updateSnapshootInfo({view:{id:y.generateSpanId(),loading_type:dn.INITIAL_LOAD,view_name:document.title,view_url:window.location.href,referrer:""}}),a("view",!0))},onExpire:function(){try{localStorage.removeItem("aegis_session_id")}catch(e){}e(),i.updateSnapshootInfo({session:void 0,view:void 0,action:void 0})}}),d=On({onStart:function(){i.updateSnapshootInfo({view:{id:y.generateSpanId(),loading_type:dn.INITIAL_LOAD,view_name:document.title,view_url:window.location.href,referrer:""}}),a("view",!0)},onChange:function(e,t){var n;null!=(n=i.snapshootInfo)&&n.action&&(a("action",!1),i.updateSnapshootInfo({action:void 0})),a("view",!1),i.updateSnapshootInfo({view:{id:y.generateSpanId(),loading_type:dn.ROUTE_CHANGE,view_name:document.title,view_url:t.href,referer:e.href}}),a("view",!0)}}),p=(null==(p=i.config.plugin)?void 0:p.session).actionTypes,h=(null==(h=i.config.plugin)?void 0:h.session).customActionNameAttribute,m=function(){};function g(e){var t,n,r,i,o,s;function a(){r||(r=!0,t(),clearTimeout(o),clearTimeout(s),null==i)||i()}c()&&(e={action_type:e.type,action_name:Tn(e.target,u),action_target_name:null==(e=null==(e=e.target)?void 0:e.nodeName)?void 0:e.toLowerCase()},l(e),t=f,r=!1,i=function(e){function n(){e({isBusy:0<i})}var t,r,i=0,o=new In(function(e){var t;if(MutationObserver)return(t=new MutationObserver(function(){return e.notify()})).observe(document,{attributes:!0,characterData:!0,childList:!0,subtree:!0}),function(){return t.disconnect()}}),s=new In(function(t){var e,n,r;return"function"==typeof(null===window||void 0===window?void 0:window.PerformanceObserver)?((e=new window.PerformanceObserver(function(){t.notify()})).observe({entryTypes:["resource"]}),function(){e.disconnect()}):ze()?(n=performance.getEntriesByType("resource").length,r=setInterval(function(){var e=performance.getEntriesByType("resource");n<=e.length&&t.notify(),n=e.length},100),function(){clearInterval(r),n=0}):yn}),a=function(e){var r=1,t=new In(function(n){var e=wn(XMLHttpRequest.prototype,"send",{before:function(){r+=1,this.requestIndex=r,n.notify({state:"start",requestIndex:r})},after:function(){var e=this,t=function(){e.removeEventListener("loadend",t),n.notify({state:"end",requestIndex:e.requestIndex})};this.addEventListener("loadend",t)}}).stop;return function(){return e()}}).subscribe(e),n=new In(function(t){var e=wn(window,"fetch",{before:function(){r+=1,t.notify({state:"start",requestIndex:r})},after:function(e){e.finally(function(){t.notify({state:"end",requestIndex:r})})}}).stop;return function(){return e()}}).subscribe(e);return{unsubscribe:function(){t.unsubscribe(),n.unsubscribe()}}},u=[o.subscribe(n),s.subscribe(n),a(function(e){var t=e.state,e=e.requestIndex;"start"===t?(void 0===r&&(r=e),i+=1,n()):"end"!==t||void 0===r||e<r||(--i,n())})],c=yn;try{null!=(t=Object.getOwnPropertyDescriptor(window,"open"))&&t.writable&&(c=wn(window,"open",{before:n}).stop)}catch(e){}return{stop:function(){c(),u.forEach(function(e){return e.unsubscribe()})}}}(function(e){clearTimeout(o),clearTimeout(n),e.isBusy||(n=setTimeout(function(){r||(r=!0,t(),clearTimeout(o),clearTimeout(s),null==i)||i()},100))}).stop,o=setTimeout(a,100),s=setTimeout(a,1e4))}!1!==(p=void 0===p?["click"]:p)&&0!==p.length&&(t=(p={actionTypes:p,customActionNameAttribute:h}).actionTypes,u=p.customActionNameAttribute,c=(h={onBeforeStart:function(){var e;return!(null!=(e=i.snapshootInfo)&&e.action)},onStart:function(e){i.updateSnapshootInfo({action:G({id:y.generateSpanId(),timestamp:Date.now()},e)}),a("action",!0)},onEnd:function(){var e;null!=(e=i.snapshootInfo)&&e.action&&(a("action",!1),i.updateSnapshootInfo({action:void 0}))}}).onBeforeStart,l=h.onStart,f=h.onEnd,t.forEach(function(e){window.addEventListener.call(window,e,g,!0)}),m=function(){t.forEach(function(e){window.removeEventListener.call(window,e,g)})},e=function(){var e=i.snapshootInfo,t=e.session,n=e.view;e.action&&a("action",!1),n&&a("view",!1),t&&a("session",!1)},window.addEventListener.call(window,"beforeunload",function(){i.sendNow=!0,e()},!0),this.stopSessionPlugin=function(){i.updateSnapshootInfo({session:void 0,view:void 0,action:void 0}),r(),d(),m()})},publishSessionDataWithAegis:function(e,t){this.publish(G(G({},e),{msg:e.session_type,type:z.SESSION}),t)},destroy:function(){var e;null!=(e=this.stopSessionPlugin)&&e.call(this)}}),Ln={containers:["body","html","#app","#root"],ignoreContainers:[],detectStartPosition:{x:0,y:0},emptyElementsPercent:70,sameElementsPercent:70,debounceDuration:1500,everySideSampleNumber:9,disableSameElementsCheck:!1,ignoreElesWhenDomChange:[],reDetectInterval:1500,samePointDepth:5},An=["canvas","img","svg","iframe"];function kn(e){this.onFirstSubscribe=e,this.observers=[]}(Dn=Cn=Cn||{})[Dn.UNKNOWN=0]="UNKNOWN",Dn[Dn.ERROR=1]="ERROR",Dn[Dn.DOM_CHANGE=2]="DOM_CHANGE";function Nn(e,t,n){var r=(e=e.map(function(e){return xn(e)}))[0];return r?(t=t.some(function(e){return r.includes(e)}),n=n.some(function(e){return r.includes(e)}),{isWhitePoint:t&&n,selectorPointers:e}):{isWhitePoint:!1,selectorPointers:e}}function _n(e){try{var t=e.config.plugin.blankScreen;return!0===t?Ln:J(Ln,t)}catch(e){return Ln}}var xn=function(e){var t=e.nodeName.toLowerCase();return e.id?t+"#"+e.id:e.className&&"string"==typeof e.className?t+"."+e.className.split(" ").filter(function(e){return!!e}).join("."):t},Dn=new o({name:v.BLANK_SCREEN,onNewAegis:function(I){var P,L,A,e,t,k,N,_,x,n,D,M,q,r,H,B,i,F,j,U,o,s,W,a,u,c,l,f,d,p,w,h,m,g,v,y=this;null!==document&&void 0!==document&&document.elementFromPoint&&(r=_n(I),A=r.everySideSampleNumber,e=r.sameElementsPercent,t=r.emptyElementsPercent,k=r.containers,N=r.ignoreContainers,_=r.debounceDuration,x=r.detectStartPosition,n=r.ignoreElesWhenDomChange,D=r.reDetectInterval,M=r.disableSameElementsCheck,q=r.samePointDepth,r=4*A-3,H=Math.floor(r*t/100),B=Math.floor(r*e/100),i=Cn.UNKNOWN,j=null,U="",o=F=!1,s=function(){return K(y,void 0,void 0,function(){var i,o,s,a,u,c,l,f,d,p,h,m,g,v,y,w,b,E,S,R,T,O,C=this;return X(this,function(e){switch(e.label){case 0:if(i=window.innerHeight,o=window.innerWidth,s=x.x,a=x.y,o<=s||i<=a)return[2];for(u=s+(o-s)/2,c=a+(i-a)/2,l=[],f=A+1,d=0,p=Date.now(),h=1;h<f;h++)w=s+(o-s)*h/f,y=a+(i-a)*h/f,v=document.elementsFromPoint(w,c),m=document.elementsFromPoint(u,y),g=document.elementsFromPoint(w,y),w=document.elementsFromPoint(w,i-y),y=Nn(v,k,N),v=y.isWhitePoint,y=y.selectorPointers,v&&(d+=1),l.push(y),h!==f/2&&(v=Nn(m,k,N),y=v.isWhitePoint,m=v.selectorPointers,y&&(d+=1),v=Nn(g,k,N),y=v.isWhitePoint,g=v.selectorPointers,y&&(d+=1),v=Nn(w,k,N),y=v.isWhitePoint,w=v.selectorPointers,y&&(d+=1),l.push(m,g,w));return b=H<=d,E=!1,S={samePointerMap:{},maximumSamePointer:{}},M||(T=l.map(function(e){return e.slice(0,q)}),t=T.map(function(e){return e.join(" < ")}).reduce(function(e,t){return e[t]=e[t]?e[t]+1:1,e},{}),n=Object.keys(t).map(function(e){return[e,t[e]]}).sort(function(e,t){return t[1]-e[1]})[0],r=n[0],n=n[1],S={samePointerMap:(T={samePointerMap:t,maximumSamePointerClass:r,maximumSamePointerCounter:n}).samePointerMap,maximumSamePointer:((O={})[R=T.maximumSamePointerClass]=T=T.maximumSamePointerCounter,O)},O=An.some(function(e){return R.startsWith(e)}),E=B<=T&&!O),b||E?[3,1]:(F=!1,j=null,[3,4]);case 1:return F?(U!==I.snapshootInfo.from&&j&&(this.publish({level:V.BLANK_SCREEN,type:z.PV}),U=I.snapshootInfo.from),this.publish(J(j,P?{linkLogId:null==P?void 0:P.aegisv2_goto,linkLogType:null==P?void 0:P.type,linkLogLevel:null==P?void 0:P.level,linkErrorMsg:null==P?void 0:P.errorMsg}:{}),I),[4,I.setTempConfig({id:"SDK-88b1f242f91b60885f0c"},function(){return I.reportTime("白屏检测总耗时",Date.now()-p)})]):[3,3];case 2:return e.sent(),F=!1,j=null,[3,4];case 3:T=Date.now()-I.timeOrigin-_,j=G(G({msg:"blank_screen",level:V.BLANK_SCREEN,type:z.NORMAL,triggerTime:T<0?0:T,whitePointCount:d},S),P?{linkLogId:null==P?void 0:P.aegisv2_goto,linkLogType:null==P?void 0:P.type,linkLogLevel:null==P?void 0:P.level,linkErrorMsg:null==P?void 0:P.errorMsg}:{}),F=!0,L=setTimeout(function(){return K(C,void 0,void 0,function(){return X(this,function(e){switch(e.label){case 0:return[4,W()];case 1:return e.sent(),[2]}})})},D),e.label=4;case 4:return[2]}var t,n,r})})},W=function(){return K(y,void 0,void 0,function(){var n=this;return X(this,function(e){switch(e.label){case 0:return"requestIdleCallback"in window?(window.requestIdleCallback(function(t){return K(n,void 0,void 0,function(){return X(this,function(e){switch(e.label){case 0:return 0<t.timeRemaining()?[4,s()]:[3,2];case 1:e.sent(),e.label=2;case 2:return[2]}})})}),[3,3]):[3,1];case 1:return[4,s()];case 2:e.sent(),e.label=3;case 3:return[2]}})})},a=function(){var e;F||i===Cn.ERROR&&!P||(i=P?Cn.ERROR:Cn.DOM_CHANGE,clearTimeout(L),e=_n(I).debounceDuration,L=setTimeout(function(){return K(y,void 0,void 0,function(){return X(this,function(e){switch(e.label){case 0:return clearTimeout(L),[4,W()];case 1:return e.sent(),[2]}})})},e))},I.event.on(b.errorOccurred,u=function(e){e=e.filter(function(e){return e.level!==V.BLANK_SCREEN});0<e.length&&(P=e[e.length-1],a())}),c=null,window.MutationObserver&&(h=m=null,l=window.cancelAnimationFrame||function(e){clearTimeout(e)},f=window.requestAnimationFrame||function(e){return setTimeout(e,1e3/60)},d=function(){(c=new MutationObserver(function(e){var t=n.includes(xn(e[0].target));1===e.length&&t||a()})).observe(document.body,{childList:!0,subtree:!0})},p=function(){m&&(clearTimeout(m),m=null),h&&(l(h),h=null)},document.body?d():(h=f(w=function(){document.body?(p(),d()):h=f(w)}),m=setTimeout(function(){p()},1e4))),g=function(){o&&(o=!0,F)&&(clearTimeout(L),s())},v=function(){"visible"===document.visibilityState&&(o=!1),"hidden"===document.visibilityState&&g()},window.addEventListener("pagehide",g),window.addEventListener("beforeunload",g),window.addEventListener("visibilitychange",v),this.destroyBlanckScreenDetect=function(){window.removeEventListener("pagehide",g),window.removeEventListener("beforeunload",g),window.removeEventListener("visibilitychange",v),I.event.remove(b.errorOccurred,u),null!=c&&c.disconnect()})},destroy:function(){this.destroyBlanckScreenDetect()}}),o=(new o({name:v.WEBSOCKET}),new o({name:v.WEBSOCKET,init:function(t){var n=this,e={key:t.config.id+"-"+this.name,onErr:function(e){t.config.websocketHack||n.publishWsErrorLog(e,t)},sendErr:function(e){t.config.websocketHack||n.publishWsErrorLog(e,t)},sendConnectInfo:function(e){n.publish(G(G({},e),{level:V.INFO,type:z.WEBSOCKET}),t)}};Ye(e)},publishWsErrorLog:function(e,t){var n=e.connectUrl,r=e.msg,e=e.readyState;this.publish({msg:"WEBSOCKET_ERROR: \n              connect: "+n+"\n              readyState: "+e+"\n              msg: "+r,level:V.WEBSOCKET_ERROR,type:z.WEBSOCKET},t)},destroy:function(){this.publishErrorLog=function(){},this.hackWebsocketConfig&&Qe(this.hackWebsocketConfig)}}));return i.use(Pn),i.use(Ke),i.use(pn),i.use(Rt),i.use(vn),i.use(Je),i.use(Ot),i.use($t),i.use(wt),i.use(Zt),i.use(rn),i.use(Dn),i.use(o),i});
