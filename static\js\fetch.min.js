﻿!function(b,a){"object"==typeof exports&&"undefined"!=typeof module?a():"function"==typeof define&&define.amd?define(a):a()}(0,function(){function p(c){var a=this.constructor;return this.then(function(d){return a.resolve(c()).then(function(){return d})},function(d){return a.resolve(c()).then(function(){return a.reject(d)})})}function y(a){return new this(function(d,u){function s(o,r){if(r&&("object"==typeof r||"function"==typeof r)){var i=r.then;if("function"==typeof i){return void i.call(r,function(f){s(o,f)},function(f){e[o]={status:"rejected",reason:f},0==--c&&d(e)})}}e[o]={status:"fulfilled",value:r},0==--c&&d(e)}if(!a||"undefined"==typeof a.length){return u(new TypeError(typeof a+" "+a+" is not iterable(cannot read property Symbol(Symbol.iterator))"))}var e=Array.prototype.slice.call(a);if(0===e.length){return d([])}for(var c=e.length,l=0;e.length>l;l++){s(l,e[l])}})}function h(a){return !(!a||"undefined"==typeof a.length)}function g(){}function b(a){if(!(this instanceof b)){throw new TypeError("Promises must be constructed via new")}if("function"!=typeof a){throw new TypeError("not a function")}this._state=0,this._handled=!1,this._value=undefined,this._deferreds=[],j(a,this)}function k(c,a){for(;3===c._state;){c=c._value}0!==c._state?(c._handled=!0,b._immediateFn(function(){var f=1===c._state?a.onFulfilled:a.onRejected;if(null!==f){var e;try{e=f(c._value)}catch(d){return void x(a.promise,d)}m(a.promise,e)}else{(1===c._state?m:x)(a.promise,c._value)}})):c._deferreds.push(a)}function m(c,a){try{if(a===c){throw new TypeError("A promise cannot be resolved with itself.")}if(a&&("object"==typeof a||"function"==typeof a)){var f=a.then;if(a instanceof b){return c._state=3,c._value=a,void v(c)}if("function"==typeof f){return void j(function(l,i){return function(){l.apply(i,arguments)}}(f,a),c)}}c._state=1,c._value=a,v(c)}catch(d){x(c,d)}}function x(c,a){c._state=2,c._value=a,v(c)}function v(c){2===c._state&&0===c._deferreds.length&&b._immediateFn(function(){c._handled||b._unhandledRejectionFn(c._value)});for(var a=0,d=c._deferreds.length;d>a;a++){k(c,c._deferreds[a])}c._deferreds=null}function j(c,a){var f=!1;try{c(function(i){f||(f=!0,m(a,i))},function(i){f||(f=!0,x(a,i))})}catch(d){if(f){return}f=!0,x(a,d)}}var w=setTimeout,z="undefined"!=typeof setImmediate?setImmediate:null;b.prototype["catch"]=function(a){return this.then(null,a)},b.prototype.then=function(c,a){var d=new this.constructor(g);return k(this,new function(i,f,l){this.onFulfilled="function"==typeof i?i:null,this.onRejected="function"==typeof f?f:null,this.promise=l}(c,a,d)),d},b.prototype["finally"]=p,b.all=function(a){return new b(function(e,s){function l(i,r){try{if(r&&("object"==typeof r||"function"==typeof r)){var f=r.then;if("function"==typeof f){return void f.call(r,function(u){l(i,u)},s)}}d[i]=r,0==--n&&e(d)}catch(o){s(o)}}if(!h(a)){return s(new TypeError("Promise.all accepts an array"))}var d=Array.prototype.slice.call(a);if(0===d.length){return e([])}for(var n=d.length,c=0;d.length>c;c++){l(c,d[c])}})},b.allSettled=y,b.resolve=function(a){return a&&"object"==typeof a&&a.constructor===b?a:new b(function(c){c(a)})},b.reject=function(a){return new b(function(c,d){d(a)})},b.race=function(a){return new b(function(d,l){if(!h(a)){return l(new TypeError("Promise.race accepts an array"))}for(var c=0,e=a.length;e>c;c++){b.resolve(a[c]).then(d,l)}})},b._immediateFn="function"==typeof z&&function(a){z(a)}||function(a){w(a,0)},b._unhandledRejectionFn=function(a){void 0!==console&&console&&console.warn("Possible Unhandled Promise Rejection:",a)};var q=function(){if("undefined"!=typeof self){return self}if("undefined"!=typeof window){return window}if("undefined"!=typeof global){return global}throw Error("unable to locate global object")}();"function"!=typeof q.Promise?q.Promise=b:(q.Promise.prototype["finally"]||(q.Promise.prototype["finally"]=p),q.Promise.allSettled||(q.Promise.allSettled=y))});(function(q){if(q.fetch){return}function h(r){if(typeof r!=="string"){r=String(r)}if(/[^a-z0-9\-#$%&'*+.\^_`|~]/i.test(r)){throw new TypeError("Invalid character in header field name")}return r.toLowerCase()}function f(r){if(typeof r!=="string"){r=String(r)}return r}function o(r){this.map={};if(r instanceof o){r.forEach(function(t,s){this.append(s,t)},this)}else{if(r){Object.getOwnPropertyNames(r).forEach(function(s){this.append(s,r[s])},this)}}}o.prototype.append=function(r,t){r=h(r);t=f(t);var s=this.map[r];if(!s){s=[];this.map[r]=s}s.push(t)};o.prototype["delete"]=function(r){delete this.map[h(r)]};o.prototype.get=function(s){var r=this.map[h(s)];return r?r[0]:null};o.prototype.getAll=function(r){return this.map[h(r)]||[]};o.prototype.has=function(r){return this.map.hasOwnProperty(h(r))};o.prototype.set=function(r,s){this.map[h(r)]=[f(s)]};o.prototype.forEach=function(s,r){Object.getOwnPropertyNames(this.map).forEach(function(t){this.map[t].forEach(function(u){s.call(r,u,t,this)},this)},this)};function k(r){if(r.bodyUsed){return Promise.reject(new TypeError("Already read"))
}r.bodyUsed=true}function j(r){return new Promise(function(t,s){r.onload=function(){t(r.result)};r.onerror=function(){s(r.error)}})}function g(s){var r=new FileReader();r.readAsArrayBuffer(s);return j(r)}function p(s,u){var r=new FileReader();var x=u.headers.map["content-type"]?u.headers.map["content-type"].toString():"";var v=/charset\=[0-9a-zA-Z\-\_]*;?/;var w=s.type.match(v)||x.match(v);var t=[s];if(w){t.push(w[0].replace(/^charset\=/,"").replace(/;$/,""))}r.readAsText.apply(r,t);return j(r)}var m={blob:"FileReader" in q&&"Blob" in q&&(function(){try{new Blob();return true}catch(r){return false}})(),formData:"FormData" in q,arrayBuffer:"ArrayBuffer" in q};function l(){this.bodyUsed=false;this._initBody=function(r,s){this._bodyInit=r;if(typeof r==="string"){this._bodyText=r}else{if(m.blob&&Blob.prototype.isPrototypeOf(r)){this._bodyBlob=r;this._options=s}else{if(m.formData&&FormData.prototype.isPrototypeOf(r)){this._bodyFormData=r}else{if(!r){this._bodyText=""}else{if(m.arrayBuffer&&ArrayBuffer.prototype.isPrototypeOf(r)){}else{throw new Error("unsupported BodyInit type")}}}}}};if(m.blob){this.blob=function(){var r=k(this);if(r){return r}if(this._bodyBlob){return Promise.resolve(this._bodyBlob)}else{if(this._bodyFormData){throw new Error("could not read FormData body as blob")}else{return Promise.resolve(new Blob([this._bodyText]))}}};this.arrayBuffer=function(){return this.blob().then(g)};this.text=function(){var r=k(this);if(r){return r}if(this._bodyBlob){return p(this._bodyBlob,this._options)}else{if(this._bodyFormData){throw new Error("could not read FormData body as text")}else{return Promise.resolve(this._bodyText)}}}}else{this.text=function(){var r=k(this);return r?r:Promise.resolve(this._bodyText)}}if(m.formData){this.formData=function(){return this.text().then(a)}}this.json=function(){return this.text().then(JSON.parse)};return this}var e=["DELETE","GET","HEAD","OPTIONS","POST","PUT"];function i(s){var r=s.toUpperCase();return(e.indexOf(r)>-1)?r:s}function c(s,t){t=t||{};var r=t.body;if(c.prototype.isPrototypeOf(s)){if(s.bodyUsed){throw new TypeError("Already read")}this.url=s.url;this.credentials=s.credentials;if(!t.headers){this.headers=new o(s.headers)}this.method=s.method;this.mode=s.mode;if(!r){r=s._bodyInit;s.bodyUsed=true}}else{this.url=s}this.credentials=t.credentials||this.credentials||"omit";if(t.headers||!this.headers){this.headers=new o(t.headers)}this.method=i(t.method||this.method||"GET");this.mode=t.mode||this.mode||null;this.referrer=null;if((this.method==="GET"||this.method==="HEAD")&&r){throw new TypeError("Body not allowed for GET or HEAD requests")}this._initBody(r,t)}c.prototype.clone=function(){return new c(this)};function a(r){var s=new FormData();r.trim().split("&").forEach(function(t){if(t){var v=t.split("=");var u=v.shift().replace(/\+/g," ");var w=v.join("=").replace(/\+/g," ");s.append(decodeURIComponent(u),decodeURIComponent(w))}});return s}function d(t){var r=new o();var s=t.getAllResponseHeaders().trim().split("\n");s.forEach(function(x){var v=x.trim().split(":");var u=v.shift().trim();var w=v.join(":").trim();r.append(u,w)});return r}l.call(c.prototype);function b(s,r){if(!r){r={}}this._initBody(s,r);this.type="default";this.status=r.status;this.ok=this.status>=200&&this.status<300;this.statusText=r.statusText;this.headers=r.headers instanceof o?r.headers:new o(r.headers);this.url=r.url||""}l.call(b.prototype);b.prototype.clone=function(){return new b(this._bodyInit,{status:this.status,statusText:this.statusText,headers:new o(this.headers),url:this.url})};b.error=function(){var r=new b(null,{status:0,statusText:""});r.type="error";return r};var n=[301,302,303,307,308];b.redirect=function(s,r){if(n.indexOf(r)===-1){throw new RangeError("Invalid status code")}return new b(null,{status:r,headers:{location:s}})};q.Headers=o;q.Request=c;q.Response=b;q.fetch=function(r,s){return new Promise(function(w,v){var u;if(c.prototype.isPrototypeOf(r)&&!s){u=r}else{u=new c(r,s)}var A=new XMLHttpRequest();function z(){if("responseURL" in A){return A.responseURL}if(/^X-Request-URL:/m.test(A.getAllResponseHeaders())){return A.getResponseHeader("X-Request-URL")}return}var t=false;function y(){if(A.readyState!==4){return}var C=(A.status===1223)?204:A.status;if(C<100||C>599){if(t){return}else{t=true}v(new TypeError("Network request failed"));return}var D={status:C,statusText:A.statusText,headers:d(A),url:z()};var B="response" in A?A.response:A.responseText;if(t){return}else{t=true}w(new b(B,D))}A.onreadystatechange=y;A.onload=y;A.onerror=function(){if(t){return}else{t=true}v(new TypeError("Network request failed"))};A.open(u.method,u.url,true);try{if(u.credentials==="include"){if("withCredentials" in A){A.withCredentials=true}else{console&&console.warn&&console.warn("withCredentials is not supported, you can ignore this warning")}}}catch(x){console&&console.warn&&console.warn("set withCredentials error:"+x)}if("responseType" in A&&m.blob){A.responseType="blob"}u.headers.forEach(function(C,B){A.setRequestHeader(B,C)
});A.send(typeof u._bodyInit==="undefined"?null:u._bodyInit)})};q.fetch.polyfill=true;if(typeof module!=="undefined"&&module.exports){module.exports=q.fetch}})(typeof self!=="undefined"?self:this);