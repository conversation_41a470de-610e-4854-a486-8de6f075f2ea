﻿!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).Fingerprint=e()}(this,function(){"use strict";function u(t,e,r,n,o,i,a){try{var c=t[i](a),u=c.value}catch(t){return void r(t)}c.done?e(u):Promise.resolve(u).then(n,o)}function i(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}var t,a=(function(e){e=function(a){var u,t=Object.prototype,s=t.hasOwnProperty,e="function"==typeof Symbol?Symbol:{},n=e.iterator||"@@iterator",r=e.asyncIterator||"@@asyncIterator",o=e.toStringTag||"@@toStringTag";function i(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{i({},"")}catch(t){i=function(t,e,r){return t[e]=r}}function c(t,e,r,n){var o,i,a,c,e=e&&e.prototype instanceof g?e:g,e=Object.create(e.prototype),n=new L(n||[]);return e._invoke=(o=t,i=r,a=n,c=l,function(t,e){if(c===d)throw new Error("Generator is already running");if(c===p){if("throw"===t)throw e;return _()}for(a.method=t,a.arg=e;;){var r=a.delegate;if(r){var n=function t(e,r){var n=e.iterator[r.method];if(n===u){if(r.delegate=null,"throw"===r.method){if(e.iterator.return&&(r.method="return",r.arg=u,t(e,r),"throw"===r.method))return v;r.method="throw",r.arg=new TypeError("The iterator does not provide a 'throw' method")}return v}n=h(n,e.iterator,r.arg);if("throw"===n.type)return r.method="throw",r.arg=n.arg,r.delegate=null,v;var n=n.arg;if(!n)return r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v;{if(!n.done)return n;r[e.resultName]=n.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=u)}r.delegate=null;return v}(r,a);if(n){if(n===v)continue;return n}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(c===l)throw c=p,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);c=d;n=h(o,i,a);if("normal"===n.type){if(c=a.done?p:f,n.arg!==v)return{value:n.arg,done:a.done}}else"throw"===n.type&&(c=p,a.method="throw",a.arg=n.arg)}}),e}function h(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}a.wrap=c;var l="suspendedStart",f="suspendedYield",d="executing",p="completed",v={};function g(){}function y(){}function w(){}var m={};m[n]=function(){return this};e=Object.getPrototypeOf,e=e&&e(e(O([])));e&&e!==t&&s.call(e,n)&&(m=e);var x=w.prototype=g.prototype=Object.create(m);function b(t){["next","throw","return"].forEach(function(e){i(t,e,function(t){return this._invoke(e,t)})})}function C(a,c){var e;this._invoke=function(r,n){function t(){return new c(function(t,e){!function e(t,r,n,o){t=h(a[t],a,r);if("throw"!==t.type){var i=t.arg,r=i.value;return r&&"object"==typeof r&&s.call(r,"__await")?c.resolve(r.__await).then(function(t){e("next",t,n,o)},function(t){e("throw",t,n,o)}):c.resolve(r).then(function(t){i.value=t,n(i)},function(t){return e("throw",t,n,o)})}o(t.arg)}(r,n,t,e)})}return e=e?e.then(t,t):t()}}function A(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function E(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function L(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(A,this),this.reset(!0)}function O(e){if(e){var t=e[n];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,t=function t(){for(;++r<e.length;)if(s.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=u,t.done=!0,t};return t.next=t}}return{next:_}}function _(){return{value:u,done:!0}}return((y.prototype=x.constructor=w).constructor=y).displayName=i(w,o,"GeneratorFunction"),a.isGeneratorFunction=function(t){t="function"==typeof t&&t.constructor;return!!t&&(t===y||"GeneratorFunction"===(t.displayName||t.name))},a.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,i(t,o,"GeneratorFunction")),t.prototype=Object.create(x),t},a.awrap=function(t){return{__await:t}},b(C.prototype),C.prototype[r]=function(){return this},a.AsyncIterator=C,a.async=function(t,e,r,n,o){void 0===o&&(o=Promise);var i=new C(c(t,e,r,n),o);return a.isGeneratorFunction(e)?i:i.next().then(function(t){return t.done?t.value:i.next()})},b(x),i(x,o,"Generator"),x[n]=function(){return this},x.toString=function(){return"[object Generator]"},a.keys=function(r){var t,n=[];for(t in r)n.push(t);return n.reverse(),function t(){for(;n.length;){var e=n.pop();if(e in r)return t.value=e,t.done=!1,t}return t.done=!0,t}},a.values=O,L.prototype={constructor:L,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=u,this.done=!1,this.delegate=null,this.method="next",this.arg=u,this.tryEntries.forEach(E),!t)for(var e in this)"t"===e.charAt(0)&&s.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=u)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(r){if(this.done)throw r;var n=this;function t(t,e){return i.type="throw",i.arg=r,n.next=t,e&&(n.method="next",n.arg=u),!!e}for(var e=this.tryEntries.length-1;0<=e;--e){var o=this.tryEntries[e],i=o.completion;if("root"===o.tryLoc)return t("end");if(o.tryLoc<=this.prev){var a=s.call(o,"catchLoc"),c=s.call(o,"finallyLoc");if(a&&c){if(this.prev<o.catchLoc)return t(o.catchLoc,!0);if(this.prev<o.finallyLoc)return t(o.finallyLoc)}else if(a){if(this.prev<o.catchLoc)return t(o.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return t(o.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;0<=r;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&s.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var o=n;break}}var i=(o=o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc?null:o)?o.completion:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o.finallyLoc,v):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;0<=e;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),E(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;0<=e;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n,o=r.completion;return"throw"===o.type&&(n=o.arg,E(r)),n}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:O(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=u),v}},a}(e.exports);try{regeneratorRuntime=e}catch(t){Function("r","regeneratorRuntime = r")(e)}}(t={exports:{}}),t.exports);return function(){function r(t){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,r),this.version="1.0.0";var e=Array.prototype.map,a=Array.prototype.forEach;t&&(this.hasher=t),this.each=function(t,e,r){if(null!=t)if(a&&t.forEach===a)t.forEach(e,r);else if(t.length===+t.length){for(var n=0,o=t.length;n<o;n++)if(e.call(r,t[n],n,t)==={})return}else for(var i in t)if(t.hasOwnProperty(i)&&e.call(r,t[i],i,t)==={})return},this.map=function(t,n,o){var i=[];return null==t?i:e&&t.map===e?t.map(n,o):(this.each(t,function(t,e,r){i[i.length]=n.call(o,t,e,r)}),i)}}var t,e,n,c,o;return t=r,(e=[{key:"getQimei36",value:function(n,o){var i=this;this.getHid().then(function(t){var e="3BJr"+n.substring(0,2)+(t&&t.substring(3,7)),r=new XMLHttpRequest;r.open("POST","https://snowflake.qq.com/ola/h5",!0),r.setRequestHeader("Content-Type","application/json"),r.onreadystatechange=function(){if(r.readyState==XMLHttpRequest.DONE&&200==r.status)try{o&&o(JSON.parse(r.responseText))}catch(t){o(null),console.log(t)}},r.send(JSON.stringify({appKey:n,hid:t,sign:e,version:i.version}))})}},{key:"getHid",value:(c=a.mark(function t(){var r,n;return a.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return(r=[]).push((e=void 0,(e=[Math.floor(window.screen.width*window.devicePixelRatio),Math.floor(window.screen.height*window.devicePixelRatio)]).sort().reverse(),e.join("x"))),r.push((e=void 0,(e=[Math.floor(window.screen.availWidth*window.devicePixelRatio),Math.floor(window.screen.availHeight*window.devicePixelRatio)]).sort().reverse(),e.join("x"))),r.push(navigator.deviceMemory),r.push(!!window.sessionStorage),r.push(!!window.indexedDB),r.push(navigator.productSub),r.push(navigator.hardwareConcurrency),r.push(this.getWebglVendorAndRenderer()),r.push((new Date).getTimezoneOffset()),t.next=12,this.getFactor();case 12:if(n=t.sent,r.push(n),this.hasher)return t.abrupt("return",this.hasher(r.join("###"),31));t.next=18;break;case 18:return t.abrupt("return",this.x64hash128(r.join("###"),31));case 19:case"end":return t.stop()}var e},t,this)}),o=function(){var t=this,a=arguments;return new Promise(function(e,r){var n=c.apply(t,a);function o(t){u(n,e,r,o,i,"next",t)}function i(t){u(n,e,r,o,i,"throw",t)}o(void 0)})},function(){return o.apply(this,arguments)})},{key:"getUserAgent",value:function(){return navigator.userAgent}},{key:"getNative",value:function(){var e=this;this.getHid().then(function(t){JSInterface.callback(e.version,t,e.getUserAgent())})}},{key:"getWebglVendorAndRenderer",value:function(){try{var t=function(){var t=document.createElement("canvas"),e=null;try{e=t.getContext("webgl")||t.getContext("experimental-webgl")}catch(t){}return e=e||null}(),e=t.getExtension("WEBGL_debug_renderer_info"),r=[t.getParameter(e.UNMASKED_VENDOR_WEBGL),t.getParameter(e.UNMASKED_RENDERER_WEBGL)].join("~"),n=t.getExtension("WEBGL_lose_context");return null!=n&&n.loseContext(),r}catch(t){return null}}},{key:"getFactor",value:function(){return new Promise(function(o,t){var a=window.RTCPeerConnection||window.webkitRTCPeerConnection||window.mozRTCPeerConnection;a?function(){var e=new a({iceServers:[]});e.createDataChannel("",{reliable:!1}),e.onicecandidate=function(t){t.candidate&&r("a=".concat(t.candidate.candidate))},e.createOffer(function(t){r(t.sdp),e.setLocalDescription(t)},function(t){console.warn("offer failed!",t)});var n=Object.create(null);function i(t){if(!(t in n)){n[t]=!0;for(var e=Object.keys(n).filter(function(t){return n[t]}),r=0;r<e.length;r++)16<e[r].length&&(e.splice(r,1),r--);o(e[0])}}function r(t){(0<arguments.length&&void 0!==t?t:"").split("\r\n").forEach(function(t,e,r){var n,o;~t.indexOf("a=candidate")?(o=(n=t.split(" "))[4],"host"===n[7]&&i(o)):~t.indexOf("c=")&&i(t.split(" ")[2])})}n["0.0.0.0"]=!1}():o(null)})}},{key:"x64hash128",value:function(t,e){for(var r=function(t,e){t=[t[0]>>>16,65535&t[0],t[1]>>>16,65535&t[1]],e=[e[0]>>>16,65535&e[0],e[1]>>>16,65535&e[1]];var r=[0,0,0,0];return r[3]+=t[3]+e[3],r[2]+=r[3]>>>16,r[3]&=65535,r[2]+=t[2]+e[2],r[1]+=r[2]>>>16,r[2]&=65535,r[1]+=t[1]+e[1],r[0]+=r[1]>>>16,r[1]&=65535,r[0]+=t[0]+e[0],r[0]&=65535,[r[0]<<16|r[1],r[2]<<16|r[3]]},n=function(t,e){t=[t[0]>>>16,65535&t[0],t[1]>>>16,65535&t[1]],e=[e[0]>>>16,65535&e[0],e[1]>>>16,65535&e[1]];var r=[0,0,0,0];return r[3]+=t[3]*e[3],r[2]+=r[3]>>>16,r[3]&=65535,r[2]+=t[2]*e[3],r[1]+=r[2]>>>16,r[2]&=65535,r[2]+=t[3]*e[2],r[1]+=r[2]>>>16,r[2]&=65535,r[1]+=t[1]*e[3],r[0]+=r[1]>>>16,r[1]&=65535,r[1]+=t[2]*e[2],r[0]+=r[1]>>>16,r[1]&=65535,r[1]+=t[3]*e[1],r[0]+=r[1]>>>16,r[1]&=65535,r[0]+=t[0]*e[3]+t[1]*e[2]+t[2]*e[1]+t[3]*e[0],r[0]&=65535,[r[0]<<16|r[1],r[2]<<16|r[3]]},o=function(t,e){return 32===(e%=64)?[t[1],t[0]]:e<32?[t[0]<<e|t[1]>>>32-e,t[1]<<e|t[0]>>>32-e]:[t[1]<<(e-=32)|t[0]>>>32-e,t[0]<<e|t[1]>>>32-e]},i=function(t,e){return 0===(e%=64)?t:e<32?[t[0]<<e|t[1]>>>32-e,t[1]<<e]:[t[1]<<e-32,0]},a=function(t,e){return[t[0]^e[0],t[1]^e[1]]},c=function(t){return t=a(t,[0,t[0]>>>1]),t=n(t,[4283543511,3981806797]),t=a(t,[0,t[0]>>>1]),t=n(t,[3301882366,444984403]),t=a(t,[0,t[0]>>>1])},u=(t=t||"").length%16,s=t.length-u,h=[0,e=e||0],l=[0,e],f=[0,0],d=[0,0],p=[2277735313,289559509],v=[1291169091,658871167],g=0;g<s;g+=16)f=[255&t.charCodeAt(g+4)|(255&t.charCodeAt(g+5))<<8|(255&t.charCodeAt(g+6))<<16|(255&t.charCodeAt(g+7))<<24,255&t.charCodeAt(g)|(255&t.charCodeAt(g+1))<<8|(255&t.charCodeAt(g+2))<<16|(255&t.charCodeAt(g+3))<<24],d=[255&t.charCodeAt(g+12)|(255&t.charCodeAt(g+13))<<8|(255&t.charCodeAt(g+14))<<16|(255&t.charCodeAt(g+15))<<24,255&t.charCodeAt(g+8)|(255&t.charCodeAt(g+9))<<8|(255&t.charCodeAt(g+10))<<16|(255&t.charCodeAt(g+11))<<24],f=o(f=n(f,p),31),f=n(f,v),h=r(h=o(h=a(h,f),27),l),h=r(n(h,[0,5]),[0,1390208809]),d=o(d=n(d,v),33),d=n(d,p),l=r(l=o(l=a(l,d),31),h),l=r(n(l,[0,5]),[0,944331445]);switch(f=[0,0],d=[0,0],u){case 15:d=a(d,i([0,t.charCodeAt(g+14)],48));case 14:d=a(d,i([0,t.charCodeAt(g+13)],40));case 13:d=a(d,i([0,t.charCodeAt(g+12)],32));case 12:d=a(d,i([0,t.charCodeAt(g+11)],24));case 11:d=a(d,i([0,t.charCodeAt(g+10)],16));case 10:d=a(d,i([0,t.charCodeAt(g+9)],8));case 9:d=a(d,[0,t.charCodeAt(g+8)]),d=o(d=n(d,v),33),d=n(d,p),l=a(l,d);case 8:f=a(f,i([0,t.charCodeAt(g+7)],56));case 7:f=a(f,i([0,t.charCodeAt(g+6)],48));case 6:f=a(f,i([0,t.charCodeAt(g+5)],40));case 5:f=a(f,i([0,t.charCodeAt(g+4)],32));case 4:f=a(f,i([0,t.charCodeAt(g+3)],24));case 3:f=a(f,i([0,t.charCodeAt(g+2)],16));case 2:f=a(f,i([0,t.charCodeAt(g+1)],8));case 1:f=a(f,[t.charCodeAt(g)]),f=o(f=n(f,p),31),f=n(f,v),h=a(h,f)}return h=a(h,[0,t.length]),l=r(l=a(l,[0,t.length]),h=r(h,l)),h=c(h),l=r(l=c(l),h=r(h,l)),("00000000"+(h[0]>>>0).toString(16)).slice(-8)+("00000000"+(h[1]>>>0).toString(16)).slice(-8)+("00000000"+(l[0]>>>0).toString(16)).slice(-8)+("00000000"+(l[1]>>>0).toString(16)).slice(-8)}}])&&i(t.prototype,e),n&&i(t,n),r}()});
