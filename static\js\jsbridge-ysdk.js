﻿// JsBridge 通用模块
window.JsBridge = function (JsBridge, win, undefined) {
	// JsBridge 核心框架通用模块
JsBridge = JsBridge || {};

JsBridge.iOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
if (JsBridge.iOS && navigator.userAgent.indexOf("Android") >= 0) {
	// 同时是 iOS 和 Android，那就说明不是 iOS
	JsBridge.iOS = false;
}

var framesPool = [];

function createNewFrame() {
	var frame = document.createElement("iframe");
	framesPool.push(frame);
	frame.style.cssText = "position:absolute;left:0;top:0;width:0;height:0;visibility:hidden;";
	frame.frameBorder = "0";
	document.body.appendChild(frame);
	return frame;
}

JsBridge._createMultiCallback = function (callbackParams, alias) {
	return function (res) {
		callbackParams.result[alias] = res;
		callbackParams.count--;
		if (callbackParams.count == 0 && callbackParams.callback) {
			res = callbackParams.callback(callbackParams.result);
			if (res && typeof res == "object") {
				JsBridge.multiCall.apply(JsBridge, [res].concat(callbackParams.callbackChain));
			}
		}
	};
};

JsBridge._callWithScheme = JsBridge.callWithScheme = function (url) {
	console.log("JsBridge.callWithScheme: ", url); // @debug
	var frame;
	for (var i = 0; frame = framesPool[i]; i++) {
		if (!frame._busy) {
			break;
		}
	}
	if (!frame || frame._busy) {
		frame = createNewFrame();
	}
	frame._busy = true;
	frame.src = url;
	setTimeout(function () {
		frame._busy = false;
	}, 0);
};

var onResumeEventListener = [];

var onResume = function (callback) {
	onResumeEventListener = [callback];
};

JsBridge.onResume = onResume;

JsBridge.addResumeEventListener = function (callback) {
	var index = onResumeEventListener.indexOf(callback);
	if (index < 0) {
		onResumeEventListener.push(callback);
	}
};

JsBridge.removeResumeEventListener = function (callback) {
	var index = onResumeEventListener.indexOf(callback);
	if (index >= 0) {
		onResumeEventListener = onResumeEventListener.slice(0, index).concat(onResumeEventListener.slice(index + 1));
	}
};

JsBridge._onResume = function () {
	if (JsBridge.onResume != onResume) {
		typeof JsBridge.onResume == "function" ? JsBridge.onResume() : (onResumeEventListener = []);
	} else {
		onResumeEventListener.slice(0).forEach(function (callback) {
			callback();
		});
	}
};

var onPauseEventListener = [];

var onPause = function (callback) {
	onPauseEventListener = [callback];
};

JsBridge.onPause = onPause;

JsBridge.addPauseEventListener = function (callback) {
	var index = onPauseEventListener.indexOf(callback);
	if (index < 0) {
		onPauseEventListener.push(callback);
	}
};

JsBridge.removePauseEventListener = function (callback) {
	var index = onPauseEventListener.indexOf(callback);
	if (index >= 0) {
		onPauseEventListener = onPauseEventListener.slice(0, index).concat(onPauseEventListener.slice(index + 1));
	}
};

JsBridge._onPause = function () {
	if (JsBridge.onPause != onPause) {
		typeof JsBridge.onPause == "function" ? JsBridge.onPause() : (onPauseEventListener = []);
	} else {
		onPauseEventListener.slice(0).forEach(function (callback) {
			callback();
		});
	}
};

JsBridge.ready = false;
var poolOnReady = [];

JsBridge.onReady = function (onReady) {
	if (JsBridge.ready) {
		onReady && onReady();
	} else if (onReady) {
		poolOnReady.push(onReady);
	}
};

JsBridge._readyCallback = function () {
	if (JsBridge.ready) {
		return;
	}
	console.log("bridge ready"); // @debug
	JsBridge.ready = true;
	poolOnReady.slice(0).forEach(function (callback) {
		callback();
	});
};

JsBridge._coreReadyCallback = JsBridge._readyCallback;
;
	JsBridge.SCENE_NONE = 0;
JsBridge.SCENE_DOWNLOADER = 1;
JsBridge.SCENE_DOWNLOADER_DETAIL = 2 | JsBridge.SCENE_DOWNLOADER;
JsBridge.SCENE_DOWNLOADER_EXTERNAL = 4 | JsBridge.SCENE_DOWNLOADER;
JsBridge.SCENE_DOWNLOADER_SDK = 8 | JsBridge.SCENE_DOWNLOADER;
JsBridge.SCENE_MOBILEQ = 0x10;
JsBridge.SCENE_WECHAT = 0x20;
JsBridge.SCENE_YUNOS = 0x30;
JsBridge.SCENE_QQMUSIC = 0x40; // qq音乐

/* 手机助手 */
JsBridge.SCENE_SZUSE = 0x50;

/* 腾讯视频 */
JsBridge.SCENE_VIDEO = 0x100;

// 手管系
JsBridge.SCENE_WESECURE_SERIES = 0x2000;
// 手管
JsBridge.SCENE_WESECURE = 0x2200;
// wifi管家
JsBridge.SCENE_WIFIMANAGER = 0x2300;
// wifi管家, 支持下载接口
JsBridge.SCENE_WIFIMANAGER_SUPPORT_DOWNLOAD = 0x2400;
// 同步助手
JsBridge.SCENE_QQPIM = 0x2500;
// 同步助手, 支持下载接口
JsBridge.SCENE_QQPIM_SUPPORT_DOWNLOAD = 0x2600;
JsBridge.SCENE_QQPIM_SUPPORT_DOWNLOAD_V2 = 0x2700; // 增强版，增加暂停及进度等接口
// 游戏YSDK
JsBridge.SCENE_YSDK = 0x2800;

JsBridge.SCENE = JsBridge.SCENE_NONE;
;
	JsBridge._greaterThanOrEqual = function (value1, value2) {
	value1 = String(value1).split(".");
	value2 = String(value2).split(".");
	try {
		for (var i = 0, len = Math.max(value1.length, value2.length) ; i < len; i++) {
			var v1 = isFinite(value1[i]) && Number(value1[i]) || 0, v2 = isFinite(value2[i]) && Number(value2[i]) || 0;
			if (v1 < v2) {
				return false;
			} else if (v1 > v2) {
				return true;
			}
		}
	} catch (_) {
		return false;
	}
	return true;
};
;
	return JsBridge;
}(window.JsBridge, window);
// JsBridge YSDK模块
(function (JsBridge, win, undefined) {
  if (!JsBridge) {
    return;
  }

  if (JsBridge.SCENE === JsBridge.SCENE_NONE) {
  var match = navigator.userAgent.match(/\/GameIcon\//);
  if (match) {
    JsBridge.SCENE = JsBridge.SCENE_YSDK;
  }
}
;

  if (!(JsBridge.SCENE & JsBridge.SCENE_YSDK)) {
    return;
  }

  // JsBridge 核心框架YSDK模块（跟应用宝基本一致）
JsBridge.allowBatchCall = true;

var seq = 1;
var map = {};
var pool = [];
var timer = 0;

function callSingle(name, args, callback, callbackChain) {
  var url = ["jsb:/", name, seq, "JsBridge.callback?"].join("/"), params = [];
  for (var key in args) {
    params.push(encodeURIComponent(key) + "=" + encodeURIComponent(args[key] + ""));
  }
  url += params.join("&");
  map[seq++] = {
    callback: callback,
    callbackChain: callbackChain
  };
  JsBridge._callWithScheme(url);
}

function callBatch() {
  timer = 0;
  if (pool.length == 1) {
    var one = pool[0];
    callSingle(one.name, one.args, one.callback, one.callbackChain);
  } else {
    var params = [];
    for (var i = 0, one; one = pool[i]; i++) {
      if (one.args) {  // 终端使用 args 前，把 args 里的 value 都 decodeURIComponent 了一次，所以这里要加上这个逻辑兼容
        for (var p in one.args) {
          if (one.args.hasOwnProperty(p)) {
            if (one.args[p]) {
              one.args[p] = encodeURIComponent(one.args[p]);
            }
          }
        }
      }
      params.push({
        method: one.name,
        seqid: seq,
        args: one.args,
        callback: "JsBridge.callback"
      });
      map[seq++] = {
        callback: one.callback
      };
    }
    var url = ["jsb://callBatch", seq++, "JsBridge.callback?param="].join("/")
    url += encodeURIComponent(JSON.stringify(params));
    JsBridge._callWithScheme(url);
  }
  pool = [];
}

var call = JsBridge._call = JsBridge.call = function (name, args, callback) {
  console.log("JsBridge._call: ", name, args); // @debug
  args = args || {};
  var callbackChain = [].slice.call(arguments, 3);
  if (JsBridge.allowBatchCall) {
    pool.push({
      name: name,
      args: args,
      callback: callback,
      callbackChain: callbackChain
    });
    !timer && (timer = setTimeout(callBatch, 0));
  } else {
    callSingle(name, args, callback, callbackChain);
  }
};

JsBridge.multiCall = function (args, callback) {
  console.log("JsBridge.multiCall: ", args); // @debug
  var params = [], callbackParams = {
    callback: callback,
    callbackChain: [].slice.call(arguments, 2),
    count: 0,
    result: {}
  };
  for (var alias in args) {
    var one = args[alias];
    params.push({
      method: one.name,
      seqid: seq,
      args: one.args || {},
      callback: "JsBridge.callback"
    });
    map[seq++] = {
      callback: JsBridge._createMultiCallback(callbackParams, alias)
    };
    callbackParams.count++;
  }
  if (callbackParams.count == 0) {
    return;
  }
  var url = ["jsb://callBatch", seq++, "JsBridge.callback?param="].join("/")
  url += encodeURIComponent(JSON.stringify(params));
  JsBridge._callWithScheme(url);
};

JsBridge.callback = function (args) {
  console.log("JsBridge.callback: ", args); // @debug
  var one, res, callbackChain;
  if (map[args.seqid]) {
    one = map[args.seqid];
    callbackChain = one.callbackChain;
    res = one.callback && one.callback(args);
    delete map[args.seqid];
  }
  if (res && typeof res == "object") {
    call.apply(null, [res.name, res.args || {}].concat(callbackChain || []));
  }
};

win.activityStateCallback = function (res) {
  if (res.data == "onResume") {
    JsBridge._onResume();
  } else if (res.data == "onPause") {
    JsBridge._onPause();
  }
};

win.readyCallback = JsBridge._coreReadyCallback;
JsBridge.__onReady = JsBridge.onReady;
JsBridge.onReady = function (onReady) {
  if (!JsBridge.ready && !JsBridge.onReady.called) {
    JsBridge.onReady.called = 1;
    JsBridge.call("isInterfaceReady", {}, function (ret) {
      console.log("isInterfaceReady:", JSON.parse(ret.data)); // @debug
    });
  }
  JsBridge.__onReady(onReady);
};
;
  // JsBridge 完整框架通用模块
JsBridge.SHARE_USER_SELECTION = 0;
JsBridge.SHARE_MOBILEQ = 1;
JsBridge.SAHRE_QZONE = 2
JsBridge.SAHRE_WECHAT = 3;
JsBridge.SAHRE_WECHAT_TIMELINE = 4;
JsBridge.SHARE_USER_SELECTION_POPUP = 5;

JsBridge._shareInfo = {
	iconUrl: "",
	jumpUrl: location.href,
	title: document.title,
	summary: location.href,
	message: "",
	appBarInfo: "",
	contentType: 0, //分享类型：0:默认  1：音乐  2：视频
	dataUrl: "" //音乐或者视频的地址
};

JsBridge.setShareInfo = function (args) {
	args = args || {};
	if (args.allowShare == 1 || args.allowShare === true) {
		JsBridge._showShareButton && JsBridge._showShareButton();
	} else if (args.allowShare == 0 || args.allowShare === false) {
		JsBridge._hideShareButton && JsBridge._hideShareButton();
	}
	var shareInfo = JsBridge._shareInfo;
	shareInfo.iconUrl = args.iconUrl || shareInfo.iconUrl;
	shareInfo.jumpUrl = args.jumpUrl || shareInfo.jumpUrl;
	shareInfo.title = args.title || shareInfo.title;
	shareInfo.summary = args.summary || shareInfo.summary;
	shareInfo.message = args.message || shareInfo.message;
	shareInfo.appBarInfo = args.appBarInfo || shareInfo.appBarInfo;
	shareInfo.contentType = args.contentType || shareInfo.contentType;
	shareInfo.dataUrl = args.dataUrl || shareInfo.dataUrl;
	shareInfo.back = args.back || false;
	shareInfo.scene = args.scene || ""
	shareInfo.sourceName = args.sourceName || "腾讯应用宝"
	shareInfo.callback = args.callback || function() {}
	JsBridge._setShareInfo && JsBridge._setShareInfo(args);
};

JsBridge.getCookie = function (name) {
	var r = new RegExp("(?:^|;+|\\s+)" + name + "=([^;]*)"), m = document.cookie.match(r);
	return !m ? "" : m[1];
};

JsBridge.setCookie = function (name, value, domain, path, hour) {
	if (hour) {
		var expire = new Date();
		expire.setTime(expire.getTime() + 3600000 * hour);
	}
	document.cookie = name + "=" + value + "; " + (hour ? "expires=" + expire.toGMTString() + "; " : "") + (path ? "path=" + path + "; " : "") + (domain ? "domain=" + domain + ";" : "");
};

JsBridge.delCookie = function(name, domain, path) {
	document.cookie = name + "=; expires=Mon, 26 Jul 1997 05:00:00 GMT; " + (path ? "path=" + path + "; " : "") + (domain ? "domain=" + domain + ";" : "");
};

// 手Q登录相关功能
JsBridge.getLoginUin = function () {
	var uin = JsBridge.getCookie("uin");
	if (!uin) {
		return 0;
	}
	uin = /^o(\d+)$/.exec(uin);
	if (uin && (uin = new Number(uin[1]) + 0) > 10000) {
		return uin;
	}
	return 0;
};

JsBridge.getLoginSkey = function () {
	return JsBridge.getCookie("skey");
};

JsBridge.getLoginVkey = function () {
	return JsBridge.getCookie("vkey");
};
;
  // JsBridge 完整框架YSDK模块
JsBridge.LOGIN_TYPE_MOBILEQ_QUICK = "QMOBILEQ"; // only for login
JsBridge.LOGIN_TYPE_MOBILEQ = "MOBILEQ";
JsBridge.LOGIN_TYPE_WECHAT = "WX";
JsBridge.LOGIN_TYPE_USER_SELECTION = "DEFAULT"; // only for login
JsBridge.LOGIN_TYPE_NONE = "NONE"; // only for getLoginType

JsBridge.logout = function () {
	JsBridge.setCookie("logintype", JsBridge.LOGIN_TYPE_NONE, "qq.com", "/");
	JsBridge.delCookie("openid", "qq.com", "/");
	JsBridge.delCookie("accesstoken", "qq.com", "/");
	JsBridge.delCookie("qopenid", "qq.com", "/");
	JsBridge.delCookie("qaccesstoken", "qq.com", "/");
	JsBridge.delCookie("openappid", "qq.com", "/");
	JsBridge.delCookie("uin", "qq.com", "/");
	JsBridge.delCookie("skey", "qq.com", "/");
	JsBridge.delCookie("sid", "qq.com", "/");
	JsBridge.delCookie("vkey", "qq.com", "/");
};

JsBridge.getLoginType = function () {
	return JsBridge.getCookie("logintype") || JsBridge.LOGIN_TYPE_NONE;
};

JsBridge.getLoginOpenId = function () {
	return JsBridge.getCookie("openid");
};

JsBridge.getLoginAccessToken = function () {
	return JsBridge.getCookie("accesstoken");
};
;
})(window.JsBridge, window);
