﻿// JsBridge 通用模块
window.JsBridge = function ( JsBridge, win, undefined )
{
	// JsBridge 核心框架通用模块
	JsBridge = JsBridge || {};

	JsBridge.iOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
	if (JsBridge.iOS && navigator.userAgent.indexOf("Android") >= 0) {
		// 同时是 iOS 和 Android，那就说明不是 iOS
		JsBridge.iOS = false;
	}

	var framesPool = [];

	function createNewFrame() {
		var frame = document.createElement("iframe");
		framesPool.push(frame);
		frame.style.cssText = "position:absolute;left:0;top:0;width:0;height:0;visibility:hidden;";
		frame.frameBorder = "0";
		document.body.appendChild(frame);
		return frame;
	}

	JsBridge._createMultiCallback = function (callbackParams, alias) {
		return function (res) {
			callbackParams.result[alias] = res;
			callbackParams.count--;
			if (callbackParams.count == 0 && callbackParams.callback) {
				res = callbackParams.callback(callbackParams.result);
				if (res && typeof res == "object") {
					JsBridge.multiCall.apply(JsBridge, [res].concat(callbackParams.callbackChain));
				}
			}
		};
	};

	JsBridge._callWithScheme = JsBridge.callWithScheme = function (url) {
		console.log("JsBridge.callWithScheme: ", url); // @debug
		var frame;
		for (var i = 0; frame = framesPool[i]; i++) {
			if (!frame._busy) {
				break;
			}
		}
		if (!frame || frame._busy) {
			frame = createNewFrame();
		}
		frame._busy = true;
		frame.src = url;
		setTimeout(function () {
			frame._busy = false;
		}, 0);
	};

	var onResumeEventListener = [];

	var onResume = function (callback) {
		onResumeEventListener = [callback];
	};

	JsBridge.onResume = onResume;

	JsBridge.addResumeEventListener = function (callback) {
		var index = onResumeEventListener.indexOf(callback);
		if (index < 0) {
			onResumeEventListener.push(callback);
		}
	};

	JsBridge.removeResumeEventListener = function (callback) {
		var index = onResumeEventListener.indexOf(callback);
		if (index >= 0) {
			onResumeEventListener = onResumeEventListener.slice(0, index).concat(onResumeEventListener.slice(index + 1));
		}
	};

	JsBridge._onResume = function () {
		if (JsBridge.onResume != onResume) {
			typeof JsBridge.onResume == "function" ? JsBridge.onResume() : (onResumeEventListener = []);
		} else {
			onResumeEventListener.slice(0).forEach(function (callback) {
				callback();
			});
		}
	};

	var onPauseEventListener = [];

	var onPause = function (callback) {
		onPauseEventListener = [callback];
	};

	JsBridge.onPause = onPause;

	JsBridge.addPauseEventListener = function (callback) {
		var index = onPauseEventListener.indexOf(callback);
		if (index < 0) {
			onPauseEventListener.push(callback);
		}
	};

	JsBridge.removePauseEventListener = function (callback) {
		var index = onPauseEventListener.indexOf(callback);
		if (index >= 0) {
			onPauseEventListener = onPauseEventListener.slice(0, index).concat(onPauseEventListener.slice(index + 1));
		}
	};

	JsBridge._onPause = function () {
		if (JsBridge.onPause != onPause) {
			typeof JsBridge.onPause == "function" ? JsBridge.onPause() : (onPauseEventListener = []);
		} else {
			onPauseEventListener.slice(0).forEach(function (callback) {
				callback();
			});
		}
	};

	JsBridge.ready = false;
	var poolOnReady = [];

	JsBridge.onReady = function (onReady) {
		if (JsBridge.ready) {
			onReady && onReady();
		} else if (onReady) {
			poolOnReady.push(onReady);
		}
	};

	JsBridge._readyCallback = function () {
		if (JsBridge.ready) {
			return;
		}
		console.log("bridge ready"); // @debug
		JsBridge.ready = true;
		poolOnReady.slice(0).forEach(function (callback) {
			callback();
		});
	};

	JsBridge._coreReadyCallback = JsBridge._readyCallback;
	;
	// JsBridge 下载框架通用模块
	var Download = function (args, callback, context) {
		if (!(this instanceof Download)) {
			return new Download(args, callback, context);
		}
		this.args = args || {};
		this.callback = callback;
		this.context = context;
		this.identifier = 0;
		this.state = 0;
		this.percentage = 0;
		this.installedVersion = 0;
		this.downloadedVersion = 0;
		this.initializing = true;
		Download._instances[this.args.packageName] = this; // 卸载应用回调只有包名
		Download._instancePool.push(this);
		this._init();
	};

	Download._instances = {};
	Download._instancePool = [];

	Download.reset = function () {
		Download._instances = {};
		Download._instancePool = [];
	};

	Download.HAS_PERCENTAGE = true;

	var installationStatusRetrievedEventListener = [], downloadingStatusRetrievedEventListener = [];

	Download.addInstallationStatusRetrievedEventListener = function (callback) {
		var index = installationStatusRetrievedEventListener.indexOf(callback);
		if (index < 0) {
			installationStatusRetrievedEventListener.push(callback);
		}
	};

	Download.removeInstallationStatusRetrievedEventListener = function (callback) {
		var index = installationStatusRetrievedEventListener.indexOf(callback);
		if (index >= 0) {
			installationStatusRetrievedEventListener = installationStatusRetrievedEventListener.slice(0, index).concat(installationStatusRetrievedEventListener.slice(index + 1));
		}
	};

	Download._dispatchInstallationStatusRetrievedEvent = function (instances) {
		console.log("JsBridge.Download._dispatchInstallationStatusRetrievedEvent", instances); // @debug
		installationStatusRetrievedEventListener.slice(0).forEach(function (callback) {
			callback(instances.slice(0));
		});
	};

	Download.addDownloadingStatusRetrievedEventListener = function (callback) {
		var index = downloadingStatusRetrievedEventListener.indexOf(callback);
		if (index < 0) {
			downloadingStatusRetrievedEventListener.push(callback);
		}
	};

	Download.removeDownloadingStatusRetrievedEventListener = function (callback) {
		var index = downloadingStatusRetrievedEventListener.indexOf(callback);
		if (index >= 0) {
			downloadingStatusRetrievedEventListener = downloadingStatusRetrievedEventListener.slice(0, index).concat(downloadingStatusRetrievedEventListener.slice(index + 1));
		}
	};

	Download._dispatchDownloadingStatusRetrievedEvent = function (instances) {
		console.log("JsBridge.Download._dispatchDownloadingStatusRetrievedEvent", instances); // @debug
		instances.forEach(function (instance) {
			instance.initializing = false;
			if (instance.args.autoStart) {
				switch (instance.state) {
					case JsBridge.Download.STATE_READY:
					case JsBridge.Download.STATE_UPDATE:
					case JsBridge.Download.STATE_PAUSED:
						instance.start();
						break;
					case JsBridge.Download.STATE_DOWNLOADED:
						instance.install();
						break;
				}
			}
		});
		downloadingStatusRetrievedEventListener.slice(0).forEach(function (callback) {
			callback(instances.slice(0));
		});
	};

	Download.STATE_READY = 1;
	Download.STATE_UPDATE = 2;
	Download.STATE_QUEUING = 3;
	Download.STATE_DOWNLOADING = 4;
	Download.STATE_PAUSED = 5;
	Download.STATE_DOWNLOADED = 6;
	Download.STATE_INSTALLING = 7;
	Download.STATE_INSTALLED = 8;

	/*
	switch (state) {
		case JsBridge.Download.STATE_READY:
			break;
		case JsBridge.Download.STATE_UPDATE:
			break;
		case JsBridge.Download.STATE_QUEUING:
			break;
		case JsBridge.Download.STATE_DOWNLOADING:
			break;
		case JsBridge.Download.STATE_PAUSED:
			break;
		case JsBridge.Download.STATE_DOWNLOADED:
			break;
		case JsBridge.Download.STATE_INSTALLING:
			break;
		case JsBridge.Download.STATE_INSTALLED:
			break;
	}
	*/

	Download._getDownloadState = function (state) {
		return Download._stateMap[state];
	};

	Download.prototype._setIdentifier = function (identifier) {
		if (this.identifier) {
			delete Download._instances[this.identifier];
		}
		this.identifier = identifier || 0;
		this.identifier && (Download._instances[this.identifier] = this);
	};

	Download.prototype._callback = function (state, args) {
		if (!state) {
			return;
		}
		if (state == Download.STATE_READY && this.args.checkUpdate && this.installedVersion && (this.args.versionCode > this.installedVersion)) {
			state = Download.STATE_UPDATE;
		}
		// 状态相同，如果有百分比并且是下载中才可以重复回调
		if (this.state == state && state != Download.STATE_PAUSED && (!Download.HAS_PERCENTAGE || state != Download.STATE_DOWNLOADING)) {
			return;
		}
		this.state = state;
		switch (state) {
			case Download.STATE_READY:
			case Download.STATE_UPDATE:
				this.percentage = 0;
				break;
			case Download.STATE_DOWNLOADED:
			case Download.STATE_INSTALLING:
			case Download.STATE_INSTALLED:
				this.percentage = 100;
				break;
		}
		this.callback && this.callback.call(this, this.state, this.percentage, this.context, args || {});
	};

	Download.prototype.doAction = function (callback) {
		if (this.initializing) {
			return;
		}
		switch (this.state) {
			case Download.STATE_QUEUING:
			case Download.STATE_DOWNLOADING:
				if (this.args.preventStopWhenDoAction) {
					break;
				}
				this.stop();
				callback && callback.onStop && callback.onStop.call(this);
				break;
			case Download.STATE_DOWNLOADED:
				this.install();
				callback && callback.onInstall && callback.onInstall.call(this);
				break;
			case Download.STATE_INSTALLED:
				this.open();
				callback && callback.onOpen && callback.onOpen.call(this);
				break;
			case Download.STATE_INSTALLING:
				break;
			default:
				this.start();
				callback && callback.onStart && callback.onStart.call(this);
				break;
		}
	};

	JsBridge.Download = Download;
	;
	JsBridge.SCENE_NONE = 0;
	JsBridge.SCENE_DOWNLOADER = 1;
	JsBridge.SCENE_DOWNLOADER_DETAIL = 2 | JsBridge.SCENE_DOWNLOADER;
	JsBridge.SCENE_DOWNLOADER_EXTERNAL = 4 | JsBridge.SCENE_DOWNLOADER;
	JsBridge.SCENE_DOWNLOADER_SDK = 8 | JsBridge.SCENE_DOWNLOADER;
	JsBridge.SCENE_MOBILEQ = 0x10;
	JsBridge.SCENE_WECHAT = 0x20;
	JsBridge.SCENE_YUNOS = 0x30;
	JsBridge.SCENE_QQMUSIC = 0x40; // qq音乐

	/* 手机助手 */
	JsBridge.SCENE_SZUSE = 0x50;

	/* 腾讯视频 */
	JsBridge.SCENE_VIDEO = 0x100;

	// 手管系
	JsBridge.SCENE_WESECURE_SERIES = 0x2000;
	// 手管
	JsBridge.SCENE_WESECURE = 0x2200;
	// wifi管家
	JsBridge.SCENE_WIFIMANAGER = 0x2300;
	// wifi管家, 支持下载接口
	JsBridge.SCENE_WIFIMANAGER_SUPPORT_DOWNLOAD = 0x2400;
	// 同步助手
	JsBridge.SCENE_QQPIM = 0x2500;
	// 同步助手, 支持下载接口
	JsBridge.SCENE_QQPIM_SUPPORT_DOWNLOAD = 0x2600;
	JsBridge.SCENE_QQPIM_SUPPORT_DOWNLOAD_V2 = 0x2700; // 增强版，增加暂停及进度等接口

	JsBridge.SCENE = JsBridge.SCENE_NONE;
	;

	// JsBridge 完整框架通用模块
	JsBridge.SHARE_USER_SELECTION = 0;
	JsBridge.SHARE_MOBILEQ = 1;
	JsBridge.SAHRE_QZONE = 2
	JsBridge.SAHRE_WECHAT = 3;
	JsBridge.SAHRE_WECHAT_TIMELINE = 4;
	JsBridge.SHARE_USER_SELECTION_POPUP = 5;

	JsBridge._shareInfo = {
		iconUrl: "",
		jumpUrl: location.href,
		title: document.title,
		summary: location.href,
		message: "",
		appBarInfo: "",
		contentType: 0, //分享类型：0:默认  1：音乐  2：视频
		dataUrl: "" //音乐或者视频的地址
	};

	JsBridge.setShareInfo = function (args) {
		args = args || {};
		if (args.allowShare == 1 || args.allowShare === true) {
			JsBridge._showShareButton && JsBridge._showShareButton();
		} else if (args.allowShare == 0 || args.allowShare === false) {
			JsBridge._hideShareButton && JsBridge._hideShareButton();
		}
		var shareInfo = JsBridge._shareInfo;
		shareInfo.iconUrl = args.iconUrl || shareInfo.iconUrl;
		shareInfo.jumpUrl = args.jumpUrl || shareInfo.jumpUrl;
		shareInfo.title = args.title || shareInfo.title;
		shareInfo.summary = args.summary || shareInfo.summary;
		shareInfo.message = args.message || shareInfo.message;
		shareInfo.appBarInfo = args.appBarInfo || shareInfo.appBarInfo;
		shareInfo.contentType = args.contentType || shareInfo.contentType;
		shareInfo.dataUrl = args.dataUrl || shareInfo.dataUrl;
		JsBridge._setShareInfo && JsBridge._setShareInfo(args);
	};

	JsBridge.getCookie = function (name) {
		var r = new RegExp("(?:^|;+|\\s+)" + name + "=([^;]*)"), m = document.cookie.match(r);
		return !m ? "" : m[1];
	};

	JsBridge.setCookie = function (name, value, domain, path, hour) {
		if (hour) {
			var expire = new Date();
			expire.setTime(expire.getTime() + 3600000 * hour);
		}
		document.cookie = name + "=" + value + "; " + (hour ? "expires=" + expire.toGMTString() + "; " : "") + (path ? "path=" + path + "; " : "") + (domain ? "domain=" + domain + ";" : "");
	};

	JsBridge.delCookie = function (name, domain, path) {
		document.cookie = name + "=; expires=Mon, 26 Jul 1997 05:00:00 GMT; " + (path ? "path=" + path + "; " : "") + (domain ? "domain=" + domain + ";" : "");
	};

	// 手Q登录相关功能
	JsBridge.getLoginUin = function () {
		var uin = JsBridge.getCookie("uin");
		if (!uin) {
			return 0;
		}
		uin = /^o(\d+)$/.exec(uin);
		if (uin && (uin = new Number(uin[1]) + 0) > 10000) {
			return uin;
		}
		return 0;
	};

	JsBridge.getLoginSkey = function () {
		return JsBridge.getCookie("skey");
	};

	JsBridge.getLoginVkey = function () {
		return JsBridge.getCookie("vkey");
	};
	;

	JsBridge._greaterThanOrEqual = function (value1, value2) {
		value1 = String(value1).split(".");
		value2 = String(value2).split(".");
		try {
			for (var i = 0, len = Math.max(value1.length, value2.length); i < len; i++) {
				var v1 = isFinite(value1[i]) && Number(value1[i]) || 0, v2 = isFinite(value2[i]) && Number(value2[i]) || 0;
				if (v1 < v2) {
					return false;
				} else if (v1 > v2) {
					return true;
				}
			}
		} catch (_) {
			return false;
		}
		return true;
	};
	;

	return JsBridge;
}( window.JsBridge, window );
// JsBridge 手Q模块
( function ( JsBridge, win, undefined )
{
	if ( !JsBridge )
	{
		return;
	}

	if (JsBridge.SCENE == JsBridge.SCENE_NONE) {
		var match = navigator.userAgent.match(/(?:\bV1_AND_SQI?_|QQ\/)([\d\.]+)/);
		if (match && JsBridge._greaterThanOrEqual(match[1], "4.6")) {
			JsBridge.SCENE = JsBridge.SCENE_MOBILEQ;
		}
	}
	;

	if (!(JsBridge.SCENE & JsBridge.SCENE_MOBILEQ)) {
		return;
	}

	JsBridge._coreReadyCallback = function () { };

	// JsBridge 核心框架手Q模块
	JsBridge.iOS = /(iPad|iPhone|iPod).*? (IPad)?QQ\/([\d\.]+)/.test(navigator.userAgent);
	if (JsBridge.iOS && /\bV1_AND_SQI?_([\d\.]+)(.*? QQ\/([\d\.]+))?/.test(navigator.userAgent)) {
		// 同时是 iOS 和 Android，那就说明不是 iOS
		JsBridge.iOS = false;
	}
	JsBridge.isPublicAccount = JsBridge.iOS || navigator.userAgent.indexOf("Agent/") < 0 && navigator.userAgent.indexOf("V1_AND_SQ") >= 0; // iOS 下均为公众账号，安卓下没有 Agent 但是有 V1_AND_SQ 才是公众账号，有 Agent 一定是应用中心，有 V1_AND_SQ 不一定，因为新版应用中心也有这个了

	var seq = 1;
	var map = {};

	var call = JsBridge._call = JsBridge.call = function (name) {
		var args = [], callbackChain = [], chain = false;
		[].slice.call(arguments, 1).forEach(function (arg) {
			if (typeof arg == "function") {
				callbackChain.push(arg);
				chain = true;
			} else if (!chain) {
				args.push(arg);
			}
		});
		console.log.apply(console, ["JsBridge._call: ", name].concat(args)); // @debug
		var url = ["jsbridge:/", name];
		if (JsBridge.iOS) {
			url = url.join("/");
			args.forEach(function (arg, i) {
				url += (i == 0 ? "?p=" : ("&p" + i + "=")) + encodeURIComponent(typeof arg == "object" ? JSON.stringify(arg) : arg + "");
			});
			url += "#" + seq;
		} else {
			url.push(seq);
			args.forEach(function (arg) {
				url.push(encodeURIComponent(typeof arg == "object" ? JSON.stringify(arg) : arg + ""));
			});
			url = url.join("/");
		}
		map[seq++] = {
			callback: callbackChain[0],
			callbackChain: callbackChain.slice(1)
		};
		JsBridge._callWithScheme(url);
	};

	JsBridge.multiCall = function (args, callback) {
		console.log("JsBridge.multiCall: ", args); // @debug
		var callbackParams = {
			callback: callback,
			callbackChain: [].slice.call(arguments, 2),
			count: 0,
			result: {}
		};
		for (var alias in args) {
			var one = args[alias];
			callbackParams.count++;
			call.apply(null, [one.name].concat(one.args || []).concat([JsBridge._createMultiCallback(callbackParams, alias)]));
		}
	};

	JsBridge.callback = function (key, args) {
		console.log("JsBridge.callback: ", key, args); // @debug
		if (key == "resume") {
			JsBridge._onResume();
			return;
		}
		var one, res, callbackChain;
		if (args && args.r == 0) {
			if (isFinite(key)) {
				if (map[key]) {
					one = map[key];
					callbackChain = one.callbackChain;
					res = one.callback && one.callback(args.result);
					delete map[key];
				}
			} else if (args.guid && map[args.guid]) {
				one = map[args.guid];
				callbackChain = one.callbackChain;
				res = one.callback && one.callback(args.data);
				delete map[args.guid];
			}
		}
		if (res && typeof res == "object") {
			call.apply(null, [res.name].concat(res.args || []).concat(callbackChain || []));
		}
	};

	win.QzoneApp = {
		fire: function (key, args) {
			JsBridge.callback(key, args);
		}
	};

	JsBridge._coreReadyCallback();
	;
	// JsBridge 下载框架手Q模块
	if (JsBridge.iOS) {
		delete JsBridge.Download;
	} else {
		var Download = JsBridge.Download;

		Download._stateMap = {
			20: Download.STATE_QUEUING,
			2: Download.STATE_DOWNLOADING,
			12: Download.STATE_DOWNLOADING,
			3: Download.STATE_PAUSED,
			4: Download.STATE_DOWNLOADED,
			5: Download.STATE_INSTALLING,
			6: Download.STATE_INSTALLED,
			13: Download.STATE_INSTALLED,
			10: Download.STATE_READY // 取消回调
		};
		Download.UITYPE_WISE_NO_WIFI_BOOKING_DOWNLOAD = "NO_SUPPORT";
		function _getDownloadKey(one) {
			return one.appid || one.appId
		};
		Download.prototype._getDownloadKey = function () {
			return this.args.hnAppId;
		};
		Download.prototype._getDownloadParam = function (action) {
			return {
				versionCode: this.args.versionCode || "",
				myAppConfig: this.args.myAppConfig || "0",
				appId: this.args.hnAppId,
				url: this.args.url,
				packageName: this.args.packageName,
				actionCode: action,
				toPageType: (this.args.yybUrl && !window.hasMyapp) ? '-1' : '0', // TODO. 判断应用宝是否安装有点耦合
				appName: this.args.alias || this.args.appName || '',
				isAutoDownload: typeof this.args.isAutoDownload !== "undefined" ? this.args.isAutoDownload : 1,
				isAutoInstall: typeof isAutoInstall !== "undefined" ? this.args.isAutoInstall : 1,
				via: this.args.via || "",
				auto_start_yyb_download: this.args.auto_start_yyb_download,
				channelId: this.args.channelId || "",
				apkId: this.args.apkId || "",
				recommendId: this.args.recommendId || "",
				sourceFromServer: this.args.source || "",
				pageId: this.args.pageId || "",
				moduleId: this.args.moduleId || "",
				positionId: this.args.positionId || "",
				extraData: this.args.extraData || "",
				yybUrl: this.args.yybUrl || '',
			};
		};

		Download.prototype.appendArgs = function (args) {
			Object.assign(this.args, args);
		};

		var downloaderQueryTimer = 0;
		var downloaderQueryPool = [];
		var pendingQueryDownloadActions = [];
		var downloaderQueryBatch = function () {
			downloaderQueryTimer = 0;
			var privatePool = downloaderQueryPool;
			downloaderQueryPool = [];

			var multiCallParams = {};
			privatePool.forEach(function (instance) {
				multiCallParams[instance.args.packageName] = {
					name: "qqZoneAppList/getAppVersionCode",
					args: {
						packageName: instance.args.packageName,
						downloadUrl: ""
					}
				};
			});
			JsBridge.multiCall(multiCallParams, function (res) {
				var param = [];
				privatePool.forEach(function (instance) {
					var version = JSON.parse(res[instance.args.packageName].replace(/downloadedVersionCode /g, "downloadedVersionCode"));
					if (version) {
						if (version.installedVersionCode) {
							instance.installedVersion = version.installedVersionCode;
							if (!instance.args.checkUpdate || !instance.args.versionCode || instance.args.versionCode <= instance.installedVersion) {
								instance._callback(Download.STATE_INSTALLED);
								return;
							}
						}
						if (version.downloadedVersionCode) {
							instance.downloadedVersion = version.downloadedVersionCode;
						}
					}
					param.push({
						appid: instance.args.hnAppId,
						packageName: instance.args.packageName
					});
				});
				Download._dispatchInstallationStatusRetrievedEvent(privatePool);
				if (param.length == 0) {
					// 全都安装了，不需要再查询下载状态了
					Download._dispatchDownloadingStatusRetrievedEvent(privatePool);
					return;
				}
				pendingQueryDownloadActions.push(privatePool);
				call("q_download_v2/getQueryDownloadAction", param);
				// call("q_download/getQueryDownloadAction", JsBridge.isPublicAccount ? {
				// 	guid: 0,
				// 	infolist: param
				// } : param);
			});
		};

		Download.prototype._init = function () {
			Download._instances[this._getDownloadKey()] = this;
			this._callback(Download.STATE_READY);
			downloaderQueryPool.push(this);
			!downloaderQueryTimer && (downloaderQueryTimer = setTimeout(downloaderQueryBatch, 0));
		};

		Download.prototype.start = function () {
			if (this.initializing) {
				return;
			}
			switch (this.state) {
				case Download.STATE_READY:
				case Download.STATE_UPDATE:
					// 更新/下载
					call("q_download_v2/doDownloadAction", this._getDownloadParam(this.installedVersion ? 12 : 2)); // 如果本地已安装，则一定是可升级的场景才会走到这里
					break;
				case Download.STATE_PAUSED:
					// 下载（继续）
					call("q_download_v2/doDownloadAction", this._getDownloadParam(2));
					break;
			}
		};

		Download.prototype.stop = function ()
		{
			if ( this.initializing )
			{
				return;
			}
			// 暂停
			call( "q_download_v2/doDownloadAction", this._getDownloadParam( 3 ) );
		};

		Download.prototype.install = function ()
		{
			if ( this.initializing )
			{
				return;
			}
			// 安装
			call( "q_download_v2/doDownloadAction", this._getDownloadParam( 5 ) );
		};

		Download.prototype.open = function ()
		{
			if ( this.initializing )
			{
				return;
			}
			JsBridge.startApp( this.args.packageName );
		};

		function _downloadCallback ( res )
		{
			if ( Object.prototype.toString.call( res ) == "[object Array]" )
			{
				var processed = {};
				for ( var i = 0, one; one = res[ i ]; i++ )
				{
					_downloadCallback( one );
					var instance = Download._instances[ _getDownloadKey( one ) ];
					instance && ( processed[ instance._getDownloadKey() ] = 1 );
				}
				var instances = pendingQueryDownloadActions.shift();
				if ( instances )
				{
					instances.forEach( function ( instance )
					{
						if ( !processed[ instance._getDownloadKey() ] && instance.state != Download.STATE_INSTALLED )
						{
							instance._callback( Download.STATE_READY );
						}
					} );
					Download._dispatchDownloadingStatusRetrievedEvent( instances );
				}
				return;
			}
			var instance = Download._instances[ _getDownloadKey( res ) ];
			if ( !instance )
			{
				return;
			}
			if ( res.state == 9 )
			{
				// 卸载回调
				JsBridge.getAppInstalledVersion( instance.args.packageName, function ( res )
				{
					instance.installedVersion = res[ instance.args.packageName ] || 0;
					instance._callback( Download.STATE_READY );
				} );
				return;
			}
			var state = Download._getDownloadState( res.state );
			if ( instance.downloadedVersion && instance.args.versionCode && instance.args.versionCode > instance.downloadedVersion && state == Download.STATE_DOWNLOADED )
			{
				// 下载了旧版，当作没下载
				state = Download.STATE_READY;
			}
			res.pro && ( instance.percentage = res.pro );
			instance._callback( state );
		}

		Download.callback = function ( res )
		{
			console.log( "JsBridge.Download.callback", res ); // @debug
			_downloadCallback( res );
		};

		win.publicAccountDownload = {
			queryProcess: function ( res )
			{
				_downloadCallback( res );
			}
		};

		JsBridge.onReady( function ()
		{
			call( "q_download_v2/registerDownloadCallBackListener", "JsBridge.Download.callback" );
		} );

		JsBridge.getAppInstalledVersion = function ( packageNames, callback )
		{
			if ( {}.toString.call( packageNames ) != "[object Array]" )
			{
				return JsBridge.getAppInstalledVersion( [ packageNames ], callback );
			}
			call( "qzone_app/getAppInfoBatch", packageNames.join( "," ), ",", false, false, false, function ( res )
			{
				res = res && JSON.parse( res.data || res ) || [];
				var result = {};
				for ( var i = 0, one; one = res[ i ]; i++ )
				{
					result[ one[ 0 ] ] = one[ 1 ];
				}
				callback && callback( result ); // 返回已安装的应用版本号
			} );
		};

		JsBridge.startApp = function ( packageName )
		{
			call( "qqZoneAppList/startApp", packageName, "" );
		};

		JsBridge.setDownloaderFirstOpenPage = function ( url )
		{
			call( "q_download_v2/setDownloaderFirstOpenPage", {
				url: url
			} );
		};

		var originalJsBridgeCallback = JsBridge.callback;

		JsBridge.callback = function ( key, args )
		{
			console.log( "JsBridge.callback: ", key, args );
			if ( key == "interface.getQueryDownloadAction" )
			{ // 手Q WebView（公众账号）的 getQueryDownloadAction 不会走 key 为数字的分支，而是走手Q应用中心的回调方式
				console.log( "JsBridge.callback: ", key, args ); // @debug
				args.r == 0 && _downloadCallback( args.data );
			} else
			{
				originalJsBridgeCallback( key, args );
			}
		};

		JsBridge.getAllSDKDownload = function ( cb )
		{
			call( "q_download_v2/getAllSDKDownloadInfos", function ( res )
			{
				cb && cb( res );
			} );
		}
		JsBridge.queryInstalledApp = function ( cb )
		{
			call( "q_download_v2/queryInstalledAppInfo", function ( res )
			{
				cb && cb( res );
			} );
		}
	}


	// 手Q应用中心新增
	JsBridge.openQQBrowserActivity = function ( url )
	{
		call( 'q_appcenter/openQQBrowserActivity', { url: url } );
	};
	JsBridge.openAppCenterBrowser = function ( url )
	{
		call( 'q_appcenter/openAppCenterBrowser', { url: url } );
	};
	JsBridge.setAppCenterTitle = function ( title, back )
	{
		const obj = { title: title }
		if ( back )
		{
			obj.back = back // 左侧返回箭头旁的文案，默认显示返回或者动态
		}
		call( 'q_appcenter/setTitleBarText', obj );
	};
	JsBridge.hideSearchIcon = function ()
	{
		call( 'q_appcenter/hideSearchIcon' );
	};
	// 跳转应用宝，如果跳转失败会跳转到手Q应用中心详情页
	JsBridge.jumpToYYBAppDetail = function ( opt )
	{
		call( 'q_appcenter/jumpToYYBAppDetail', opt );
	};

	// JsBridge 完整框架手Q模块
	JsBridge._setShareInfo = function ()
	{
		win.shareCallback = function ( type )
		{
			JsBridge.share( parseInt( type ) + 1 );
		};
		call( JsBridge.iOS ? "nav/addWebShareListener" : "ui/setOnShareHandler", {
			callback: "shareCallback"
		} );
		delete JsBridge._setShareInfo;
	};

	JsBridge.share = function ( type )
	{
		type = type || JsBridge.SHARE_USER_SELECTION;
		var shareInfo = JsBridge._shareInfo;
		var params = {
			title: shareInfo.title,
			desc: shareInfo.summary,
			share_url: shareInfo.jumpUrl,
			image_url: shareInfo.iconUrl
		};
		if ( params.share_url )
		{
			params.share_url = params.share_url.replace( /(\\?|#|&)(?:sid|3g_sid)=(?:[^&#?]*)(\\?|$|&|#)/i, "$1$2" );
		}
		if ( params.desc )
		{
			params.desc = params.desc.length > 50 ? ( params.desc.substr( 0, 50 ) + "..." ) : params.desc;
		}
		if ( type == JsBridge.SHARE_USER_SELECTION )
		{
			var cb = function ()
			{
				call( "ui/showShareMenu" );
			};
			JsBridge.iOS ? ( call( "data/setShareInfo", {
				params: params
			} ), cb() ) : call( "QQApi/setShareInfo", params, cb );
		} else
		{
			params.share_type = type - 1;
			JsBridge.iOS ? call( "nav/shareURLWebRichData", params ) : call( "QQApi/shareMsg", params );
		}
	};

	JsBridge.showPictures = function ( urls, current )
	{
		call( "troopNotice/showPicture", {
			imageIDs: urls,
			index: isFinite( current ) && current >= 0 && current < urls.length ? current : 0
		} );
	};

	JsBridge.openNewWindow = function ( url, style, opt )
	{ // 0: 顶部标题栏模式（默认，无底部工具栏）1: 顶部标题栏无分享入口（无底部工具栏）2: 底部工具栏模式（顶部标题依然会存在）3: 底部工具栏模式且顶部无分享入口（顶部标题依然会存在）

		opt = opt || {};
		var isMobileQAppStore = /Agent\/\d+/.test( navigator.userAgent );
		JsBridge.iOS ? call( "nav/openLinkInNewWebView", {
			url: url,
			options: {
				styleCode: {
					1: 4,
					2: 2,
					3: 5
				}[ style ] || 1
			}
		} ) : ( isMobileQAppStore ? call( "qqZoneAppList/goUrl", JSON.stringify( {
			url: url,
			titleInfo: [ {
				name: opt.name || "",
				tipNum: 0 // 默认写0
			} ]
		} ) ) : call( "qbizApi/openLinkInNewWebView", url, style || 0 ) );
	};

	// 获取QQ版本号
	JsBridge.QQVersion = ( function ()
	{
		var qV = navigator.userAgent.match( /QQ\/([\d\.]+)/ )
		if ( qV )
		{
			return qV[ 1 ]
		} else
		{
			return 0
		}
	}() )

	// 上报部分 手Q应用中心有调整 上报地址调整
	var reportPool = [], reportTimer = 0;

	function reportBatch ()
	{
		JsBridge.call( 'qqZoneAppList/reportBatch', reportPool )
		reportPool = [];
		reportTimer = 0;
	}

	// 上报，字段结构示例
	// {
	// 	type: 3003,
	// 	data: {
	// 		pageId: 123,
	// 		appId: 234435
	// 		...
	// 	}
	// }
	JsBridge.report = function ( opt, immediate )
	{
		console.log( "JsBridge.report", opt ); // @debug
		if ( !opt || !opt.data || !opt.data.pageId )
		{
			return
		}
		if ( !opt.data.qqv )
		{
			opt.data.qqv = JsBridge.QQVersion
		}
		if ( !opt.data.time )
		{
			opt.data.time = new Date().getTime()
		}
		if ( !opt.data.uin )
		{
			opt.data.uin = JsBridge.getCookie( 'uin' )
			if ( opt.data.uin )
			{
				opt.data.uin = ( opt.data.uin + '' ).replace( /^o0*/, '' )
			}
		}
		if ( !opt.data.moduleId )
		{
			opt.data.moduleId = '0'
		}
		if ( !opt.data.positionId )
		{
			opt.data.positionId = '0'
		}

		var comKey = [ 'pageId', 'moduleId', 'positionId', 'via', 'uin', 'qqv', 'time', 'extraData' ], appKey = [ 'appName', 'packageName', 'versionCode', 'appId', 'apkId', 'source', 'channelId', 'url', 'recommendId' ]
		var allKey = comKey
		if ( opt.type !== 3001 && opt.type !== '3001' )
		{
			allKey = allKey.concat( appKey )
		}
		if ( opt.type === 3003 || opt.type === '3003' )
		{
			allKey.push( 'action' )
		} else if ( opt.type === 3005 || opt.type === '3005' )
		{
			allKey.push( 'filesize' )
		} else if ( opt.type === 3006 || opt.type === '3006' )
		{
			allKey.push( 'errorCode', 'errorMsg' )
		} else if ( opt.type === 3007 || opt.type === '3007' )
		{
			allKey.push( 'filesize' )
		} else if ( opt.type === 3008 || opt.type === '3008' )
		{
			allKey.push( 'installType', 'filesize' )
		}
		var arr = []
		for ( var i in allKey )
		{
			var value = opt.data[ allKey[ i ] ]
			if ( !value )
			{
				value = ''
			}
			value = ( value + '' ).replace( /|/g, '' )
			arr.push( value )
		}
		var data = arr.join( '|' )
		reportPool.push( {
			type: opt.type,
			data: data
		} );
		if ( immediate )
		{
			reportBatch();
		} else
		{
			!reportTimer && ( reportTimer = setTimeout( reportBatch, 1000 ) );
		}
	};

	JsBridge.reportImmediate = function ( opt )
	{
		JsBridge.report( opt, true );
	};

	win.userFitCallback = function ()
	{
		// 检测身份是否一致用的，暂时用不上，同样是为了避免报错
	};


	JsBridge.freecallback = function ()
	{
		window.free = function ( res )
		{
			window.cardInformation = res || window.cardInformation;
		};

		call( 'yyb_treasureCard/freeTrafficCardModel', 'free', '123' );

	}

	JsBridge.getNetwork = function ()
	{
		call( 'qqZoneAppList/getAPNType', function ( e )
		{
			if (e === null || e === undefined) {
				// callback 数据异常
				console.error('get qqZoneAppList/getAPNType error,', e);
				return;
			}
			window.getNetworkInfo = e;
		} );
	}

	// 自定义 API 页面：http://mqq.oa.com/api/custom.html 可以下载全部 API 的调用方式，然后通过 JsBridge._call 调用，注意区分 iOS 和 Android

	JsBridge._readyCallback();




	;

})(window.JsBridge, window);
