<!-- --moka-component-- [page][page-xxx] --><!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <meta name="format-detection" content="telephone=no">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="description" itemprop="description" content="应用宝">
  <title>腾讯应用宝</title>
  
  
  
  
  
  <style>@charset "utf-8";
html {
  box-sizing: border-box;
}
*,
*:before,
*:after {
  box-sizing: inherit;
}
body,
div,
dl,
dt,
dd,
ul,
ol,
li,
h1,
h2,
h3,
h4,
h5,
h6,
pre,
code,
form,
fieldset,
legend,
input,
textarea,
p,
blockquote,
th,
td,
header,
hgroup,
nav,
section,
article,
aside,
footer,
figure,
figcaption,
menu {
  margin: 0;
  padding: 0;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: normal;
}
body {
  font-family: Roboto, "Droid Sans", "Droid Sans Fallback", "Helvetica Neue", Helvetica, STHeiTi, sans-serif;
  line-height: 24px;
  font-size: 14px;
  color: #000;
  -webkit-user-select: none;
  -webkit-text-size-adjust: none;
  background-color: #f5f5f5;
}
table {
  border-collapse: collapse;
  border-spacing: 0;
}
fieldset,
img {
  display: block;
  border: 0;
}
li {
  list-style: none;
}
input,
textarea,
select {
  font-family: inherit;
  font-size: inherit;
  font-weight: inherit;
}
a,
input {
  text-decoration: none;
  color: #1D82FF;
  -webkit-tap-highlight-color: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
header,
nav,
section,
article,
footer,
figure,
figcaption {
  display: block;
}
:root {
  --moka-length-calc-basic-vw: 1;
  --moka-length-calc-basic-px: 0;
  --m-bvw: 1;
  --m-bpx: 0;
}
@media (min-width: 800px) {
  :root {
    --moka-length-calc-basic-vw: 0;
    --moka-length-calc-basic-px: 1;
    --m-bvw: 0;
    --m-bpx: 1;
  }
}
.one-line {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.mod-dialog {
  display: -webkit-box;
  -webkit-box-align: stretch;
  -webkit-box-pack: center;
  -webkit-box-orient: vertical;
  background: #fff;
  position: absolute;
  top: 50%;
  left: 24px;
  right: 24px;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  width: auto;
  margin: auto;
  z-index: 199;
}
.mod-dialog-header {
  padding: 12px 20px 0;
  height: 36px;
  line-height: 24px;
  color: #000000;
  font-size: 16px;
  position: relative;
}
.mod-dialog-header .mod-close-btn {
  position: absolute;
  top: 18px;
  right: 18px;
  height: 13px;
  width: 13px;
}
.mod-dialog-header .mod-close-btn:after {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  height: 13px;
  width: 13px;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABYAAAAWCAYAAADEtGw7AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyFpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNS1jMDE0IDc5LjE1MTQ4MSwgMjAxMy8wMy8xMy0xMjowOToxNSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIChXaW5kb3dzKSIgeG1wTU06SW5zdGFuY2VJRD0ieG1wLmlpZDpDNEIxQzA0RDcyNTYxMUU1QjIxMjgxRTMwQ0ExMTc0QSIgeG1wTU06RG9jdW1lbnRJRD0ieG1wLmRpZDpDNEIxQzA0RTcyNTYxMUU1QjIxMjgxRTMwQ0ExMTc0QSI+IDx4bXBNTTpEZXJpdmVkRnJvbSBzdFJlZjppbnN0YW5jZUlEPSJ4bXAuaWlkOkM0QjFDMDRCNzI1NjExRTVCMjEyODFFMzBDQTExNzRBIiBzdFJlZjpkb2N1bWVudElEPSJ4bXAuZGlkOkM0QjFDMDRDNzI1NjExRTVCMjEyODFFMzBDQTExNzRBIi8+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiA8P3hwYWNrZXQgZW5kPSJyIj8+LbWYNwAAAPpJREFUeNqk1NERgjAMBuCQJXAFmAKdAldAtoHDDdQp4FwCZnCKmtwVDmsLScndzwOW72JaSJqmOQHAg3Kr63qCVbVtC8rKKB3linR5Us6UnqAM4oufHSgF5cVwRflQuPMhEp/R1FoV2r9/sTfSCNxF2RoTY8w8z5zHsVrA45liUP4B5xXU+eh2bh9Uoz+wEt9E/2Ahvot64R1chHItm+crz4aCBA12vCq3cxEqgbmM8J4KdmcqPYph2L597kaJzzkq0NEzc16Ti+ANNLShvQ9HJSrGMQIV4RhC7dsHynO+4HgA3cS54/sBNIR3DJeUN3/YI1EXZ6v8CjAA5saaf8iGh+UAAAAASUVORK5CYII=) no-repeat scroll 0 0;
  background-size: cover;
}
.mod-dialog-content {
  position: relative;
  margin: 8px 20px 20px;
  color: #282828;
  font-size: 14px;
  line-height: 21px;
}
.mod-dialog-content ul {
  padding: 10px;
}
.mod-dialog-content li + li {
  margin-top: 15px;
}
.mod-dialog-content h3 {
  font-weight: bold;
  font-size: 14px;
  color: #000;
}
.mod-dialog-content p {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}
.mod-dialog-content #normalDownDialogContent {
  color: #282828;
  font-size: 14px;
  line-height: 21px;
}
.mod-dialog-content .mod-dailog-tips {
  font-size: 14px;
  color: #fe5c46;
}
.mod-dialog-footer {
  margin: 0 20px 20px;
  text-align: center;
  display: -webkit-box;
  -webkit-box-pack: justify;
}
.mod-dialog-footer a {
  -webkit-box-flex: 1;
  width: 50%;
}
.mod-dialog-footer a:first-child {
  margin-right: 10px;
}
.mod-dialog-footer .w-btn.btn-medium {
  display: block;
  width: 138px;
  height: 40px;
  line-height: 40px;
  border-width: 1px;
  border-style: solid;
  box-sizing: border-box;
  text-align: center;
  font-size: 14px;
}
.mod-dialog-footer .is-gray {
  border-color: #b0ccd9;
  color: #7da2b3;
}
.mod-dialog-footer .is-blue {
  border-color: #84d6fd;
  color: #00a3ef;
}
.mod-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 99;
}
.mod-mask.opacity-66 {
  background: rgba(0, 0, 0, 0.66);
}
.mod-card {
  background-color: #ffffff;
  position: absolute;
  bottom: 0;
  height: 311px;
  width: 100%;
  display: flex;
  flex-direction: column;
}
.mod-card.small-mod-card {
  height: 100px;
}
.autoscroll {
  overflow: auto;
}
.image-previewer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 99;
}
.image-previewer .icon-close {
  position: absolute;
  width: 20px;
  height: 20px;
  top: 10px;
  right: 10px;
  background-image: url(static/picture/close-icon-hover.b57f2752.svg);
  background-repeat: no-repeat;
  background-size: contain;
}
.image-previewer .page-view {
  position: absolute;
  width: 20px;
  height: 20px;
  top: 10px;
  right: 0;
  width: 100%;
  color: #fff;
  text-align: center;
}
.image-previewer .container {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
}
.image-previewer .container .slider {
  display: flex;
  flex-direction: row;
  width: 100vw;
  overflow: hidden;
}
.image-previewer .container .slider .slide-item {
  width: 100vw;
  display: flex;
  justify-content: center;
}
.image-previewer .container .slider .slide-item img {
  object-fit: contain;
  max-width: 80vw;
  max-height: 80vh;
}
.image-previewer .container .download-button {
  margin-top: calc(10 / 3.6 * 1vw * var(--m-bvw) + 10 * 1px * var(--m-bpx));
  width: calc(169 / 3.6 * 1vw * var(--m-bvw) + 169 * 1px * var(--m-bpx));
  height: calc(36 / 3.6 * 1vw * var(--m-bvw) + 36 * 1px * var(--m-bpx));
  font-size: calc(15 / 3.6 * 1vw * var(--m-bvw) + 15 * 1px * var(--m-bpx));
  line-height: calc(36 / 3.6 * 1vw * var(--m-bvw) + 36 * 1px * var(--m-bpx));
  border-radius: calc(18 / 3.6 * 1vw * var(--m-bvw) + 18 * 1px * var(--m-bpx));
  color: #fff;
  text-align: center;
  background-color: #FF3138;
}
* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  -webkit-tap-highlight-color: transparent;
  outline: none;
}
html,
body {
  width: 100vw;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen, Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif;
  background-color: #fff;
}
ul {
  list-style: none;
}
i {
  display: block;
}
h1,
h2,
h3 {
  font-weight: 500;
}
:root body {
  position: absolute;
}
.sem-page-container {
  width: 100vw;
  max-width: 800px;
  margin: 0 auto;
  background-repeat: no-repeat;
  background-size: 100%;
  background-position: top center;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow-x: hidden;
}
@media (min-width: 800px) {
  .sem-page-container {
    width: 360px;
    margin: 0 auto;
  }
  .sem-app-bottom-bar {
    width: 360px;
    margin: 0 auto;
  }
}
.moka-sem-page {
  width: 100%;
  background-repeat: no-repeat;
  background-size: 100%;
  background-position: top center;
}
.show {
  display: block;
}
.hidden {
  display: none !important;
}
.file-stream-dialog .mod-dialog {
  width: 296px;
  border-radius: 16px;
  padding: 24px;
}
.file-stream-dialog .mod-dialog .content {
  font-size: 16px;
  line-height: 24px;
  height: 48px;
  color: rgba(15, 15, 15, 0.85);
  margin-bottom: 24px;
}
.file-stream-dialog .mod-dialog .download-button {
  border-radius: 24px;
  box-sizing: border-box;
  display: block;
  width: 100%;
  height: 48px;
  overflow: hidden;
  text-align: center;
}
.file-stream-dialog .mod-dialog .download-button .progress {
  position: relative;
  width: 100%;
  height: 48px;
  color: #0080ff;
  background: #ebf5ff;
}
.file-stream-dialog .mod-dialog .download-button .progress .progress-holder {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  width: 100%;
  height: 100%;
}
.file-stream-dialog .mod-dialog .download-button .progress .progress-bar {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  overflow: hidden;
  display: block;
  background: #0080ff;
}
.file-stream-dialog .mod-dialog .download-button .progress .progress-text {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 248px;
  flex-direction: row;
  color: #fff;
}


.sem-app-top-bar {
  display: flex;
  width: 100%;
  height: 57px;
  padding: 10px 16px;
  font-size: 12px;
  line-height: 18px;
  align-items: center;
  color: rgba(0, 0, 0, 0.4);
  background: #FFFFFF;
  box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2;
}
.sem-app-top-bar .top-bar-app-card {
  flex: 1;
  width: 0;
}
.sem-app-top-bar .detail {
  display: flex;
  margin-top: 1px;
}
.sem-app-top-bar .version {
  margin-right: 8px;
  flex-shrink: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.sem-app-top-bar .developer {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.sem-app-top-bar .name {
  margin-right: 8px;
  flex: 0 0 auto;
  max-width: 100px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.sem-app-top-bar .top-bar-download-btn {
  flex: 0 0 auto;
  background: #0080ff;
  border-radius: 12px;
  width: 56px;
  height: 24px;
  color: #FFFFFF;
  line-height: 18px;
  margin-left: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.sem-app-privacy-link {
  display: flex;
}
.sem-app-privacy-link .separator {
  margin: 0 4px;
}
.sem-app-privacy-link .link {
  color: rgba(0, 128, 255, 0.65);
  flex: 0 0 auto;
}


.sem-vi-bar {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 16px;
  flex: 0 0 auto;
  flex-direction: row;
}
.sem-vi-bar .bar-app-icon {
  width: 24px;
  height: 24px;
  flex: 0 0 auto;
  border-radius: 4px;
}
.sem-vi-bar .bar-app-name {
  font-size: 12px;
  line-height: 18px;
  color: #000000;
  flex: 1 1 0;
  width: 0;
  margin-left: 4px;
  margin-right: 16px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.sem-vi-bar .bar-yyb-logo svg {
  width: 102px;
  height: 19px;
}


.sem-container.centered {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
}




.sem-app-title-panel {
  width: 100%;
  text-align: center;
}
.sem-app-title-panel .main-title {
  font-size: 28px;
  line-height: 41px;
  color: #0F0F0F;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.sem-app-title-panel .sub-title {
  font-size: 16px;
  line-height: 24px;
  color: rgba(15, 15, 15, 0.65);
  margin-top: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  word-wrap: break-word;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}


.sem-app-big-main-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  position: relative;
  width: 100%;
}
.sem-app-big-main-info .app-icon-wrapper {
  width: 104px;
  height: 104px;
  border: 0.5px solid rgba(0, 0, 0, 0.1);
  border-radius: 24px;
}
.sem-app-big-main-info .app-icon-wrapper.middle-icon {
  width: 80px;
  height: 80px;
}
.sem-app-big-main-info .app-icon-wrapper img {
  width: 100%;
  height: 100%;
}
.sem-app-big-main-info .label {
  position: relative;
}
.sem-app-big-main-info .label::before {
  content: '官方';
  position: absolute;
  left: 0;
  top: 0;
  background-image: url('static/picture/3teTLDoH.svg');
  background-size: contain;
  background-repeat: no-repeat;
  text-align: center;
  width: 34px;
  height: 15px;
  font-size: 12px;
  line-height: 15px;
  color: rgba(255, 255, 255, 0.9);
}
.sem-app-big-main-info .app-name {
  font-size: 18px;
  line-height: 24px;
  color: rgba(15, 15, 15, 0.85);
  margin-top: 8px;
  position: relative;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.sem-app-big-main-info .app-tags {
  display: flex;
  margin-top: 8px;
  position: relative;
  flex-wrap: wrap;
  justify-content: center;
  row-gap: 4px;
}
.sem-app-big-main-info .app-tags .tag-item {
  height: 19px;
  background: rgba(15, 15, 15, 0.06);
  border-radius: 3px;
  padding: 0 6px;
  font-size: 12px;
  line-height: 19px;
  color: rgba(15, 15, 15, 0.65);
  margin-right: 5.5px;
}
.sem-app-big-main-info .app-tags .tag-item:nth-child(4n + 1) {
  color: #1FB564;
  background-color: #00F8721A;
}
.sem-app-big-main-info .app-tags .tag-item:nth-child(4n + 2) {
  color: #F26700;
  background-color: #FFD0001F;
}
.sem-app-big-main-info .app-tags .tag-item:nth-child(4n + 3) {
  color: #EE3875;
  background-color: #FF296C0F;
}
.sem-app-big-main-info .app-tags .tag-item:nth-child(4n + 4) {
  color: #0080FF;
  background-color: #0080FF14;
}
.sem-app-big-main-info .app-tags .tag-item:last-child {
  margin-right: 0;
}
.sem-app-big-main-info .app-tags .tag-item.default-tag-official {
  color: #0080FF;
  background-color: #0080FF14;
}
.sem-app-big-main-info .app-tags .tag-item.default-tag-hot {
  color: #EE3875;
  background-color: #FF296C0F;
}


.sem-download-btn {
  margin-top: calc(16 / 3.6 * 1vw * var(--m-bvw) + 16 * 1px * var(--m-bpx));
  padding: 0 16px;
  width: 100%;
}
.sem-download-btn .download-btn {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #0080ff;
  border-radius: 35px;
  width: 100%;
  height: 48px;
  font-size: 16px;
  line-height: 24px;
  color: #FFFFFF;
  margin: auto;
}
@media (min-width: 800px) {
  .sem-download-btn .download-btn.custom-fixed {
    transform: translate(calc(50vw - 180px));
  }
}
.sem-download-btn.download-btn--show-sub-title {
  padding-bottom: 20px;
}
.sem-download-btn.download-btn--show-sub-title .download-btn:after {
  content: '将通过应用宝APP高速下载';
  font-weight: 400;
  position: absolute;
  text-align: center;
  width: 100%;
  left: 0;
  font-size: 12px;
  line-height: 12px;
  bottom: -20px;
  color: #D0D0D0;
}


.sem-app-main-privacy .container.style-one {
  margin-top: 8px;
  padding: 0 32px;
  font-size: 12px;
  line-height: 18px;
  color: rgba(0, 0, 0, 0.4);
}
.sem-app-main-privacy .container.style-one span {
  padding-right: 4px;
}
.sem-app-main-privacy .container.style-one .app-privacy {
  display: inline-block;
}
.sem-app-main-privacy .container.style-one .sem-app-privacy-link {
  display: flex;
}
.sem-app-main-privacy .container.style-one .sem-app-privacy-link .separator {
  margin: 0 4px;
}
.sem-app-main-privacy .container.style-one .sem-app-privacy-link .link {
  color: rgba(0, 128, 255, 0.65);
  flex: 0 0 auto;
}
.sem-app-main-privacy .container.style-two {
  text-align: center;
  width: 100%;
  font-size: 10px;
  line-height: 16px;
}
.sem-app-main-privacy .container.style-two .text {
  color: #D0D0D0;
}
.sem-app-main-privacy .container.style-two .sem-app-privacy-link {
  display: flex;
  justify-content: center;
  color: #cdd2d8;
}
.sem-app-main-privacy .container.style-two .sem-app-privacy-link .separator {
  margin: 0 10px;
}
.sem-app-main-privacy .container.style-two .sem-app-privacy-link .link {
  color: #a1bddd;
  flex: 0 0 auto;
}

</style>
  <script>
    window.htmlFirstTime = performance.now();
  </script>
  <script src="static/js/performance-reporter.umd.js"></script>
  <script>
    window.PerformanceReporter.init({
      reportEnv: 'formal',
      publicParams: {
        project: 'sem',
        uuid: '91171754119120803',
      },
      publicExtraData: {
        exp_ids: '0',
      },
    });
  </script>
</head>
<body>
  <div id="sem-page" class="sem-page-container">
    <div id="moka-sem-page" class="moka-sem-page" style="display: flex; flex-direction: column; height: 100vh; overflow: auto">
      <!-- --moka-component-- [app-top-bar][app-top-bar-xxx] --><!-- 顶部应用隐私信息展示 -->
<div id="app-top-bar-xxx" class="sem-app-top-bar" style="">
  <div class="top-bar-app-card"> 
    <div class="developer">
      开发商：深圳市腾讯计算机系统有限公司
    </div>
    <div class="detail">
      <div class="name">应用宝</div>
      <div class="version">9.0.3</div>

      <!-- app-privacy-link 组件，TODO: 需要引入其他组件逻辑，这个组件依赖到弹窗，怎么实现？ -->
      <div class="sem-app-privacy-link">
        <a href="https://cftweb.3g.qq.com/privacy/agreement?appid=5848" target="_blank" class="link">
          隐私
        </a>
        <div class="separator">|</div>
        <a href="javascript:void(0);" class="permission-button-flag link" data-appid="5848">
          权限
        </a>
        <div class="separator">|</div>
        <a href="https://m.yyb.qq.com/agreement/app-desc/?appid=5848" target="_blank" class="link">
          简介
        </a>
        <div class="separator">|</div>
        <a href="javascript:void(0);" class="icp-button-flag link">
          备案
        </a>
      </div>
      <!-- ---end--- -->
    </div>
  </div>
  <div class="top-bar-download-btn download-button-flag">
    下载
  </div>
</div>


<!-- --moka-component-- [vi-bar][vi-bar-xxx] --><!-- 品牌展示 -->
<div id="vi-bar-xxx" class="sem-vi-bar" style="">
  <img class="bar-app-icon" src="static/picture/96.png" alt="应用宝logo">
  <div class="bar-app-name">漫蛙</div>
  <div class="bar-yyb-logo">
    <svg viewbox="0 0 830 152" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M246.95 29.0503L228.44 133.05H215.44L222.97 93.0503H214.29C211.35 107.73 207.91 121.39 204.59 133.05H191.82C195.65 120.53 199.1 107.05 202.03 92.3203C207.03 67.6803 211.1 43.6803 214.67 21.5903H238.93C245.44 21.6403 247.97 22.7903 246.95 29.0503ZM228.7 62.2402H219.89C218.74 69.0102 217.46 76.0303 216.18 82.6703H224.87L228.7 62.2402ZM231.5 32.4902H225.12L221.8 51.9003H230.61L233.8 34.9003C234.19 32.8803 233.44 32.4902 231.5 32.4902ZM283.5 117.78H239.5L237.71 107.78H285.84L283.5 117.78ZM317.58 74.6202L314.26 85.8604C308.463 84.6262 302.959 82.2832 298.05 78.9603L295.11 92.6302H301.37C305.84 92.6302 308.9 93.3902 308.01 98.7502C306.48 108.46 304.18 121.75 302.01 133.1H267.41L265.11 122.75H289.88C291.16 117.14 292.56 110.5 293.45 105.14C293.84 103.35 293.45 102.71 291.67 102.71H247.75L251.75 81.9003C247.819 83.6697 243.709 85.0107 239.49 85.9003L242.17 73.1302C248.55 71.6002 258.77 66.8803 263.49 61.9003H244.34L246.34 51.9003H270.22C271.221 49.9946 272.116 48.0349 272.9 46.0303H247.34L249.34 35.8103H258.15L255.08 20.4902H268.49C269.38 25.6002 270.4 30.3203 271.55 35.8103H275.89C277.29 30.0734 278.147 24.2176 278.45 18.3203H291.98C291.675 24.2093 290.862 30.061 289.55 35.8103H294.66C297.002 30.844 299.006 25.7257 300.66 20.4902H314.07C312.206 25.7214 310.032 30.8372 307.56 35.8103H317L314.58 46.0303H286.58C285.81 47.9403 285.05 50.0303 284.15 51.9003H318.5L316.07 61.9003H296.41C299.81 67.8603 310.79 72.9602 317.55 74.6202H317.58ZM282.09 92.6202L284.09 83.3003C284.48 81.5103 284.09 80.8702 282.31 80.8702H258.44L264.44 84.9503L262.91 92.6202H282.09ZM278.52 61.8503C275.737 65.2643 272.614 68.3869 269.2 71.1703H289.2C286.779 68.3558 284.76 65.2193 283.2 61.8503H278.52Z" fill="#222222"></path>
      <path d="M351.9 106.289C357.14 102.459 363.14 97.5994 368.75 93.1394L365.69 109.729C355.163 118.044 344.118 125.68 332.62 132.589L345.52 63.0094H332.37L334.28 51.7694H361.6L351.9 106.289ZM351.77 45.0094L347.05 18.1895H360.33L365.69 45.0094H351.77ZM441.27 30.0695C436.04 59.2995 430.27 92.2395 425.06 121.219H440.89L436.29 133.099H408.71C412.54 111.009 415.86 91.5994 418.93 74.7494H398.5L388.29 133.099H374.29L384.44 74.7494H366.95L369.12 63.8994H386.35L391.2 36.5795L403.59 45.7694L400.4 63.8994H420.82C422.61 53.5594 424.27 44.2395 425.82 36.3195C426.46 32.9995 425.06 32.6194 422.12 32.6194H375.01L372.46 21.3894H433.35C441.14 21.3894 442.44 23.6794 441.27 30.0494V30.0695Z" fill="#222222"></path>
      <path d="M575.84 28.049L573.84 39.9189H481.15C480.25 53.7089 478.98 68.6389 477.32 81.2789C474.963 99.0925 470.283 116.521 463.4 133.119H447.4C455.031 116.168 460.316 98.2568 463.11 79.8789C465.637 62.7051 467.17 45.3996 467.7 28.049H514.3C513.41 24.729 512.51 21.529 511.75 18.209H527.96C528.86 21.279 529.75 24.469 531.03 28.049H575.84ZM571.84 53.449C564.69 73.369 553.84 98.769 543.24 120.859H566.24L564.58 133.119H472.28L474.07 120.859H527.94C538.882 98.9596 548.565 76.4529 556.94 53.449H571.84ZM507.75 109.619H492.56C489.75 91.4989 487.32 72.349 485.66 53.829H499.32C501.28 71.429 504.09 90.449 507.79 109.599L507.75 109.619ZM533.92 105.279H518.98C517.71 87.0289 516.81 67.7489 516.43 50.3889H530.09C530.77 67.4689 532.05 86.4989 533.96 105.259L533.92 105.279Z" fill="#222222"></path>
      <path d="M699.55 30.3185L685 133.099H657.68L654.1 121.609H671.21L674.91 97.0885H646.91L642.06 133.089H627.44L632.55 97.0885H602.8C599.99 109.479 596.8 121.729 593.23 133.089H576.63C590.8 92.8685 596.29 58.5284 600.12 22.3984H691.4C699.3 22.4084 700.32 24.8285 699.55 30.3185ZM634.06 86.1085L637 65.0485H609C607.72 72.0485 606.44 79.0485 605 86.1085H634.06ZM613.38 33.6384C612.61 40.2784 611.72 47.1785 610.7 54.0685H638.53L641.34 33.6384H613.38ZM676.57 86.1085L679.76 65.0485H651.44L648.51 86.1085H676.57ZM680.28 33.6384H655.63L652.83 54.0685H681.44L684 37.4685C684.49 34.1485 683.21 33.6384 680.28 33.6384Z" fill="#222222"></path>
      <path d="M771.18 120.709H819.06L817.52 131.819H707.6L709.01 120.709H756.37L760.07 92.8794H723.94L725.6 81.8794H761.6L764.02 63.7494H736.32L737.72 52.7494H808.96L807.43 63.7494H778.83L776.4 81.8794H812.4L810.74 92.8794H774.86L771.18 120.709ZM829.27 37.3395L826.21 59.8094H811.27L813.69 42.7095C813.82 41.8095 813.44 41.1794 812.42 41.1794H736.33L733.65 59.5594H718.71L723.18 30.0695H768.12C767.22 26.2395 766.33 22.2795 765.69 18.1895H781.01C781.734 22.1969 782.672 26.1626 783.82 30.0695H822.89C828.25 30.0495 830.04 31.4695 829.27 37.3395ZM784.97 96.0695H799.14C801.193 103.372 803.708 110.536 806.67 117.519H791.22C788.694 110.499 786.607 103.329 784.97 96.0494V96.0695Z" fill="#222222"></path>
      <path d="M76.009 0.050845C66.0992 0.03795 56.2844 1.98179 47.1276 5.77082C37.9708 9.55984 29.6522 15.1194 22.649 22.1308C18.7573 26.0271 16.5713 31.3089 16.5713 36.8159C16.5713 42.3228 18.7573 47.6045 22.649 51.5008V51.5008C24.578 53.432 26.8687 54.9641 29.3901 56.0093C31.9116 57.0546 34.6144 57.5926 37.344 57.5926C40.0735 57.5926 42.7763 57.0546 45.2978 56.0093C47.8193 54.9641 50.11 53.432 52.0389 51.5008L99.689 3.85089C92.0486 1.33013 84.0544 0.04722 76.009 0.050845V0.050845Z" fill="#FFCC00"></path>
      <path d="M4.23929 51.9707C-0.139185 65.2526 -0.748744 79.4883 2.47836 93.0958C5.70547 106.703 12.6428 119.149 22.5193 129.051C26.4169 132.94 31.6982 135.124 37.2043 135.124C42.7105 135.124 47.9917 132.94 51.8893 129.051V129.051C55.7877 125.152 57.9778 119.864 57.9778 114.351C57.9778 108.837 55.7877 103.549 51.8893 99.6506L4.23929 51.9707Z" fill="#0080FF"></path>
      <path d="M75.9693 151.201C85.8788 151.212 95.6931 149.267 104.85 145.478C114.006 141.689 122.325 136.131 129.329 129.121C133.219 125.223 135.403 119.942 135.403 114.436C135.403 108.93 133.219 103.648 129.329 99.7506V99.7506C125.43 95.8522 120.143 93.6621 114.629 93.6621C109.116 93.6621 103.828 95.8522 99.9293 99.7506L52.2793 147.401C59.9233 149.921 67.9207 151.203 75.9693 151.201V151.201Z" fill="#FF0055"></path>
      <path d="M100.039 22.3409C98.1078 24.2699 96.5758 26.5606 95.5305 29.082C94.4852 31.6035 93.9473 34.3063 93.9473 37.0359C93.9473 39.7654 94.4852 42.4683 95.5305 44.9898C96.5758 47.5112 98.1078 49.802 100.039 51.731L147.689 99.3809C152.07 86.1047 152.683 71.874 149.461 58.2701C146.239 44.6663 139.309 32.2222 129.439 22.3209C127.508 20.3907 125.215 18.86 122.692 17.8163C120.169 16.7726 117.465 16.2364 114.735 16.2383C112.004 16.2401 109.301 16.78 106.78 17.8272C104.258 18.8743 101.968 20.4081 100.039 22.3409V22.3409Z" fill="#2BD6A9"></path>
    </svg>
  </div>
</div>
<!-- --moka-component-- [container][container-xxx] --><!-- 容器组件 -->
<div id="container-xxx" class="sem-container " style="width: 100%; display: flex; flex: 1; flex-direction: column; justify-content: space-between; align-items: center; padding: 0 16px" data-report="{&#34;module&#34;:&#34;&#34;}">
  <!-- --moka-component-- [empty][empty-xxx] --><!-- 空白的占位组件 -->
<div id="empty-xxx" class="sem-empty" style="">
</div>

<!-- --moka-component-- [app-title-panel][app-title-panel-xxx] --><!-- 标题组 -->
<div id="app-title-panel-xxx" class="sem-app-title-panel" style="">
  <div class="main-title">
    2025官方版下载</div>
  <div class="sub-title">
    精彩漫蛙漫画
  </div>
</div>
<!-- --moka-component-- [app-big-main-info][app-big-main-info] --><!-- 应用信息展示(大) -->

<div id="app-big-main-info" class="sem-app-big-main-info" style="">
  <div class="app-icon-wrapper   big-icon">
    <img class="app-icon" src="static/picture/0.png" alt="应用宝logo">
  </div>
  <div class="app-name">漫蛙</div>
  
  
    <div class="app-tags">
      
        <div class="tag-item">漫画</div>
      
      
        <span class="tag-item default-tag-official">官方</span>
        <span class="tag-item default-tag-hot">热门应用</span>
      
    </div>
  
</div>
<!-- --moka-component-- [download-btn][download-btn-xxx] --><!-- 下载按钮 -->

<div id="download-btn-xxx" class="sem-download-btn download-button-flag download-btn--show-sub-title" style="width: 244px">
  <div class="download-btn " style="; background-color: undefined; top: undefined; left: undefined; bottom: unset; width: undefined; height: undefined; opacity: 1;">
    立即下载
  </div>
</div>
<!-- --moka-component-- [empty][empty-xxx1] --><!-- 空白的占位组件 -->
<div id="empty-xxx1" class="sem-empty" style="">
</div>

<!-- --moka-component-- [empty][empty-xxx2] --><!-- 空白的占位组件 -->
<div id="empty-xxx2" class="sem-empty" style="">
</div>

</div>
<!-- --moka-component-- [app-main-privacy][app-main-privacy-xxx] --><!-- 应用六要素 -->
<div id="app-main-privacy-xxx" class="sem-app-main-privacy" style="margin: 6px 0">
  
  <div class="container style-two">
    
    <p class="text">开发商: 贵阳红彩华海科技有限公司</p>
    <p class="text">版本: 1.5</p>
    <div class="sem-app-privacy-link">
      <a href="https://cftweb.3g.qq.com/privacy/agreement?appid=54471964" target="_blank" class="link">
        隐私
      </a>
      <div class="separator">|</div>
      <a href="javascript:void(0);" class="permission-button-flag link" data-appid="54471964">
        权限
      </a>
      <div class="separator">|</div>
      <a href="https://m.yyb.qq.com/agreement/app-desc/?appid=54471964" target="_blank" class="link">
        简介
      </a>
      <div class="separator">|</div>
      <a href="javascript:void(0);" class="icp-button-flag link">
        备案
      </a>
    </div>
  </div>
  
</div>


    </div>
  </div>
  
  <div id="file-stream-dialog" class="mod-mask hidden file-stream-dialog">
    <div class="mod-dialog">
      <div class="content">正在为您下载，预计3秒后下载完成</div>
      <div class="download-button">
        <div class="progress">
          <div class="progress-holder">0%</div>
          <div class="progress-bar">
            <div class="progress-text">0%</div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
</body>
</html>