<!-- --moka-component-- [page][page-xxx] --><!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <meta name="format-detection" content="telephone=no">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="description" itemprop="description" content="应用宝">
  <title>腾讯应用宝</title>
  
  
  
  
  
  <style>@charset "utf-8";
html {
  box-sizing: border-box;
}
*,
*:before,
*:after {
  box-sizing: inherit;
}
body,
div,
dl,
dt,
dd,
ul,
ol,
li,
h1,
h2,
h3,
h4,
h5,
h6,
pre,
code,
form,
fieldset,
legend,
input,
textarea,
p,
blockquote,
th,
td,
header,
hgroup,
nav,
section,
article,
aside,
footer,
figure,
figcaption,
menu {
  margin: 0;
  padding: 0;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: normal;
}
body {
  font-family: Roboto, "Droid Sans", "Droid Sans Fallback", "Helvetica Neue", Helvetica, STHeiTi, sans-serif;
  line-height: 24px;
  font-size: 14px;
  color: #000;
  -webkit-user-select: none;
  -webkit-text-size-adjust: none;
  background-color: #f5f5f5;
}
table {
  border-collapse: collapse;
  border-spacing: 0;
}
fieldset,
img {
  display: block;
  border: 0;
}
li {
  list-style: none;
}
input,
textarea,
select {
  font-family: inherit;
  font-size: inherit;
  font-weight: inherit;
}
a,
input {
  text-decoration: none;
  color: #1D82FF;
  -webkit-tap-highlight-color: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
header,
nav,
section,
article,
footer,
figure,
figcaption {
  display: block;
}
:root {
  --moka-length-calc-basic-vw: 1;
  --moka-length-calc-basic-px: 0;
  --m-bvw: 1;
  --m-bpx: 0;
}
@media (min-width: 800px) {
  :root {
    --moka-length-calc-basic-vw: 0;
    --moka-length-calc-basic-px: 1;
    --m-bvw: 0;
    --m-bpx: 1;
  }
}
.one-line {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.mod-dialog {
  display: -webkit-box;
  -webkit-box-align: stretch;
  -webkit-box-pack: center;
  -webkit-box-orient: vertical;
  background: #fff;
  position: absolute;
  top: 50%;
  left: 24px;
  right: 24px;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  width: auto;
  margin: auto;
  z-index: 199;
}
.mod-dialog-header {
  padding: 12px 20px 0;
  height: 36px;
  line-height: 24px;
  color: #000000;
  font-size: 16px;
  position: relative;
}
.mod-dialog-header .mod-close-btn {
  position: absolute;
  top: 18px;
  right: 18px;
  height: 13px;
  width: 13px;
}
.mod-dialog-header .mod-close-btn:after {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  height: 13px;
  width: 13px;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABYAAAAWCAYAAADEtGw7AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyFpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNS1jMDE0IDc5LjE1MTQ4MSwgMjAxMy8wMy8xMy0xMjowOToxNSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIChXaW5kb3dzKSIgeG1wTU06SW5zdGFuY2VJRD0ieG1wLmlpZDpDNEIxQzA0RDcyNTYxMUU1QjIxMjgxRTMwQ0ExMTc0QSIgeG1wTU06RG9jdW1lbnRJRD0ieG1wLmRpZDpDNEIxQzA0RTcyNTYxMUU1QjIxMjgxRTMwQ0ExMTc0QSI+IDx4bXBNTTpEZXJpdmVkRnJvbSBzdFJlZjppbnN0YW5jZUlEPSJ4bXAuaWlkOkM0QjFDMDRCNzI1NjExRTVCMjEyODFFMzBDQTExNzRBIiBzdFJlZjpkb2N1bWVudElEPSJ4bXAuZGlkOkM0QjFDMDRDNzI1NjExRTVCMjEyODFFMzBDQTExNzRBIi8+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiA8P3hwYWNrZXQgZW5kPSJyIj8+LbWYNwAAAPpJREFUeNqk1NERgjAMBuCQJXAFmAKdAldAtoHDDdQp4FwCZnCKmtwVDmsLScndzwOW72JaSJqmOQHAg3Kr63qCVbVtC8rKKB3linR5Us6UnqAM4oufHSgF5cVwRflQuPMhEp/R1FoV2r9/sTfSCNxF2RoTY8w8z5zHsVrA45liUP4B5xXU+eh2bh9Uoz+wEt9E/2Ahvot64R1chHItm+crz4aCBA12vCq3cxEqgbmM8J4KdmcqPYph2L597kaJzzkq0NEzc16Ti+ANNLShvQ9HJSrGMQIV4RhC7dsHynO+4HgA3cS54/sBNIR3DJeUN3/YI1EXZ6v8CjAA5saaf8iGh+UAAAAASUVORK5CYII=) no-repeat scroll 0 0;
  background-size: cover;
}
.mod-dialog-content {
  position: relative;
  margin: 8px 20px 20px;
  color: #282828;
  font-size: 14px;
  line-height: 21px;
}
.mod-dialog-content ul {
  padding: 10px;
}
.mod-dialog-content li + li {
  margin-top: 15px;
}
.mod-dialog-content h3 {
  font-weight: bold;
  font-size: 14px;
  color: #000;
}
.mod-dialog-content p {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}
.mod-dialog-content #normalDownDialogContent {
  color: #282828;
  font-size: 14px;
  line-height: 21px;
}
.mod-dialog-content .mod-dailog-tips {
  font-size: 14px;
  color: #fe5c46;
}
.mod-dialog-footer {
  margin: 0 20px 20px;
  text-align: center;
  display: -webkit-box;
  -webkit-box-pack: justify;
}
.mod-dialog-footer a {
  -webkit-box-flex: 1;
  width: 50%;
}
.mod-dialog-footer a:first-child {
  margin-right: 10px;
}
.mod-dialog-footer .w-btn.btn-medium {
  display: block;
  width: 138px;
  height: 40px;
  line-height: 40px;
  border-width: 1px;
  border-style: solid;
  box-sizing: border-box;
  text-align: center;
  font-size: 14px;
}
.mod-dialog-footer .is-gray {
  border-color: #b0ccd9;
  color: #7da2b3;
}
.mod-dialog-footer .is-blue {
  border-color: #84d6fd;
  color: #00a3ef;
}
.mod-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 99;
}
.mod-mask.opacity-66 {
  background: rgba(0, 0, 0, 0.66);
}
.mod-card {
  background-color: #ffffff;
  position: absolute;
  bottom: 0;
  height: 311px;
  width: 100%;
  display: flex;
  flex-direction: column;
}
.mod-card.small-mod-card {
  height: 100px;
}
.autoscroll {
  overflow: auto;
}
.image-previewer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 99;
}
.image-previewer .icon-close {
  position: absolute;
  width: 20px;
  height: 20px;
  top: 10px;
  right: 10px;
  background-image: url(static/picture/close-icon-hover.b57f2752.svg);
  background-repeat: no-repeat;
  background-size: contain;
}
.image-previewer .page-view {
  position: absolute;
  width: 20px;
  height: 20px;
  top: 10px;
  right: 0;
  width: 100%;
  color: #fff;
  text-align: center;
}
.image-previewer .container {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
}
.image-previewer .container .slider {
  display: flex;
  flex-direction: row;
  width: 100vw;
  overflow: hidden;
}
.image-previewer .container .slider .slide-item {
  width: 100vw;
  display: flex;
  justify-content: center;
}
.image-previewer .container .slider .slide-item img {
  object-fit: contain;
  max-width: 80vw;
  max-height: 80vh;
}
.image-previewer .container .download-button {
  margin-top: calc(10 / 3.6 * 1vw * var(--m-bvw) + 10 * 1px * var(--m-bpx));
  width: calc(169 / 3.6 * 1vw * var(--m-bvw) + 169 * 1px * var(--m-bpx));
  height: calc(36 / 3.6 * 1vw * var(--m-bvw) + 36 * 1px * var(--m-bpx));
  font-size: calc(15 / 3.6 * 1vw * var(--m-bvw) + 15 * 1px * var(--m-bpx));
  line-height: calc(36 / 3.6 * 1vw * var(--m-bvw) + 36 * 1px * var(--m-bpx));
  border-radius: calc(18 / 3.6 * 1vw * var(--m-bvw) + 18 * 1px * var(--m-bpx));
  color: #fff;
  text-align: center;
  background-color: #FF3138;
}
* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  -webkit-tap-highlight-color: transparent;
  outline: none;
}
html,
body {
  width: 100vw;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen, Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif;
  background-color: #fff;
}
ul {
  list-style: none;
}
i {
  display: block;
}
h1,
h2,
h3 {
  font-weight: 500;
}
:root body {
  position: absolute;
}
.sem-page-container {
  width: 100vw;
  max-width: 800px;
  margin: 0 auto;
  background-repeat: no-repeat;
  background-size: 100%;
  background-position: top center;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow-x: hidden;
}
@media (min-width: 800px) {
  .sem-page-container {
    width: 360px;
    margin: 0 auto;
  }
  .sem-app-bottom-bar {
    width: 360px;
    margin: 0 auto;
  }
}
.moka-sem-page {
  width: 100%;
  background-repeat: no-repeat;
  background-size: 100%;
  background-position: top center;
}
.show {
  display: block;
}
.hidden {
  display: none !important;
}
.file-stream-dialog .mod-dialog {
  width: 296px;
  border-radius: 16px;
  padding: 24px;
}
.file-stream-dialog .mod-dialog .content {
  font-size: 16px;
  line-height: 24px;
  height: 48px;
  color: rgba(15, 15, 15, 0.85);
  margin-bottom: 24px;
}
.file-stream-dialog .mod-dialog .download-button {
  border-radius: 24px;
  box-sizing: border-box;
  display: block;
  width: 100%;
  height: 48px;
  overflow: hidden;
  text-align: center;
}
.file-stream-dialog .mod-dialog .download-button .progress {
  position: relative;
  width: 100%;
  height: 48px;
  color: #0080ff;
  background: #ebf5ff;
}
.file-stream-dialog .mod-dialog .download-button .progress .progress-holder {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  width: 100%;
  height: 100%;
}
.file-stream-dialog .mod-dialog .download-button .progress .progress-bar {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  overflow: hidden;
  display: block;
  background: #0080ff;
}
.file-stream-dialog .mod-dialog .download-button .progress .progress-text {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 248px;
  flex-direction: row;
  color: #fff;
}


.sem-app-top-bar {
  display: flex;
  width: 100%;
  height: 57px;
  padding: 10px 16px;
  font-size: 12px;
  line-height: 18px;
  align-items: center;
  color: rgba(0, 0, 0, 0.4);
  background: #FFFFFF;
  box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2;
}
.sem-app-top-bar .top-bar-app-card {
  flex: 1;
  width: 0;
}
.sem-app-top-bar .detail {
  display: flex;
  margin-top: 1px;
}
.sem-app-top-bar .version {
  margin-right: 8px;
  flex-shrink: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.sem-app-top-bar .developer {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.sem-app-top-bar .name {
  margin-right: 8px;
  flex: 0 0 auto;
  max-width: 100px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.sem-app-top-bar .top-bar-download-btn {
  flex: 0 0 auto;
  background: #0080ff;
  border-radius: 12px;
  width: 56px;
  height: 24px;
  color: #FFFFFF;
  line-height: 18px;
  margin-left: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.sem-app-privacy-link {
  display: flex;
}
.sem-app-privacy-link .separator {
  margin: 0 4px;
}
.sem-app-privacy-link .link {
  color: rgba(0, 128, 255, 0.65);
  flex: 0 0 auto;
}


.sem-vi-bar {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 16px;
  flex: 0 0 auto;
  flex-direction: row;
}
.sem-vi-bar .bar-app-icon {
  width: 24px;
  height: 24px;
  flex: 0 0 auto;
  border-radius: 4px;
}
.sem-vi-bar .bar-app-name {
  font-size: 12px;
  line-height: 18px;
  color: #000000;
  flex: 1 1 0;
  width: 0;
  margin-left: 4px;
  margin-right: 16px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.sem-vi-bar .bar-yyb-logo svg {
  width: 102px;
  height: 19px;
}


.sem-container.centered {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
}




.sem-app-title-panel {
  width: 100%;
  text-align: center;
}
.sem-app-title-panel .main-title {
  font-size: 28px;
  line-height: 41px;
  color: #0F0F0F;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.sem-app-title-panel .sub-title {
  font-size: 16px;
  line-height: 24px;
  color: rgba(15, 15, 15, 0.65);
  margin-top: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  word-wrap: break-word;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}


.sem-app-big-main-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  position: relative;
  width: 100%;
}
.sem-app-big-main-info .app-icon-wrapper {
  width: 104px;
  height: 104px;
  border: 0.5px solid rgba(0, 0, 0, 0.1);
  border-radius: 24px;
}
.sem-app-big-main-info .app-icon-wrapper.middle-icon {
  width: 80px;
  height: 80px;
}
.sem-app-big-main-info .app-icon-wrapper img {
  width: 100%;
  height: 100%;
}
.sem-app-big-main-info .label {
  position: relative;
}
.sem-app-big-main-info .label::before {
  content: '官方';
  position: absolute;
  left: 0;
  top: 0;
  background-image: url('static/picture/3teTLDoH.svg');
  background-size: contain;
  background-repeat: no-repeat;
  text-align: center;
  width: 34px;
  height: 15px;
  font-size: 12px;
  line-height: 15px;
  color: rgba(255, 255, 255, 0.9);
}
.sem-app-big-main-info .app-name {
  font-size: 18px;
  line-height: 24px;
  color: rgba(15, 15, 15, 0.85);
  margin-top: 8px;
  position: relative;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.sem-app-big-main-info .app-tags {
  display: flex;
  margin-top: 8px;
  position: relative;
  flex-wrap: wrap;
  justify-content: center;
  row-gap: 4px;
}
.sem-app-big-main-info .app-tags .tag-item {
  height: 19px;
  background: rgba(15, 15, 15, 0.06);
  border-radius: 3px;
  padding: 0 6px;
  font-size: 12px;
  line-height: 19px;
  color: rgba(15, 15, 15, 0.65);
  margin-right: 5.5px;
}
.sem-app-big-main-info .app-tags .tag-item:nth-child(4n + 1) {
  color: #1FB564;
  background-color: #00F8721A;
}
.sem-app-big-main-info .app-tags .tag-item:nth-child(4n + 2) {
  color: #F26700;
  background-color: #FFD0001F;
}
.sem-app-big-main-info .app-tags .tag-item:nth-child(4n + 3) {
  color: #EE3875;
  background-color: #FF296C0F;
}
.sem-app-big-main-info .app-tags .tag-item:nth-child(4n + 4) {
  color: #0080FF;
  background-color: #0080FF14;
}
.sem-app-big-main-info .app-tags .tag-item:last-child {
  margin-right: 0;
}
.sem-app-big-main-info .app-tags .tag-item.default-tag-official {
  color: #0080FF;
  background-color: #0080FF14;
}
.sem-app-big-main-info .app-tags .tag-item.default-tag-hot {
  color: #EE3875;
  background-color: #FF296C0F;
}


.sem-download-btn {
  margin-top: calc(16 / 3.6 * 1vw * var(--m-bvw) + 16 * 1px * var(--m-bpx));
  padding: 0 16px;
  width: 100%;
}
.sem-download-btn .download-btn {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #0080ff;
  border-radius: 35px;
  width: 100%;
  height: 48px;
  font-size: 16px;
  line-height: 24px;
  color: #FFFFFF;
  margin: auto;
}
@media (min-width: 800px) {
  .sem-download-btn .download-btn.custom-fixed {
    transform: translate(calc(50vw - 180px));
  }
}
.sem-download-btn.download-btn--show-sub-title {
  padding-bottom: 20px;
}
.sem-download-btn.download-btn--show-sub-title .download-btn:after {
  content: '将通过应用宝APP高速下载';
  font-weight: 400;
  position: absolute;
  text-align: center;
  width: 100%;
  left: 0;
  font-size: 12px;
  line-height: 12px;
  bottom: -20px;
  color: #D0D0D0;
}


.sem-app-main-privacy .container.style-one {
  margin-top: 8px;
  padding: 0 32px;
  font-size: 12px;
  line-height: 18px;
  color: rgba(0, 0, 0, 0.4);
}
.sem-app-main-privacy .container.style-one span {
  padding-right: 4px;
}
.sem-app-main-privacy .container.style-one .app-privacy {
  display: inline-block;
}
.sem-app-main-privacy .container.style-one .sem-app-privacy-link {
  display: flex;
}
.sem-app-main-privacy .container.style-one .sem-app-privacy-link .separator {
  margin: 0 4px;
}
.sem-app-main-privacy .container.style-one .sem-app-privacy-link .link {
  color: rgba(0, 128, 255, 0.65);
  flex: 0 0 auto;
}
.sem-app-main-privacy .container.style-two {
  text-align: center;
  width: 100%;
  font-size: 10px;
  line-height: 16px;
}
.sem-app-main-privacy .container.style-two .text {
  color: #D0D0D0;
}
.sem-app-main-privacy .container.style-two .sem-app-privacy-link {
  display: flex;
  justify-content: center;
  color: #cdd2d8;
}
.sem-app-main-privacy .container.style-two .sem-app-privacy-link .separator {
  margin: 0 10px;
}
.sem-app-main-privacy .container.style-two .sem-app-privacy-link .link {
  color: #a1bddd;
  flex: 0 0 auto;
}

/* 新增下载选项样式 - 一排显示 */
.download-options {
  width: 100%;
  padding: 0 16px;
  margin-top: 16px;
  display: flex;
  justify-content: space-between;
  gap: 8px;
}

.download-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  padding: 10px 6px;
  background: #f8f9fa;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #e9ecef;
  min-height: 60px;
}

.download-option:hover {
  background: #e9ecef;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.download-option:active {
  transform: translateY(0);
}

.download-option img {
  width: 40px;
  height: 40px;
  margin-bottom: 4px;
  border-radius: 3px;
}

.download-option span {
  font-size: 12px;
  color: #333;
  font-weight: 500;
  text-align: center;
  line-height: 1.2;
}

</style>
  <script>
    window.htmlFirstTime = performance.now();
  </script>
  <script src="static/js/performance-reporter.umd.js"></script>
  <script>
    window.PerformanceReporter.init({
      reportEnv: 'formal',
      publicParams: {
        project: 'sem',
        uuid: '91171754119120803',
      },
      publicExtraData: {
        exp_ids: '0',
      },
    });
  </script>
</head>
<body>
  <div id="sem-page" class="sem-page-container">
    <div id="moka-sem-page" class="moka-sem-page" style="display: flex; flex-direction: column; height: 100vh; overflow: auto">
      <!-- --moka-component-- [app-top-bar][app-top-bar-xxx] --><!-- 顶部应用隐私信息展示 -->
<div id="app-top-bar-xxx" class="sem-app-top-bar" style="">
  <div class="top-bar-app-card"> 
    <div class="developer">
      开发商：深圳市腾讯计算机系统有限公司
    </div>
    <div class="detail">
      <div class="name">应用宝</div>
      <div class="version">9.0.3</div>

      <!-- app-privacy-link 组件，去掉跳转链接 -->
      <div class="sem-app-privacy-link">
        <span class="link">
          隐私
        </span>
        <div class="separator">|</div>
        <span class="link">
          权限
        </span>
        <div class="separator">|</div>
        <span class="link">
          简介
        </span>
        <div class="separator">|</div>
        <span class="link">
          备案
        </span>
      </div>
      <!-- ---end--- -->
    </div>
  </div>
  <div class="top-bar-download-btn download-button-flag">
    下载
  </div>
</div>


<!-- --moka-component-- [vi-bar][vi-bar-xxx] --><!-- 品牌展示 -->
<div id="vi-bar-xxx" class="sem-vi-bar" style="">
  <img class="bar-app-icon" src="static/picture/96.png" alt="应用宝logo">
  <div class="bar-app-name">漫蛙</div>

</div>
<!-- --moka-component-- [container][container-xxx] --><!-- 容器组件 -->
<div id="container-xxx" class="sem-container " style="width: 100%; display: flex; flex: 1; flex-direction: column; justify-content: space-between; align-items: center; padding: 0 16px" data-report="{&#34;module&#34;:&#34;&#34;}">
  <!-- --moka-component-- [empty][empty-xxx] --><!-- 空白的占位组件 -->
<div id="empty-xxx" class="sem-empty" style="">
</div>

<!-- --moka-component-- [app-title-panel][app-title-panel-xxx] --><!-- 标题组 -->
<div id="app-title-panel-xxx" class="sem-app-title-panel" style="">
  <div class="main-title">
    2025官方版下载</div>
  <div class="sub-title">
    精彩漫蛙漫画
  </div>
</div>
<!-- --moka-component-- [app-big-main-info][app-big-main-info] --><!-- 应用信息展示(大) -->

<div id="app-big-main-info" class="sem-app-big-main-info" style="">
  <div class="app-icon-wrapper   big-icon">
    <img class="app-icon" src="static/picture/0.png" alt="应用宝logo">
  </div>
  <div class="app-name">漫蛙</div>
  
  
    <div class="app-tags">
      
        <div class="tag-item">漫画</div>
      
      
        <span class="tag-item default-tag-official">官方</span>
        <span class="tag-item default-tag-hot">热门应用</span>
      
    </div>
  
</div>
<!-- --moka-component-- [download-btn][download-btn-xxx] --><!-- 下载按钮 -->

<div id="download-btn-xxx" class="sem-download-btn download-button-flag download-btn--show-sub-title" style="width: 244px">
  <div class="download-btn " style="; background-color: undefined; top: undefined; left: undefined; bottom: unset; width: undefined; height: undefined; opacity: 1;">
    立即下载
  </div>
</div>

<!-- 新增的三个下载选项 - 一排显示 -->
<div class="download-options">
  <div class="download-option" onclick="downloadApp('main')">
    <img src="static/picture/0.png" alt="蛙2">
    <span>蛙2下载</span>
  </div>
  <div class="download-option" onclick="downloadApp('main')">
    <img src="static/picture/0.png" alt="漫蛙老版">
    <span>漫蛙老版下载</span>
  </div>
  <div class="download-option" onclick="downloadApp('main')">
    <img src="static/picture/0.png" alt="漫蛙纯净版">
    <span>漫蛙纯净版下载</span>
  </div>
</div>
<!-- --moka-component-- [empty][empty-xxx1] --><!-- 空白的占位组件 -->
<div id="empty-xxx1" class="sem-empty" style="">
</div>

<!-- --moka-component-- [empty][empty-xxx2] --><!-- 空白的占位组件 -->
<div id="empty-xxx2" class="sem-empty" style="">
</div>

</div>
<!-- --moka-component-- [app-main-privacy][app-main-privacy-xxx] --><!-- 应用六要素 -->
<div id="app-main-privacy-xxx" class="sem-app-main-privacy" style="margin: 6px 0">
  
  <div class="container style-two">
    
    <p class="text">开发商: 贵阳红彩华海科技有限公司</p>
    <p class="text">版本: 1.5</p>
    <div class="sem-app-privacy-link">
      <span class="link">
        隐私
      </span>
      <div class="separator">|</div>
      <span class="link">
        权限
      </span>
      <div class="separator">|</div>
      <span class="link">
        简介
      </span>
      <div class="separator">|</div>
      <span class="link">
        备案
      </span>
    </div>
  </div>
  
</div>


    </div>
  </div>
  
  <div id="file-stream-dialog" class="mod-mask hidden file-stream-dialog">
    <div class="mod-dialog">
      <div class="content">正在为您下载，预计3秒后下载完成</div>
      <div class="download-button">
        <div class="progress">
          <div class="progress-holder">0%</div>
          <div class="progress-bar">
            <div class="progress-text">0%</div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // 读取本地txt文件中的下载链接
    async function getDownloadLinks() {
      try {
        // 通过fetch读取txt文件
        const response = await fetch('appdown.txt');
        const text = await response.text();
        const lines = text.split('\n').filter(line => line.trim() !== '');

        // 使用txt文件第一行作为下载链接
        if (lines.length >= 1 && lines[0].trim() !== '') {
          return lines[0].trim();
        }
      } catch (error) {
        console.error('无法读取下载链接文件:', error);
      }

      // 如果读取失败，返回空
      return null;
    }

    // 下载应用函数 - 所有下载都使用txt文件中的同一个链接
    async function downloadApp(type) {
      const downloadUrl = await getDownloadLinks();

      if (downloadUrl) {
        window.open(downloadUrl, '_blank');
      } else {
        alert('下载链接暂未配置，请稍后再试');
      }
    }

    // 为主下载按钮和右上角下载按钮添加点击事件
    document.addEventListener('DOMContentLoaded', function() {
      // 主下载按钮
      const mainDownloadBtn = document.querySelector('#download-btn-xxx .download-btn');
      if (mainDownloadBtn) {
        mainDownloadBtn.addEventListener('click', () => downloadApp('main'));
      }

      // 右上角下载按钮
      const topDownloadBtn = document.querySelector('.top-bar-download-btn');
      if (topDownloadBtn) {
        topDownloadBtn.addEventListener('click', () => downloadApp('main'));
      }
    });
  </script>

</body>
</html>