﻿!function(r,n){"object"==typeof exports&&"undefined"!=typeof module?module.exports=n():"function"==typeof define&&define.amd?define(n):(r="undefined"!=typeof globalThis?globalThis:r||self).AegisBootstrap=n()}(this,(function(){"use strict";function r(r,n,e,t){return new(e||(e=Promise))((function(o,i){function u(r){try{f(t.next(r))}catch(r){i(r)}}function c(r){try{f(t.throw(r))}catch(r){i(r)}}function f(r){var n;r.done?o(r.value):(n=r.value,n instanceof e?n:new e((function(r){r(n)}))).then(u,c)}f((t=t.apply(r,n||[])).next())}))}var n,e,t,o,i;"function"==typeof SuppressedError&&SuppressedError,function(r){r.Test="test",r.Prod="production"}(n||(n={})),function(r){r[r.Regex=1]="Regex",r[r.StartWith=2]="StartWith",r[r.Contains=3]="Contains"}(e||(e={})),function(r){r[r.Global=1]="Global",r[r.Project=2]="Project"}(t||(t={})),function(r){r.Info="info",r.Error="error",r.SdkError="sdk_error",r.PromiseError="promise_error",r.AjaxError="ajax_error",r.ScriptError="script_error",r.WebsocketError="websocket_error",r.ImageError="image_error",r.CSSError="css_error",r.MediaError="media_error",r.Report="report",r.RetError="ret_error",r.BrigeError="brige_error",r.BlankScreen="blank_screen",r.CustomError="custom_error",r.PageNotFoundError="page_not_found_error",r.LazyLoadError="lazy_load_error"}(o||(o={})),function(r){r.PV="pv",r.Api="api"}(i||(i={}));class u{constructor(r,n){this.projectName=r,this.env=n}getRemoteConfig(){return r(this,void 0,void 0,(function*(){const e=`${this.projectName}-${this.env}`;if(u.remoteConfigCache.has(e))return u.remoteConfigCache.get(e);try{const t=(()=>r(this,void 0,void 0,(function*(){const r=yield fetch(`https://yyb.qpic.cn/yyb-alarm-cloud-config/${this.projectName}/config${this.env===n.Prod?"":"_test"}.json`);let e={};try{e=yield r.json()}catch(r){console.log("返回值错误")}return e})))();return u.remoteConfigCache.set(e,t),t}catch(r){throw console.error("Fetch config failed:",r),u.remoteConfigCache.delete(e),r}}))}}u.remoteConfigCache=new Map;const c={name:"block-js-error",onBeforeSend(n,t,o){return r(this,void 0,void 0,(function*(){if(!o.remoteConfigPromise)return n;const{blockJsErrors:r}=yield o.remoteConfigPromise;if(!r)return n;return n.filter((n=>!r.some((r=>((r,n)=>{switch(n.matchMode){case e.Regex:return new RegExp(n.matchValue).test(r);case e.StartWith:return r.startsWith(n.matchValue);case e.Contains:return r.includes(n.matchValue);default:return!1}})(n.msg,r)))))}))}},f=[0,"0","200",200];let a=!1;const s={name:"page-error-rate",onBeforeSend(r){r.find((r=>r.type===i.PV))&&(a=!1);const n=r.find((r=>[o.Error,o.PromiseError,o.CustomError].includes(r.level)&&r.type!==i.Api));return n&&!a&&(a=!0,n.name="page_error_rate_abnormal_pv"),r}};let l=!1;const d={name:"asset_error_rate",onBeforeSend(r){r.find((r=>r.type===i.PV))&&(l=!1);const n=r.find((r=>[o.ImageError,o.ScriptError,o.CSSError,o.MediaError,o.PageNotFoundError].includes(r.level)&&r.type!==i.Api));return n&&!l&&(l=!0,n.name="asset_error_rate_abnormal_pv"),r}},v=[],p={name:"report_session_log",onBeforeSend(r,n){var e;if(!g(n))return r;let t;for(const n of r)(null===(e=null==n?void 0:n.level)||void 0===e?void 0:e.includes("error"))&&(!t||n.timestamp>(null==t?void 0:t.timestamp))&&(t=n);if(!t)return r;const o=v.findIndex((r=>r.timestamp===(null==t?void 0:t.timestamp)&&r.type===(null==t?void 0:t.type)&&r.msg===(null==t?void 0:t.msg)));if(-1!==o)return r;const i=v.slice(0,o),u=new Set;for(const n of r)u.add(`${n.type}_${n.timestamp}_${n.msg}`);for(const n of i){const e=`${n.type}_${n.timestamp}_${n.msg}`;u.has(e)||(r.push(n),u.add(e))}return r},onCollected(r,n){if(!g(n))return r;if(v.length>1e3){const n=r.length-1;v.splice(0,n)}return v.push(...r),r}};function g(r){const{sampleMap:n}=r;return Object.values(n).some((r=>r&&r<1&&r>0))}const h=new class{constructor(){this.plugins=[]}use(r){if(void 0===r.name)throw new Error("插件必须定义 name 标识");this.plugins.push(r)}process(n,e,t,o){return r(this,void 0,void 0,(function*(){const r=this.plugins.filter((r=>!!r[o])).map((r=>({handler:r[o],name:r.name})));let i=[...n];for(const n of r)try{i=yield n.handler([...i],e,t)}catch(r){console.error(`[LogPipeline] 插件 ${n.name} 执行失败:`,r)}return i}))}};h.use(c),h.use(s),h.use(d),h.use(p);var m,y,b="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},w={};function C(){if(y)return m;y=1;var r=function(r){return r&&r.Math===Math&&r};return m=r("object"==typeof globalThis&&globalThis)||r("object"==typeof window&&window)||r("object"==typeof self&&self)||r("object"==typeof b&&b)||r("object"==typeof m&&m)||function(){return this}()||Function("return this")()}var S,O,E,j,_,P,R,N,k={};function x(){return O?S:(O=1,S=function(r){try{return!!r()}catch(r){return!0}})}function B(){if(j)return E;j=1;var r=x();return E=!r((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]}))}function A(){if(P)return _;P=1;var r=x();return _=!r((function(){var r=function(){}.bind();return"function"!=typeof r||r.hasOwnProperty("prototype")}))}function T(){if(N)return R;N=1;var r=A(),n=Function.prototype.call;return R=r?n.bind(n):function(){return n.apply(n,arguments)},R}var I,M,D,F,L,$,H,W,V,z,G,U,J,q,K,X,Y,Q,Z,rr,nr,er,tr,or,ir,ur,cr,fr,ar,sr,lr,dr,vr,pr,gr,hr,mr,yr,br,wr,Cr,Sr={};function Or(){return D?M:(D=1,M=function(r,n){return{enumerable:!(1&r),configurable:!(2&r),writable:!(4&r),value:n}})}function Er(){if(L)return F;L=1;var r=A(),n=Function.prototype,e=n.call,t=r&&n.bind.bind(e,e);return F=r?t:function(r){return function(){return e.apply(r,arguments)}},F}function jr(){if(H)return $;H=1;var r=Er(),n=r({}.toString),e=r("".slice);return $=function(r){return e(n(r),8,-1)}}function _r(){if(V)return W;V=1;var r=Er(),n=x(),e=jr(),t=Object,o=r("".split);return W=n((function(){return!t("z").propertyIsEnumerable(0)}))?function(r){return"String"===e(r)?o(r,""):t(r)}:t}function Pr(){return G?z:(G=1,z=function(r){return null==r})}function Rr(){if(J)return U;J=1;var r=Pr(),n=TypeError;return U=function(e){if(r(e))throw new n("Can't call method on "+e);return e}}function Nr(){if(K)return q;K=1;var r=_r(),n=Rr();return q=function(e){return r(n(e))}}function kr(){if(Y)return X;Y=1;var r="object"==typeof document&&document.all;return X=void 0===r&&void 0!==r?function(n){return"function"==typeof n||n===r}:function(r){return"function"==typeof r}}function xr(){if(Z)return Q;Z=1;var r=kr();return Q=function(n){return"object"==typeof n?null!==n:r(n)}}function Br(){if(nr)return rr;nr=1;var r=C(),n=kr();return rr=function(e,t){return arguments.length<2?(o=r[e],n(o)?o:void 0):r[e]&&r[e][t];var o},rr}function Ar(){if(ir)return or;ir=1;var r=C().navigator,n=r&&r.userAgent;return or=n?String(n):""}function Tr(){if(cr)return ur;cr=1;var r,n,e=C(),t=Ar(),o=e.process,i=e.Deno,u=o&&o.versions||i&&i.version,c=u&&u.v8;return c&&(n=(r=c.split("."))[0]>0&&r[0]<4?1:+(r[0]+r[1])),!n&&t&&(!(r=t.match(/Edge\/(\d+)/))||r[1]>=74)&&(r=t.match(/Chrome\/(\d+)/))&&(n=+r[1]),ur=n}function Ir(){if(ar)return fr;ar=1;var r=Tr(),n=x(),e=C().String;return fr=!!Object.getOwnPropertySymbols&&!n((function(){var n=Symbol("symbol detection");return!e(n)||!(Object(n)instanceof Symbol)||!Symbol.sham&&r&&r<41}))}function Mr(){if(lr)return sr;lr=1;var r=Ir();return sr=r&&!Symbol.sham&&"symbol"==typeof Symbol.iterator}function Dr(){if(vr)return dr;vr=1;var r=Br(),n=kr(),e=function(){if(tr)return er;tr=1;var r=Er();return er=r({}.isPrototypeOf)}(),t=Mr(),o=Object;return dr=t?function(r){return"symbol"==typeof r}:function(t){var i=r("Symbol");return n(i)&&e(i.prototype,o(t))}}function Fr(){if(gr)return pr;gr=1;var r=String;return pr=function(n){try{return r(n)}catch(r){return"Object"}}}function Lr(){if(mr)return hr;mr=1;var r=kr(),n=Fr(),e=TypeError;return hr=function(t){if(r(t))return t;throw new e(n(t)+" is not a function")}}function $r(){if(br)return yr;br=1;var r=Lr(),n=Pr();return yr=function(e,t){var o=e[t];return n(o)?void 0:r(o)}}function Hr(){if(Cr)return wr;Cr=1;var r=T(),n=kr(),e=xr(),t=TypeError;return wr=function(o,i){var u,c;if("string"===i&&n(u=o.toString)&&!e(c=r(u,o)))return c;if(n(u=o.valueOf)&&!e(c=r(u,o)))return c;if("string"!==i&&n(u=o.toString)&&!e(c=r(u,o)))return c;throw new t("Can't convert object to primitive value")}}var Wr,Vr,zr,Gr,Ur,Jr,qr,Kr,Xr,Yr,Qr,Zr,rn,nn,en,tn,on,un,cn,fn,an,sn,ln,dn,vn={exports:{}};function pn(){if(Gr)return zr;Gr=1;var r=C(),n=Object.defineProperty;return zr=function(e,t){try{n(r,e,{value:t,configurable:!0,writable:!0})}catch(n){r[e]=t}return t}}function gn(){if(Ur)return vn.exports;Ur=1;var r=Vr?Wr:(Vr=1,Wr=!1),n=C(),e=pn(),t="__core-js_shared__",o=vn.exports=n[t]||e(t,{});return(o.versions||(o.versions=[])).push({version:"3.41.0",mode:r?"pure":"global",copyright:"© 2014-2025 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.41.0/LICENSE",source:"https://github.com/zloirock/core-js"}),vn.exports}function hn(){if(qr)return Jr;qr=1;var r=gn();return Jr=function(n,e){return r[n]||(r[n]=e||{})}}function mn(){if(Xr)return Kr;Xr=1;var r=Rr(),n=Object;return Kr=function(e){return n(r(e))}}function yn(){if(Qr)return Yr;Qr=1;var r=Er(),n=mn(),e=r({}.hasOwnProperty);return Yr=Object.hasOwn||function(r,t){return e(n(r),t)}}function bn(){if(rn)return Zr;rn=1;var r=Er(),n=0,e=Math.random(),t=r(1..toString);return Zr=function(r){return"Symbol("+(void 0===r?"":r)+")_"+t(++n+e,36)}}function wn(){if(en)return nn;en=1;var r=C(),n=hn(),e=yn(),t=bn(),o=Ir(),i=Mr(),u=r.Symbol,c=n("wks"),f=i?u.for||u:u&&u.withoutSetter||t;return nn=function(r){return e(c,r)||(c[r]=o&&e(u,r)?u[r]:f("Symbol."+r)),c[r]}}function Cn(){if(on)return tn;on=1;var r=T(),n=xr(),e=Dr(),t=$r(),o=Hr(),i=wn(),u=TypeError,c=i("toPrimitive");return tn=function(i,f){if(!n(i)||e(i))return i;var a,s=t(i,c);if(s){if(void 0===f&&(f="default"),a=r(s,i,f),!n(a)||e(a))return a;throw new u("Can't convert object to primitive value")}return void 0===f&&(f="number"),o(i,f)}}function Sn(){if(cn)return un;cn=1;var r=Cn(),n=Dr();return un=function(e){var t=r(e,"string");return n(t)?t:t+""}}function On(){if(ln)return sn;ln=1;var r=B(),n=x(),e=function(){if(an)return fn;an=1;var r=C(),n=xr(),e=r.document,t=n(e)&&n(e.createElement);return fn=function(r){return t?e.createElement(r):{}}}();return sn=!r&&!n((function(){return 7!==Object.defineProperty(e("div"),"a",{get:function(){return 7}}).a}))}function En(){if(dn)return k;dn=1;var r=B(),n=T(),e=function(){if(I)return Sr;I=1;var r={}.propertyIsEnumerable,n=Object.getOwnPropertyDescriptor,e=n&&!r.call({1:2},1);return Sr.f=e?function(r){var e=n(this,r);return!!e&&e.enumerable}:r,Sr}(),t=Or(),o=Nr(),i=Sn(),u=yn(),c=On(),f=Object.getOwnPropertyDescriptor;return k.f=r?f:function(r,a){if(r=o(r),a=i(a),c)try{return f(r,a)}catch(r){}if(u(r,a))return t(!n(e.f,r,a),r[a])},k}var jn,_n,Pn,Rn,Nn,kn,xn,Bn={};function An(){if(Rn)return Pn;Rn=1;var r=xr(),n=String,e=TypeError;return Pn=function(t){if(r(t))return t;throw new e(n(t)+" is not an object")}}function Tn(){if(Nn)return Bn;Nn=1;var r=B(),n=On(),e=function(){if(_n)return jn;_n=1;var r=B(),n=x();return jn=r&&n((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))}(),t=An(),o=Sn(),i=TypeError,u=Object.defineProperty,c=Object.getOwnPropertyDescriptor,f="enumerable",a="configurable",s="writable";return Bn.f=r?e?function(r,n,e){if(t(r),n=o(n),t(e),"function"==typeof r&&"prototype"===n&&"value"in e&&s in e&&!e[s]){var i=c(r,n);i&&i[s]&&(r[n]=e.value,e={configurable:a in e?e[a]:i[a],enumerable:f in e?e[f]:i[f],writable:!1})}return u(r,n,e)}:u:function(r,e,c){if(t(r),e=o(e),t(c),n)try{return u(r,e,c)}catch(r){}if("get"in c||"set"in c)throw new i("Accessors not supported");return"value"in c&&(r[e]=c.value),r},Bn}function In(){if(xn)return kn;xn=1;var r=B(),n=Tn(),e=Or();return kn=r?function(r,t,o){return n.f(r,t,e(1,o))}:function(r,n,e){return r[n]=e,r}}var Mn,Dn,Fn,Ln,$n,Hn,Wn,Vn,zn,Gn,Un,Jn,qn,Kn,Xn,Yn={exports:{}};function Qn(){if(Ln)return Fn;Ln=1;var r=Er(),n=kr(),e=gn(),t=r(Function.toString);return n(e.inspectSource)||(e.inspectSource=function(r){return t(r)}),Fn=e.inspectSource}function Zn(){if(Vn)return Wn;Vn=1;var r=hn(),n=bn(),e=r("keys");return Wn=function(r){return e[r]||(e[r]=n(r))}}function re(){return Gn?zn:(Gn=1,zn={})}function ne(){if(Jn)return Un;Jn=1;var r,n,e,t=function(){if(Hn)return $n;Hn=1;var r=C(),n=kr(),e=r.WeakMap;return $n=n(e)&&/native code/.test(String(e))}(),o=C(),i=xr(),u=In(),c=yn(),f=gn(),a=Zn(),s=re(),l="Object already initialized",d=o.TypeError,v=o.WeakMap;if(t||f.state){var p=f.state||(f.state=new v);p.get=p.get,p.has=p.has,p.set=p.set,r=function(r,n){if(p.has(r))throw new d(l);return n.facade=r,p.set(r,n),n},n=function(r){return p.get(r)||{}},e=function(r){return p.has(r)}}else{var g=a("state");s[g]=!0,r=function(r,n){if(c(r,g))throw new d(l);return n.facade=r,u(r,g,n),n},n=function(r){return c(r,g)?r[g]:{}},e=function(r){return c(r,g)}}return Un={set:r,get:n,has:e,enforce:function(t){return e(t)?n(t):r(t,{})},getterFor:function(r){return function(e){var t;if(!i(e)||(t=n(e)).type!==r)throw new d("Incompatible receiver, "+r+" required");return t}}}}function ee(){if(qn)return Yn.exports;qn=1;var r=Er(),n=x(),e=kr(),t=yn(),o=B(),i=function(){if(Dn)return Mn;Dn=1;var r=B(),n=yn(),e=Function.prototype,t=r&&Object.getOwnPropertyDescriptor,o=n(e,"name"),i=o&&"something"===function(){}.name,u=o&&(!r||r&&t(e,"name").configurable);return Mn={EXISTS:o,PROPER:i,CONFIGURABLE:u}}().CONFIGURABLE,u=Qn(),c=ne(),f=c.enforce,a=c.get,s=String,l=Object.defineProperty,d=r("".slice),v=r("".replace),p=r([].join),g=o&&!n((function(){return 8!==l((function(){}),"length",{value:8}).length})),h=String(String).split("String"),m=Yn.exports=function(r,n,e){"Symbol("===d(s(n),0,7)&&(n="["+v(s(n),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),e&&e.getter&&(n="get "+n),e&&e.setter&&(n="set "+n),(!t(r,"name")||i&&r.name!==n)&&(o?l(r,"name",{value:n,configurable:!0}):r.name=n),g&&e&&t(e,"arity")&&r.length!==e.arity&&l(r,"length",{value:e.arity});try{e&&t(e,"constructor")&&e.constructor?o&&l(r,"prototype",{writable:!1}):r.prototype&&(r.prototype=void 0)}catch(r){}var u=f(r);return t(u,"source")||(u.source=p(h,"string"==typeof n?n:"")),r};return Function.prototype.toString=m((function(){return e(this)&&a(this).source||u(this)}),"toString"),Yn.exports}function te(){if(Xn)return Kn;Xn=1;var r=kr(),n=Tn(),e=ee(),t=pn();return Kn=function(o,i,u,c){c||(c={});var f=c.enumerable,a=void 0!==c.name?c.name:i;if(r(u)&&e(u,a,c),c.global)f?o[i]=u:t(i,u);else{try{c.unsafe?o[i]&&(f=!0):delete o[i]}catch(r){}f?o[i]=u:n.f(o,i,{value:u,enumerable:!1,configurable:!c.nonConfigurable,writable:!c.nonWritable})}return o}}var oe,ie,ue,ce,fe,ae,se,le,de,ve,pe,ge,he,me,ye,be,we,Ce={};function Se(){if(ce)return ue;ce=1;var r=function(){if(ie)return oe;ie=1;var r=Math.ceil,n=Math.floor;return oe=Math.trunc||function(e){var t=+e;return(t>0?n:r)(t)}}();return ue=function(n){var e=+n;return e!=e||0===e?0:r(e)}}function Oe(){if(ae)return fe;ae=1;var r=Se(),n=Math.max,e=Math.min;return fe=function(t,o){var i=r(t);return i<0?n(i+o,0):e(i,o)}}function Ee(){if(le)return se;le=1;var r=Se(),n=Math.min;return se=function(e){var t=r(e);return t>0?n(t,9007199254740991):0}}function je(){if(ve)return de;ve=1;var r=Ee();return de=function(n){return r(n.length)}}function _e(){if(me)return he;me=1;var r=Er(),n=yn(),e=Nr(),t=function(){if(ge)return pe;ge=1;var r=Nr(),n=Oe(),e=je(),t=function(t){return function(o,i,u){var c=r(o),f=e(c);if(0===f)return!t&&-1;var a,s=n(u,f);if(t&&i!=i){for(;f>s;)if((a=c[s++])!=a)return!0}else for(;f>s;s++)if((t||s in c)&&c[s]===i)return t||s||0;return!t&&-1}};return pe={includes:t(!0),indexOf:t(!1)}}().indexOf,o=re(),i=r([].push);return he=function(r,u){var c,f=e(r),a=0,s=[];for(c in f)!n(o,c)&&n(f,c)&&i(s,c);for(;u.length>a;)n(f,c=u[a++])&&(~t(s,c)||i(s,c));return s}}var Pe,Re,Ne,ke,xe,Be,Ae,Te,Ie,Me,De,Fe,Le,$e,He,We,Ve,ze,Ge={};function Ue(){if(Ne)return Re;Ne=1;var r=Br(),n=Er(),e=function(){if(we)return Ce;we=1;var r=_e(),n=(be?ye:(be=1,ye=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"])).concat("length","prototype");return Ce.f=Object.getOwnPropertyNames||function(e){return r(e,n)},Ce}(),t=(Pe||(Pe=1,Ge.f=Object.getOwnPropertySymbols),Ge),o=An(),i=n([].concat);return Re=r("Reflect","ownKeys")||function(r){var n=e.f(o(r)),u=t.f;return u?i(n,u(r)):n}}function Je(){if(xe)return ke;xe=1;var r=yn(),n=Ue(),e=En(),t=Tn();return ke=function(o,i,u){for(var c=n(i),f=t.f,a=e.f,s=0;s<c.length;s++){var l=c[s];r(o,l)||u&&r(u,l)||f(o,l,a(i,l))}}}function qe(){if(Ie)return Te;Ie=1;var r=C(),n=En().f,e=In(),t=te(),o=pn(),i=Je(),u=function(){if(Ae)return Be;Ae=1;var r=x(),n=kr(),e=/#|\.prototype\./,t=function(e,t){var f=i[o(e)];return f===c||f!==u&&(n(t)?r(t):!!t)},o=t.normalize=function(r){return String(r).replace(e,".").toLowerCase()},i=t.data={},u=t.NATIVE="N",c=t.POLYFILL="P";return Be=t}();return Te=function(c,f){var a,s,l,d,v,p=c.target,g=c.global,h=c.stat;if(a=g?r:h?r[p]||o(p,{}):r[p]&&r[p].prototype)for(s in f){if(d=f[s],l=c.dontCallGetSet?(v=n(a,s))&&v.value:a[s],!u(g?s:p+(h?".":"#")+s,c.forced)&&void 0!==l){if(typeof d==typeof l)continue;i(d,l)}(c.sham||l&&l.sham)&&e(d,"sham",!0),t(a,s,d,c)}}}function Ke(){if(Le)return Fe;Le=1;var r=x();return Fe=function(n,e){var t=[][n];return!!t&&r((function(){t.call(null,e||function(){return 1},1)}))}}function Xe(){if(Ve)return We;Ve=1;var r=function(){if(He)return $e;He=1;var r=C(),n=Ar(),e=jr(),t=function(r){return n.slice(0,r.length)===r};return $e=t("Bun/")?"BUN":t("Cloudflare-Workers")?"CLOUDFLARE":t("Deno/")?"DENO":t("Node.js/")?"NODE":r.Bun&&"string"==typeof Bun.version?"BUN":r.Deno&&"object"==typeof Deno.version?"DENO":"process"===e(r.process)?"NODE":r.window&&r.document?"BROWSER":"REST"}();return We="NODE"===r}function Ye(r){const n=["ret","retcode","code","success","errno","errcode","err_code","ret_code"],e=Object.keys(r).reduce(((r,n)=>(r[n.toLowerCase()]=n,r)),{});for(const t of n){const n=e[t.toLowerCase()],o=n?r[n]:void 0;if(Qe(o))return o}return"unknown_code"}function Qe(r){return"string"==typeof r||"number"==typeof r}function Ze(r){if("string"==typeof r)try{return JSON.parse(r)}catch(r){return{}}return"object"==typeof r&&null!==r?r:{}}!function(){if(ze)return w;ze=1;var r=qe(),n=function(){if(De)return Me;De=1;var r=Lr(),n=mn(),e=_r(),t=je(),o=TypeError,i="Reduce of empty array with no initial value",u=function(u){return function(c,f,a,s){var l=n(c),d=e(l),v=t(l);if(r(f),0===v&&a<2)throw new o(i);var p=u?v-1:0,g=u?-1:1;if(a<2)for(;;){if(p in d){s=d[p],p+=g;break}if(p+=g,u?p<0:v<=p)throw new o(i)}for(;u?p>=0:v>p;p+=g)p in d&&(s=f(s,d[p],p,l));return s}};return Me={left:u(!1),right:u(!0)}}().left,e=Ke(),t=Tr();r({target:"Array",proto:!0,forced:!Xe()&&t>79&&t<83||!e("reduce")},{reduce:function(r){var e=arguments.length;return n(this,r,e,e>1?arguments[1]:void 0)}})}();return class{constructor(r,n){this.getCodeValue=Ye,this.successCodes=f,this.sdkConfig=r,this.aegisConfig=n}genAegisConfig(){var r,e,t,o;const{enableRemoteConfig:i,projectName:c}=this.sdkConfig,f=(null===(r=this.aegisConfig)||void 0===r?void 0:r.env)||n.Prod;i&&(this.remoteConfigPromise=new u(c,f).getRemoteConfig());const a=this.genRetCodeHandler(),s=this.genOnBeforeSend(),l=this.genOnNewAegis(),d=this.genOnCollected();return Object.assign(Object.assign({},this.aegisConfig),{onBeforeSend:s,onCollected:d,onNewAegis:l,forceReportErrorLog:!0,plugin:Object.assign(Object.assign({},null===(e=this.aegisConfig)||void 0===e?void 0:e.plugin),{api:Object.assign(Object.assign({apiDetail:!0,resHeaders:["trpc-error-ret","trpc-error-msg"]},null===(o=null===(t=this.aegisConfig)||void 0===t?void 0:t.plugin)||void 0===o?void 0:o.api),{retCodeHandler:a})})})}buildBaseRetCodeHandler(){var r,n;const e=null===(n=null===(r=this.aegisConfig)||void 0===r?void 0:r.api)||void 0===n?void 0:n.retCodeHandler;return(r,n,t)=>{const o=Ze(r);if(e)try{const r=e(o,n,t);if(!1===(null==r?void 0:r.isErr))return r}catch(r){console.error("用户处理器异常",r)}const i=Ye(o);return{isErr:!f.includes(i),code:i}}}setupRemoteRetCodeHandler(n){return(e,t,o)=>r(this,void 0,void 0,(function*(){var r,i;const u=Ze(e);let c;const f=yield this.remoteConfigPromise;if(c=null==f?void 0:f.retCodeHandlerStr,c||(c=null===(r=function(){const r=localStorage.getItem("AEGIS_REMOTE_CONFIG");if(r)try{return JSON.parse(r)}catch(r){return{}}return{}}())||void 0===r?void 0:r.retCodeHandlerStr),!c)return n(e,t,o);const a=this.createRemoteHandler(c);return null!==(i=null==a?void 0:a(u,t,o))&&void 0!==i?i:n(e,t,o)}))}createRemoteHandler(r){try{const n=`\n        return function handler(resp, url, ctx) {\n          try {\n            ${r}\n            // 兜底返回业务逻辑判断结果\n            const finalCode = this.getCodeValue(resp);\n            return {\n              isErr: !this.successCodes.includes(Number(finalCode)),\n              code: finalCode\n            };\n          } catch(err) {\n            return { isErr: true, code: 'handler_exec_error' };\n          }\n        }\n      `;return new Function(n)().bind(this)}catch(r){return console.error("retCodeHandler 生成失败",r),null}}genRetCodeHandler(){const r=this.buildBaseRetCodeHandler();return this.remoteConfigPromise?this.setupRemoteRetCodeHandler(r):r}genOnBeforeSend(){var n;const e=null===(n=this.aegisConfig)||void 0===n?void 0:n.onBeforeSend;return(n,t)=>r(this,void 0,void 0,(function*(){const r=yield h.process(n,t,this,"onBeforeSend");return e?yield e(r,t):r}))}genOnCollected(){var n;const e=null===(n=this.aegisConfig)||void 0===n?void 0:n.onCollected;return(n,t)=>r(this,void 0,void 0,(function*(){const r=yield h.process(n,t,this,"onCollected");return e?yield e(r,t):r}))}genOnNewAegis(){var r,n;const e=null===(r=this.aegisConfig)||void 0===r?void 0:r.onNewAegis,t=null===(n=this.sdkConfig)||void 0===n?void 0:n.collectConsoleError;return(r,n,o)=>{if(t){const r=window.console.error;window.console.error=(...e)=>{const t=Array.from(e).map((r=>r instanceof Error?`Error: ${r.toString()} Stack: ${r.stack}`:r instanceof Object?function(r){try{return JSON.stringify(r)}catch(r){return""}}(r):r.toString()));n.error(t.join("_____")),r.call(window.console,...e)}}null==e||e(r,n,o)}}}}));
