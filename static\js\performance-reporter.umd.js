﻿var be=Object.defineProperty,Ie=Object.defineProperties;var De=Object.getOwnPropertyDescriptors;var Rt=Object.getOwnPropertySymbols;var Re=Object.prototype.hasOwnProperty,Ce=Object.prototype.propertyIsEnumerable;var Lt=Math.pow,Ct=(s,f,v)=>f in s?be(s,f,{enumerable:!0,configurable:!0,writable:!0,value:v}):s[f]=v,E=(s,f)=>{for(var v in f||(f={}))Re.call(f,v)&&Ct(s,v,f[v]);if(Rt)for(var v of Rt(f))Ce.call(f,v)&&Ct(s,v,f[v]);return s},_=(s,f)=>Ie(s,De(f));var Y=(s,f,v)=>new Promise((D,V)=>{var x=h=>{try{R(v.next(h))}catch(y){V(y)}},F=h=>{try{R(v.throw(h))}catch(y){V(y)}},R=h=>h.done?D(h.value):Promise.resolve(h.value).then(x,F);R((v=v.apply(s,f)).next())});(function(s,f){typeof exports=="object"&&typeof module!="undefined"?f(exports):typeof define=="function"&&define.amd?define(["exports"],f):(s=typeof globalThis!="undefined"?globalThis:s||self,f(s.PerformanceReporter={}))})(this,function(s){"use strict";function f(t,e){return Object.prototype.hasOwnProperty.call(t,e)}var v=function(t,e,n,r){e=e||"&",n=n||"=";var i={};if(typeof t!="string"||t.length===0)return i;var o=/\+/g;t=t.split(e);var d=1e3;r&&typeof r.maxKeys=="number"&&(d=r.maxKeys);var c=t.length;d>0&&c>d&&(c=d);for(var p=0;p<c;++p){var u=t[p].replace(o,"%20"),m=u.indexOf(n),T,b,S,I;m>=0?(T=u.substr(0,m),b=u.substr(m+1)):(T=u,b=""),S=decodeURIComponent(T),I=decodeURIComponent(b),f(i,S)?Array.isArray(i[S])?i[S].push(I):i[S]=[i[S],I]:i[S]=I}return i},D=function(t){switch(typeof t){case"string":return t;case"boolean":return t?"true":"false";case"number":return isFinite(t)?t:"";default:return""}},V=function(t,e,n,r){return e=e||"&",n=n||"=",t===null&&(t=void 0),typeof t=="object"?Object.keys(t).map(function(i){var o=encodeURIComponent(D(i))+n;return Array.isArray(t[i])?t[i].map(function(d){return o+encodeURIComponent(D(d))}).join(e):o+encodeURIComponent(D(t[i]))}).filter(Boolean).join(e):r?encodeURIComponent(D(r))+n+encodeURIComponent(D(t)):""},x,F;F=v,x=V;var R=(t=>(t.Formal="formal",t.Test="test",t))(R||{});function h(t){return t||""}const y=typeof window=="undefined";function J(t){return y?{}.DOCKER_ENV==="formal":globalThis.isProduction!==void 0?!!globalThis.isProduction:t?t===R.Formal:R.Formal}function Z(t){return!!new URLSearchParams(t).get("isTest")}var q=(t=>(t.Yyb="yyb",t.Ysdk="ysdk",t.Mqq="mqq",t.Wx="wx",t.WxMiniProgram="wxMiniProgram",t.Other="other",t))(q||{});const wt=[{appEnv:"yyb",includes:[/\/qqdownloader\/(\d+)(?:\/(appdetail|external))?/]},{appEnv:"wx",includes:[/MicroMessenger\/([\d.]+)/],excludes:[/wxwork|miniProgram/]},{appEnv:"mqq",includes:[/QQ\/([\d.]+)/]},{appEnv:"wxMiniProgram",includes:[/MicroMessenger\/([\d.]+)/,/miniProgram/]},{appEnv:"ysdk",includes:[/YSDKVersion\/([\d.]+)/]}];function N(t){var n;const e=wt.find(({includes:r=[],excludes:i=[]})=>i.some(o=>o.test(t))?!1:r.every(o=>o.test(t)));return(n=e==null?void 0:e.appEnv)!=null?n:"other"}function K(t){return/MQQBrowser\//.test(t)}function tt(t){var r;const n=/Chrome\/(\d+)/i.exec(t);return Number((r=n==null?void 0:n[1])!=null?r:0)}var et=(t=>(t.Android="android",t.Ios="ios",t.Other="other",t))(et||{});function nt(t){const n=/Android ([\d.]+);/i.exec(t);if(n)return{type:"android",version:h(n[1])};const r=t.indexOf("iPhone")>=0,i=/(iPad).*OS\s([\d_]+)/.test(t);if(r||i){const c=/OS ([\d_]+)/i.exec(t);return{type:"ios",version:h(c==null?void 0:c[1])}}return{type:"other",version:""}}function rt(){const t="0";if(!globalThis.tbsJs)return{isX5:!1,coreVersion:t,sdkVersion:t};const{TBSVC:e,COVC:n}=F(tbsJs.getQUA2());return{isX5:!0,coreVersion:n,sdkVersion:e}}!y&&window.globalThis===void 0&&(window.globalThis=window);/*!
 * cookie
 * Copyright(c) 2012-2014 Roman Shtylman
 * Copyright(c) 2015 Douglas Christopher Wilson
 * MIT Licensed
 */var it=Bt;function Bt(t,e){if(typeof t!="string")throw new TypeError("argument str must be a string");for(var n={},r=e||{},i=r.decode||Vt,o=0;o<t.length;){var d=t.indexOf("=",o);if(d===-1)break;var c=t.indexOf(";",o);if(c===-1)c=t.length;else if(c<d){o=t.lastIndexOf(";",d-1)+1;continue}var p=t.slice(o,d).trim();if(n[p]===void 0){var u=t.slice(d+1,c).trim();u.charCodeAt(0)===34&&(u=u.slice(1,-1)),n[p]=xt(u,i)}o=c+1}return n}function Vt(t){return t.indexOf("%")!==-1?decodeURIComponent(t):t}function xt(t,e){try{return e(t)}catch(n){return t}}var a=(t=>(t.BrowserRequestReceived="browserRequestReceived",t.RenderStart="renderStart",t.ApiRequestStart="apiRequestStart",t.ApiRequestEnd="apiRequestEnd",t.RenderEnd="renderEnd",t.ResponseSend="responseSend",t.NavigationStart="navigationStart",t.UnloadStart="unloadStart",t.UnloadEnd="unloadEnd",t.RedirectStart="redirectStart",t.RedirectEnd="redirectEnd",t.FetchStart="fetchStart",t.DnsLookupStart="domainLookupStart",t.DnsLookupEnd="domainLookupEnd",t.TcpConnectStart="tcpConnectStart",t.TcpConnectEnd="tcpConnectEnd",t.SslConnectStart="sslConnectStart",t.RequestStart="requestStart",t.ResponseStart="responseStart",t.ResponseEnd="responseEnd",t.DomLoading="domLoading",t.DomInteractive="domInteractive",t.DomContentLoadedStart="domContentLoadedStart",t.DomContentLoadedEnd="domContentLoadedEnd",t.DomComplete="domComplete",t.LoadStart="loadStart",t.LoadEnd="loadEnd",t.Fcp="fcp",t.Lcp="lcp",t.Fid="fid",t.HtmlFirst="htmlFirst",t.ScriptFirst="scriptFirst",t.FirstVisible="firstVisible",t.Mounted="mounted",t.FirstOperable="firstOperable",t.PageExit="pageExit",t.ResourceTiming="resourceTiming",t))(a||{});const ot=["browserRequestReceived","renderStart","apiRequestStart","apiRequestEnd","renderEnd","responseSend"],at=["navigationStart","unloadStart","unloadEnd","redirectStart","redirectEnd","fetchStart","domainLookupStart","domainLookupEnd","tcpConnectStart","tcpConnectEnd","sslConnectStart","requestStart","responseStart","responseEnd","domLoading","domInteractive","domContentLoadedStart","domContentLoadedEnd","domComplete","loadStart","loadEnd"],st=["fcp","lcp","fid"],dt=["htmlFirst","scriptFirst","firstVisible","mounted","firstOperable","pageExit"];var L=(t=>(t.Server="server",t.PerfApi="perfApi",t.WebVitals="webVitals",t.Point="point",t.Business="business",t))(L||{});function M(t){return ot.includes(t)?"server":at.includes(t)?"perfApi":st.includes(t)?"webVitals":dt.includes(t)?"point":"business"}const X="https://h.trace.qq.com/kv",Ft={attaId:"***********",attaToken:"9122499932"},At={attaId:"04b00071355",attaToken:"2514496996"};function Ot(t){return J(t)?At:Ft}const kt="Tencent_Security_Team";function Ut(t){return t.indexOf(kt)>-1}function Pt(t){const{url:e,data:n,header:r={}}=t,i=new XMLHttpRequest;i.open("POST",e),Object.keys(r).forEach(o=>{i.setRequestHeader(o,r[o])}),i.onreadystatechange=function(){i.readyState===4&&i.status!==200&&i.status!==304&&console.warn(`atta report fail: ${e} => ${i.status}: ${n}`)},i.timeout=1e4,i.ontimeout=function(){i.abort(),console.warn("atta report timeout")},i.send(n)}function _t(t){const e=new Image(1,1),{url:n,data:r}=t;e.src=`${n}?${r}`}let C=[],A;function qt(t){const e=C.map(n=>$(n));return JSON.stringify({attaid:t.attaId,token:t.attaToken,type:"batch",version:"v1.0.0",datas:e})}function $(t,e){const n=[];return e&&n.push(`attaid=${e.attaId}`,`token=${e.attaToken}`),Object.keys(t).forEach(r=>{n.push(`${r}=${encodeURIComponent(t[r])}`)}),n.push(`_dc=${Math.random()}`),n.join("&")}function ct(t,e,n=!1,r=!1){if(!Ut(navigator.userAgent)){Z(location.href)&&console.log("performance reporter:",t.eventName,t);try{if(C.push(t),n){C.forEach(i=>{const o=new Blob([$(i,e)],{type:"application/x-www-form-urlencoded"});navigator.sendBeacon(X,o)}),A=null,C=[];return}if(A)return;A=setTimeout(()=>{r?C.forEach(i=>{_t({url:X,data:$(i,e)})}):Pt({url:X,header:{"content-type":"application/json","Atta-type":"batch-report"},data:qt(e)}),A=null,C=[]},100)}catch(i){console.error(`atta report fail, err: ${i}`)}}}class Nt{constructor(e){this.attaInfo=Ot(e)}}class Mt extends Nt{constructor(e,n){const{reportEnv:r,publicParams:i,publicExtraData:o}=n!=null?n:{};super(r),this.reportedEvents=[],this.reportInfoService=e,i&&this.reportInfoService.setPublicParams(i),o&&this.reportInfoService.setPublicExtraData(o),this.isNeedReport=this.sampling(n)}report(e){return Y(this,null,function*(){const{event:n,eventTime:r,extraData:i,ignoreSampling:o=!1}=e;if(!o&&!this.isNeedReport||this.reportedEvents.includes(n))return;this.reportedEvents.push(n);const d=this.reportInfoService.getReportInfo(n,i,r);this.reportInfoService.addReportTiming(n,d.eventTime),ct(d,this.attaInfo,n===a.PageExit,this.isSendGet())})}reportSimple(e){return Y(this,null,function*(){const{event:n,eventTime:r,extraData:i,isSendBeacon:o=!1,ignoreSampling:d=!1}=e;if(!d&&!this.isNeedReport)return;const c=this.reportInfoService.getSimpleReportInfo(n,i,r);ct(c,this.attaInfo,o,this.isSendGet())})}setPublicExtraData(e){this.reportInfoService.setPublicExtraData(e)}setPublicParams(e){this.reportInfoService.setPublicParams(e)}isSendGet(){return!/^https?:\/\/([\w\-.]+\.(qq)\.com)\//.test(location.href)}sampling(e){const{samplingRate:n,guidSamplingRange:r}=e!=null?e:{};if(n===void 0)return!0;const i=this.getGuidFromCookie();if(!i)return Math.random()*100<n;const o=r!=null?r:4,d=Math.round(Lt(10,o)*(n/100));return Number(i.slice(o*-1))<d}getGuidFromCookie(){const{guid:e}=it(document.cookie);return h(e)}}function ut(){return Date.now()}function pt(){return performance.timing.navigationStart}class O{constructor(e){this.reportService=e,this.firstTime=pt(),this.init()}init(){}reportEvent(e,n,r={}){this.reportService.report({event:e,extraData:r,eventTime:Math.round(this.firstTime+n)})}reportEventBySimple(e,n,r={}){this.reportService.reportSimple({event:e,extraData:r,isSendBeacon:!1,eventTime:Math.round(this.firstTime+n)})}reportEventByEventTime(e,n,r={}){n!==0&&this.reportService.report({event:e,extraData:r,eventTime:n})}}const Xt=performance.now();class $t extends O{init(){globalThis.htmlFirstTime&&this.reportEvent(a.HtmlFirst,globalThis.htmlFirstTime),this.reportEvent(a.ScriptFirst,Xt),globalThis.addEventListener("beforeunload",()=>{this.reportService.report({event:a.PageExit})},!1)}}class lt extends O{init(){this.reportEventBeforeDomInteractive(),globalThis.addEventListener("DOMContentLoaded",()=>{this.reportEventBeforeDomContentLoadedEventEnd()},!1),globalThis.addEventListener("load",()=>{setTimeout(()=>{this.reportEventAfterDomContentLoadedEventStart()},0)},!1)}reportEventBeforeDomInteractive(){}reportEventBeforeDomContentLoadedEventEnd(){}reportEventAfterDomContentLoadedEventStart(){}}class Wt extends lt{reportEventBeforeDomInteractive(){const{unloadEventStart:e,unloadEventEnd:n,redirectStart:r,redirectEnd:i,fetchStart:o,domainLookupStart:d,domainLookupEnd:c,connectStart:p,connectEnd:u,secureConnectionStart:m,requestStart:T,responseStart:b,responseEnd:S}=performance.getEntriesByType("navigation")[0];this.reportEvent(a.NavigationStart,0),this.reportEvent(a.UnloadStart,e),this.reportEvent(a.UnloadEnd,n),this.reportEvent(a.RedirectStart,r),this.reportEvent(a.RedirectEnd,i),this.reportEvent(a.FetchStart,o),this.reportEvent(a.DnsLookupStart,d),this.reportEvent(a.DnsLookupEnd,c),this.reportEvent(a.TcpConnectStart,p),this.reportEvent(a.SslConnectStart,m),this.reportEvent(a.TcpConnectEnd,u),this.reportEvent(a.RequestStart,T),this.reportEvent(a.ResponseStart,b),this.reportEvent(a.ResponseEnd,S),this.reportService.report({event:a.DomLoading,extraData:{},eventTime:performance.timing.domLoading})}reportEventBeforeDomContentLoadedEventEnd(){const{domInteractive:e,domContentLoadedEventStart:n}=performance.getEntriesByType("navigation")[0];this.reportEvent(a.DomInteractive,e),this.reportEvent(a.DomContentLoadedStart,n)}reportEventAfterDomContentLoadedEventStart(){const{domContentLoadedEventEnd:e,domComplete:n,loadEventStart:r,loadEventEnd:i}=performance.getEntriesByType("navigation")[0];this.reportEvent(a.DomContentLoadedEnd,e),this.reportEvent(a.DomComplete,n),this.reportEvent(a.LoadStart,r),this.reportEvent(a.LoadEnd,i)}}class jt extends lt{reportEventBeforeDomInteractive(){const{navigationStart:e,unloadEventStart:n,unloadEventEnd:r,redirectStart:i,redirectEnd:o,fetchStart:d,domainLookupStart:c,domainLookupEnd:p,connectStart:u,connectEnd:m,secureConnectionStart:T,requestStart:b,responseStart:S,responseEnd:I,domLoading:z}=performance.timing;this.reportEventByEventTime(a.NavigationStart,e),this.reportEventByEventTime(a.UnloadStart,n),this.reportEventByEventTime(a.UnloadEnd,r),this.reportEventByEventTime(a.RedirectStart,i),this.reportEventByEventTime(a.RedirectEnd,o),this.reportEventByEventTime(a.FetchStart,d),this.reportEventByEventTime(a.DnsLookupStart,c),this.reportEventByEventTime(a.DnsLookupEnd,p),this.reportEventByEventTime(a.TcpConnectStart,u),this.reportEventByEventTime(a.SslConnectStart,T),this.reportEventByEventTime(a.TcpConnectEnd,m),this.reportEventByEventTime(a.RequestStart,b),this.reportEventByEventTime(a.ResponseStart,S),this.reportEventByEventTime(a.ResponseEnd,I),this.reportEventByEventTime(a.DomLoading,z)}reportEventBeforeDomContentLoadedEventEnd(){const{domInteractive:e,domContentLoadedEventStart:n}=performance.timing;this.reportEventByEventTime(a.DomInteractive,e),this.reportEventByEventTime(a.DomContentLoadedStart,n)}reportEventAfterDomContentLoadedEventStart(){const{domContentLoadedEventEnd:e,domComplete:n,loadEventStart:r,loadEventEnd:i}=performance.timing;this.reportEventByEventTime(a.DomContentLoadedEnd,e),this.reportEventByEventTime(a.DomComplete,n),this.reportEventByEventTime(a.LoadStart,r),this.reportEventByEventTime(a.LoadEnd,i)}}function Ht(){return!(!performance.getEntriesByType||performance.getEntriesByType("navigation").length===0)}function Gt(t){return Ht()?new Wt(t):new jt(t)}const Qt=["h.trace.qq.com","aegis.qq.com","otheve.beacon.qq.com"],zt=["xmlhttprequest","beacon"],W="resource";class Yt extends O{init(){performance.getEntriesByType(W).forEach(e=>{this.reportResourceTiming(e)}),this.addPerformanceObserver()}addPerformanceObserver(){if(!PerformanceObserver)return;new PerformanceObserver(n=>{n.getEntriesByType(W).forEach(r=>{this.reportResourceTiming(r)})}).observe({entryTypes:[W]})}reportResourceTiming(e){const{name:n,initiatorType:r,nextHopProtocol:i,duration:o,workerStart:d,transferSize:c,encodedBodySize:p,decodedBodySize:u}=e;zt.some(m=>m===r)||Qt.some(m=>n.includes(m))||this.reportEventBySimple(a.ResourceTiming,Number(o.toFixed(2)),{name:n,protocol:i,workerStart:d.toFixed(2),transferSize:String(c),encodedBodySize:String(p),decodedBodySize:String(u)})}}var mt=-1,j=function(t){addEventListener("pageshow",function(e){e.persisted&&(mt=e.timeStamp,t(e))},!0)},ft=function(){return window.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0]},H=function(){var t=ft();return t&&t.activationStart||0},k=function(t,e){var n=ft(),r="navigate";return mt>=0?r="back-forward-cache":n&&(r=document.prerendering||H()>0?"prerender":n.type.replace(/_/g,"-")),{name:t,value:e===void 0?-1:e,rating:"good",delta:0,entries:[],id:"v3-".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:r}},vt=function(t,e,n){try{if(PerformanceObserver.supportedEntryTypes.includes(t)){var r=new PerformanceObserver(function(i){e(i.getEntries())});return r.observe(Object.assign({type:t,buffered:!0},n||{})),r}}catch(i){}},ht=function(t,e){var n=function r(i){i.type!=="pagehide"&&document.visibilityState!=="hidden"||(t(i),e&&(removeEventListener("visibilitychange",r,!0),removeEventListener("pagehide",r,!0)))};addEventListener("visibilitychange",n,!0),addEventListener("pagehide",n,!0)},U=function(t,e,n,r){var i,o;return function(d){e.value>=0&&(d||r)&&((o=e.value-(i||0))||i===void 0)&&(i=e.value,e.delta=o,e.rating=function(c,p){return c>p[1]?"poor":c>p[0]?"needs-improvement":"good"}(e.value,n),t(e))}},w=-1,gt=function(){return document.visibilityState!=="hidden"||document.prerendering?1/0:0},Et=function(){ht(function(t){var e=t.timeStamp;w=e},!0)},St=function(){return w<0&&(w=gt(),Et(),j(function(){setTimeout(function(){w=gt(),Et()},0)})),{get firstHiddenTime(){return w}}},Jt=function(t,e){e=e||{};var n,r=[1800,3e3],i=St(),o=k("FCP"),d=function(u){u.forEach(function(m){m.name==="first-contentful-paint"&&(p&&p.disconnect(),m.startTime<i.firstHiddenTime&&(o.value=m.startTime-H(),o.entries.push(m),n(!0)))})},c=window.performance&&window.performance.getEntriesByName&&window.performance.getEntriesByName("first-contentful-paint")[0],p=c?null:vt("paint",d);(c||p)&&(n=U(t,o,r,e.reportAllChanges),c&&d([c]),j(function(u){o=k("FCP"),n=U(t,o,r,e.reportAllChanges),requestAnimationFrame(function(){requestAnimationFrame(function(){o.value=performance.now()-u.timeStamp,n(!0)})})}))},G={},Zt=function(t,e){e=e||{};var n,r=[2500,4e3],i=St(),o=k("LCP"),d=function(u){var m=u[u.length-1];if(m){var T=m.startTime-H();T<i.firstHiddenTime&&(o.value=T,o.entries=[m],n())}},c=vt("largest-contentful-paint",d);if(c){n=U(t,o,r,e.reportAllChanges);var p=function(){G[o.id]||(d(c.takeRecords()),c.disconnect(),G[o.id]=!0,n(!0))};["keydown","click"].forEach(function(u){addEventListener(u,p,{once:!0,capture:!0})}),ht(p,!0),j(function(u){o=k("LCP"),n=U(t,o,r,e.reportAllChanges),requestAnimationFrame(function(){requestAnimationFrame(function(){o.value=performance.now()-u.timeStamp,G[o.id]=!0,n(!0)})})})}};class Kt extends O{init(){Jt(e=>{this.reportEvent(a.Fcp,e.value)}),Zt(e=>{this.reportEvent(a.Lcp,e.value)})}}var P,te=new Uint8Array(16);function ee(){if(!P&&(P=typeof crypto!="undefined"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||typeof msCrypto!="undefined"&&typeof msCrypto.getRandomValues=="function"&&msCrypto.getRandomValues.bind(msCrypto),!P))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return P(te)}var ne=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;function re(t){return typeof t=="string"&&ne.test(t)}for(var g=[],Q=0;Q<256;++Q)g.push((Q+256).toString(16).substr(1));function ie(t){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,n=(g[t[e+0]]+g[t[e+1]]+g[t[e+2]]+g[t[e+3]]+"-"+g[t[e+4]]+g[t[e+5]]+"-"+g[t[e+6]]+g[t[e+7]]+"-"+g[t[e+8]]+g[t[e+9]]+"-"+g[t[e+10]]+g[t[e+11]]+g[t[e+12]]+g[t[e+13]]+g[t[e+14]]+g[t[e+15]]).toLowerCase();if(!re(n))throw TypeError("Stringified UUID is invalid");return n}function Tt(t,e,n){t=t||{};var r=t.random||(t.rng||ee)();if(r[6]=r[6]&15|64,r[8]=r[8]&63|128,e){n=n||0;for(var i=0;i<16;++i)e[n+i]=r[i];return e}return ie(r)}const oe="1101070898",ae="wx3909f6add1206543";function se(t,e){const{guid:n,yyb_uuid:r}=t;return E({guid:h(n),uuid:h(r)},de(t,e))}function de(t,e){return N(e)===q.Yyb?ce(t):ue(t)}function ce(t){const{uin:e,openid:n,mobileqopenid:r,logintype:i}=t;return i==="MOBILEQ"?{appId:oe,loginType:"qq",openId:h(r||n),uin:yt(e)}:i==="WX"?{appId:ae,loginType:"wx",openId:h(n)}:{appId:"",loginType:"none",openId:""}}function ue(t){const{uin:e,yyb_openid:n,yyb_logintype:r,yyb_appid:i}=t;return{appId:h(i),loginType:r||"none",openId:h(n),uin:yt(e)}}function yt(t){if(!t)return"";const e=/^o(\d+)$/.exec(t);if(!e)return"";const n=Number(e[1])+0;return n>1e4?String(n):""}class pe{getUserInfo(e,n){return se(e,n)}getEnv(e){const{type:n,version:r}=nt(e);return{appEnv:N(e),isX5:K(e),chromeVersion:tt(e),osType:n,osVersion:r}}getExtraData(e){return e?x(e,"&","="):""}}class le extends pe{constructor(){super(),this.publicParams={},this.publicExtraData={},this.reportTimingsByEventType=new Map,this.generateTraceID(),this.initPublicParams(),this.firstTime=pt()}initPublicParams(){var It,Dt;const e=navigator.userAgent,n=it(document.cookie),r=location.href,{appEnv:i,chromeVersion:o,isX5:d,osType:c,osVersion:p}=this.getEnv(e),{appId:u,loginType:m,openId:T,guid:b,uuid:S,uin:I}=this.getUserInfo(n,e),{coreVersion:z,sdkVersion:ye}=rt();this.publicParams={osType:c,osVersion:p,loginType:m,openId:T,url:r,ua:e,guid:b,uuid:S,x5CoreVersion:z,x5SdkVersion:ye,traceId:this.traceID,chromeVersion:String(o),isX5:d?"1":"0",project:(Dt=(It=location.pathname.split("/"))==null?void 0:It[1])!=null?Dt:"",env:i,frontTraceId:Tt(),appId:u},I&&(this.publicExtraData=E({uin:I},this.publicExtraData))}getReportInfo(e,n={},r){var u;const i=r!=null?r:ut(),{fullDuration:o,lastDuration:d}=this.getDurations(e,i),c=this.reportTimingsByEventType.get(L.Business)||[],p=c[c.length-1];return _(E({},this.publicParams),{eventName:e,eventTime:i,fullDuration:o,lastDuration:d,extraData:this.getExtraData(e===a.PageExit?_(E(E(E({},this.getSWData()),this.publicExtraData),n),{exitStatus:(u=p==null?void 0:p.event)!=null?u:""}):E(E(E({},this.getSWData()),this.publicExtraData),n))})}getSimpleReportInfo(e,n={},r){const i=r||ut();return _(E({},this.publicParams),{eventName:e,eventTime:i,fullDuration:i-this.firstTime,lastDuration:0,extraData:this.getExtraData(E(E({},this.publicExtraData),n))})}addReportTiming(e,n){var o;const r={event:e,timing:Number(n)},i=M(e);this.reportTimingsByEventType.has(i)||this.reportTimingsByEventType.set(i,[]),(o=this.reportTimingsByEventType.get(i))==null||o.push(r)}setPublicExtraData(e){this.publicExtraData=E(E({},this.publicExtraData),e)}setPublicParams(e){this.publicParams=E(E({},this.publicParams),e)}getTraceID(){return this.traceID}getDurations(e,n){return{fullDuration:Math.max(n-this.firstTime,0),lastDuration:Math.max(this.getLastDuration(e,n),0)}}getLastDuration(e,n){const r=M(e),i=this.reportTimingsByEventType.get(r);return[L.WebVitals,L.Point].includes(r)?0:!i||i.length===0?n-this.firstTime:n-i[i.length-1].timing}getSWData(){var e,n,r;return{swState:(r=(n=(e=navigator.serviceWorker)==null?void 0:e.controller)==null?void 0:n.state)!=null?r:""}}generateTraceID(){var n,r;const e=new URLSearchParams(location.href);this.traceID=(r=(n=e.get("trace_id"))!=null?n:globalThis.traceId)!=null?r:Tt(),globalThis.traceId=this.traceID}}var bt=(t=>(t.Ip="_client_ip_",t.EventName="eventName",t.EventTime="eventTime",t.FullDuration="fullDuration",t.LastDuration="lastDuration",t.TraceId="traceId",t.Guid="guid",t.Uuid="uuid",t.LoginType="loginType",t.OpenId="openId",t.Url="url",t.Env="env",t.Ua="ua",t.OsType="osType",t.OsVersion="osVersion",t.ChromeVersion="chromeVersion",t.IsX5="isX5",t.X5CoreVersion="x5CoreVersion",t.X5SdkVersion="x5SdkVersion",t.Project="project",t.ExtraData="extraData",t.Seq="seq",t.FrontTraceId="frontTraceId",t.AppId="appId",t))(bt||{});let l,B;function me(t){B=new le,l=new Mt(B,t),Gt(l),new Kt(l),new $t(l),t!=null&&t.isNeedReportResourceTiming&&new Yt(l)}function fe(t,e,n){if(y){globalThis.reportPerformance(t,e,n);return}l==null||l.report({event:t,extraData:e,eventTime:n})}function ve(t){const{event:e,extraData:n,eventTime:r}=t;if(y){globalThis.reportPerformance(e,n,r);return}l==null||l.report(t)}function he(t,e,n=!1,r){if(y){globalThis.reportPerformance(t,e,r);return}l==null||l.reportSimple({event:t,extraData:e,eventTime:r,isSendBeacon:n})}function ge(t){const{event:e,extraData:n,eventTime:r}=t;if(y){globalThis.reportPerformance(e,n,r);return}l==null||l.reportSimple(t)}function Ee(t){l==null||l.setPublicExtraData(t)}function Se(t){l==null||l.setPublicParams(t)}function Te(){return B==null?void 0:B.getTraceID()}s.AppEnv=q,s.EventType=L,s.IS_SERVER=y,s.OsType=et,s.PERF_API_EVENTS=at,s.POINT_EVENTS=dt,s.PerformanceEvent=a,s.PerformanceKey=bt,s.SERVER_EVENTS=ot,s.WEB_VITALS_EVENTS=st,s.getAppEnvFromUA=N,s.getChromeVersionFromUA=tt,s.getIsX5FromUA=K,s.getOsInfoFromUA=nt,s.getTraceId=Te,s.getTypeByEvent=M,s.getX5Info=rt,s.hasTestParam=Z,s.init=me,s.isProduction=J,s.report=fe,s.reportSimple=he,s.reportSimpleV2=ge,s.reportV2=ve,s.setPublicExtraData=Ee,s.setPublicParams=Se,Object.defineProperties(s,{__esModule:{value:!0},[Symbol.toStringTag]:{value:"Module"}})});
