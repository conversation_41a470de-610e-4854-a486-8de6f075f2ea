﻿/**
 * UniversalReport
 * v3.6.10
 * <AUTHOR> 2023-04-26 19:49:40
 */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t=t||self).UniversalReport=e()}(this,(function(){"use strict";var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])},t(e,n)};function e(e,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function i(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}var n=function(){return n=Object.assign||function(t){for(var e,n=1,i=arguments.length;n<i;n++)for(var r in e=arguments[n])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t},n.apply(this,arguments)};function i(t,e){var n={};for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&e.indexOf(i)<0&&(n[i]=t[i]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(i=Object.getOwnPropertySymbols(t);r<i.length;r++)e.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(t,i[r])&&(n[i[r]]=t[i[r]])}return n}function r(t,e,n,i){var r,o=arguments.length,a=o<3?e:null===i?i=Object.getOwnPropertyDescriptor(e,n):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,i);else for(var s=t.length-1;s>=0;s--)(r=t[s])&&(a=(o<3?r(a):o>3?r(e,n,a):r(e,n))||a);return o>3&&a&&Object.defineProperty(e,n,a),a}function o(t,e,n,i){return new(n||(n=Promise))((function(r,o){function a(t){try{l(i.next(t))}catch(t){o(t)}}function s(t){try{l(i.throw(t))}catch(t){o(t)}}function l(t){var e;t.done?r(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(a,s)}l((i=i.apply(t,e||[])).next())}))}function a(t,e){var n,i,r,o,a={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return o={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(o){return function(s){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,i&&(r=2&o[0]?i.return:o[0]?i.throw||((r=i.return)&&r.call(i),0):i.next)&&!(r=r.call(i,o[1])).done)return r;switch(i=0,r&&(o=[2&o[0],r.value]),o[0]){case 0:case 1:r=o;break;case 4:return a.label++,{value:o[1],done:!1};case 5:a.label++,i=o[1],o=[0];continue;case 7:o=a.ops.pop(),a.trys.pop();continue;default:if(!(r=a.trys,(r=r.length>0&&r[r.length-1])||6!==o[0]&&2!==o[0])){a=0;continue}if(3===o[0]&&(!r||o[1]>r[0]&&o[1]<r[3])){a.label=o[1];break}if(6===o[0]&&a.label<r[1]){a.label=r[1],r=o;break}if(r&&a.label<r[2]){a.label=r[2],a.ops.push(o);break}r[2]&&a.ops.pop(),a.trys.pop();continue}o=e.call(t,a)}catch(t){o=[6,t],i=0}finally{n=r=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,s])}}}function s(){for(var t=0,e=0,n=arguments.length;e<n;e++)t+=arguments[e].length;var i=Array(t),r=0;for(e=0;e<n;e++)for(var o=arguments[e],a=0,s=o.length;a<s;a++,r++)i[r]=o[a];return i}function l(t){var e=this.constructor;return this.then((function(n){return e.resolve(t()).then((function(){return n}))}),(function(n){return e.resolve(t()).then((function(){return e.reject(n)}))}))}Array.prototype.includes||Object.defineProperty(Array.prototype,"includes",{value:function(t,e){if(null==this)throw new TypeError('"this" is null or not defined');var n=Object(this),i=n.length>>>0;if(0===i)return!1;var r,o,a=0|e,s=Math.max(a>=0?a:i-Math.abs(a),0);for(;s<i;){if((r=n[s])===(o=t)||"number"==typeof r&&"number"==typeof o&&isNaN(r)&&isNaN(o))return!0;s++}return!1}}),Object.entries||(Object.entries=function(t){for(var e=Object.keys(t),n=e.length,i=new Array(n);n--;)i[n]=[e[n],t[e[n]]];return i}),"function"!=typeof Object.assign&&Object.defineProperty(Object,"assign",{value:function(t){if(null==t)throw new TypeError("Cannot convert undefined or null to object");for(var e=Object(t),n=1;n<arguments.length;n++){var i=arguments[n];if(null!=i)for(var r in i)Object.prototype.hasOwnProperty.call(i,r)&&(e[r]=i[r])}return e},writable:!0,configurable:!0});var u=setTimeout;function c(t){return Boolean(t&&void 0!==t.length)}function p(){}function d(t){if(!(this instanceof d))throw new TypeError("$Promises must be constructed via new");if("function"!=typeof t)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=void 0,this._deferreds=[],y(t,this)}function h(t,e){for(;3===t._state;)t=t._value;0!==t._state?(t._handled=!0,d._immediateFn((function(){var n=1===t._state?e.onFulfilled:e.onRejected;if(null!==n){var i;try{i=n(t._value)}catch(t){return void v(e.$Promise,t)}f(e.$Promise,i)}else(1===t._state?f:v)(e.$Promise,t._value)}))):t._deferreds.push(e)}function f(t,e){try{if(e===t)throw new TypeError("A $Promise cannot be resolved with itself.");if(e&&("object"==typeof e||"function"==typeof e)){var n=e.then;if(e instanceof d)return t._state=3,t._value=e,void g(t);if("function"==typeof n)return void y((i=n,r=e,function(){i.apply(r,arguments)}),t)}t._state=1,t._value=e,g(t)}catch(e){v(t,e)}var i,r}function v(t,e){t._state=2,t._value=e,g(t)}function g(t){2===t._state&&0===t._deferreds.length&&d._immediateFn((function(){t._handled||d._unhandledRejectionFn(t._value)}));for(var e=0,n=t._deferreds.length;e<n;e++)h(t,t._deferreds[e]);t._deferreds=null}function m(t,e,n){this.onFulfilled="function"==typeof t?t:null,this.onRejected="function"==typeof e?e:null,this.$Promise=n}function y(t,e){var n=!1;try{t((function(t){n||(n=!0,f(e,t))}),(function(t){n||(n=!0,v(e,t))}))}catch(t){if(n)return;n=!0,v(e,t)}}d.prototype.catch=function(t){return this.then(null,t)},d.prototype.then=function(t,e){var n=new this.constructor(p);return h(this,new m(t,e,n)),n},d.prototype.finally=l,d.all=function(t){return new d((function(e,n){if(!c(t))return n(new TypeError("$Promise.all accepts an array"));var i=Array.prototype.slice.call(t);if(0===i.length)return e([]);var r=i.length;function o(t,a){try{if(a&&("object"==typeof a||"function"==typeof a)){var s=a.then;if("function"==typeof s)return void s.call(a,(function(e){o(t,e)}),n)}i[t]=a,0==--r&&e(i)}catch(t){n(t)}}for(var a=0;a<i.length;a++)o(a,i[a])}))},d.resolve=function(t){return t&&"object"==typeof t&&t.constructor===d?t:new d((function(e){e(t)}))},d.reject=function(t){return new d((function(e,n){n(t)}))},d.race=function(t){return new d((function(e,n){if(!c(t))return n(new TypeError("$Promise.race accepts an array"));for(var i=0,r=t.length;i<r;i++)d.resolve(t[i]).then(e,n)}))},d._immediateFn="function"==typeof setImmediate&&function(t){setImmediate(t)}||function(t){u(t,0)},d._unhandledRejectionFn=function(t){"undefined"!=typeof console&&console};var b=function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if("undefined"!=typeof global)return global;throw new Error("unable to locate global object")}();"function"!=typeof b.Promise?b.Promise=d:b.Promise.prototype.finally||(b.Promise.prototype.finally=l);var w=Object.prototype.hasOwnProperty;function P(t){try{return decodeURIComponent(t.replace(/\+/g," "))}catch(t){return null}}function _(t){try{return encodeURIComponent(t)}catch(t){return null}}var E,I,R={stringify:function(t,e){e=e||"";var n,i,r=[];for(i in"string"!=typeof e&&(e="?"),t)if(w.call(t,i)){if((n=t[i])||null!=n&&!isNaN(n)||(n=""),i=_(i),n=_(n),null===i||null===n)continue;r.push(i+"="+n)}return r.length?e+r.join("&"):""},parse:function(t){for(var e,n=/([^=?#&]+)=?([^&]*)/g,i={};e=n.exec(t);){var r=P(e[1]),o=P(e[2]);null===r||null===o||r in i||(i[r]=o)}return i}};(I=E||(E={}))[I.equal=0]="equal",I[I.low=-1]="low",I[I.high=1]="high";var S,O,k=/^v?(?:\d+)(\.(?:[x*]|\d+)(\.(?:[x*]|\d+)(\.(?:[x*]|\d+))?(?:-[\da-z-]+(?:\.[\da-z-]+)*)?(?:\+[\da-z-]+(?:\.[\da-z-]+)*)?)?)?$/i;function T(t){var e,n=t.replace(/^v/,"").replace(/\+.*$/,""),i=("-",-1===(e=n).indexOf("-")?e.length:e.indexOf("-")),r=n.substring(0,i).split(".");return r.push(n.substring(i+1)),r}function A(t){return isNaN(Number(t))?t:Number(t)}function C(t){if("string"!=typeof t)throw new TypeError("Invalid argument expected string");if(!k.test(t))throw new Error("Invalid argument not valid semver ('"+t+"' received)")}!function(t){t.PGIN="dt_pgin",t.PGOUT="dt_pgout",t.IMP="dt_imp",t.IMPEND="dt_imp_end",t.CLCK="dt_clck"}(S||(S={})),function(t){t.APP="app",t.BEACON="beacon"}(O||(O={}));var N,x,D,M,j,B,L,V={dt_qq_h5:"",dt_qqopenid_h5:"",dt_wxopenid_h5:"",dt_wbopenid_h5:"",dt_mainlogin_h5:"",dt_qq:"",dt_qqopenid:"",dt_wxopenid:"",dt_wbopenid:"",dt_mainlogin:""};!function(t){t.ENABLE_FEATURE="1"}(N||(N={})),function(t){t.REPORT_VIEW="reportView",t.REALTIME_DEBUG="realTimeDebug",t.REPORT_VIEW_SET_TOKEN="setToken",t.REPORT_VIEW_PLUGIN_TOKEN="pluginToken",t.REALTIME_DEBUG_ID="dt_debugid",t.REALTIME_APP_ID="dt_appid",t.VISUAL_TRACK_PLUGIN="dtVisualTrack"}(x||(x={})),function(t){t.OVERFLOWHIDE_ATTR="data-ofhide",t.ISSCROLLER_ATTR="data-scroller",t.LAZY_ATTR="data-lazy"}(D||(D={})),function(t){t.REPORT_NIL="report_event_nil",t.REPORT_EXIST="native_bridge_exist",t.REPORT_FALLBACK="old_native_fallback",t.ANDROID_NOT_SUPPORT="android_not_support"}(M||(M={})),function(t){t.REPORT_EVENT="vr_reportEvent",t.CHECK_WEBVIEW="vr_getWebviewVisibility"}(j||(j={})),function(t){t.EXPOSE="expose",t.HIDE="hide",t.VISIBILITYCHANGED="visibilityChanged"}(B||(B={})),function(t){t.SUCCESS="200",t.ERROR="500",t.OVER_TIME="504"}(L||(L={}));var U=function(){function t(){this.eventsMap={}}return t.prototype.on=function(t,e){var n=this;t.split(",").forEach((function(t){n.eventsMap[t]||(n.eventsMap[t]=[]),n.eventsMap[t].push(e)}))},t.prototype.off=function(t,e){var n=this;t.split(",").forEach((function(t){n.removeListener(t,e)}))},t.prototype.emit=function(t){for(var e=this,n=[],i=1;i<arguments.length;i++)n[i-1]=arguments[i];var r=[];if(this.eventsMap[t]){var o=this.eventsMap[t].slice();o.forEach((function(i){try{r.push(i.apply(e,n))}catch(n){r.push(null),e.processError(n,t)}}))}return r},t.prototype.use=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var n=t[0],i=t.slice(1);return n.apply(void 0,s([this],i))},t.prototype.processError=function(t,e){},t.prototype.removeListener=function(t,e){if(this.eventsMap[t]){var n=this.eventsMap[t].findIndex((function(t){return t===e}));n>-1&&this.eventsMap[t].splice(n,1)}},t}(),q={qqvideo:{android:"8.3.40",ios:"8.3.60"}},H=0,W=1,F=2,J=3,G=["imei","imsi","idfa"],Q="qq.com",K=["style","class"],z="ios",X="android",Y="macos",$="windows",Z="qq",tt="qqvideo",et={QQ:/qq\/([\d.]+)/i,WX:/MicroMessenger\/([\w.]+)/i,QQ_VIDEO:/QQLive(?:HD)?Browser\/([\d.]+)/,QQ_NEWS:/qqnews\/(\d+\.\d+\.\d+)/i,QQ_READING:/qnreading\/(\d+\.\d+\.\d+)/i,QQ_BROWSER:/MQQBrowser\/(\d+\.\d+)/i,Q_ZONE:/Qzone\/[\w\d_]*(\d\.\d)[.\w\d_]*/i,KUAIBAO:/qnreading\/(\d+\.\d+\.\d+)/i,WEIBO:/Weibo/i},nt="undefined"!=typeof window&&window.IntersectionObserver&&window.IntersectionObserverEntry&&("isIntersecting"in window.IntersectionObserverEntry.prototype||"intersectionRatio"in window.IntersectionObserverEntry.prototype),it=/https?:\/\/datong(-test)?.(w)(o)(a).com/,rt=/localhost/;"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self&&self;var ot=function(t,e){return t(e={exports:{}},e.exports),e.exports}((function(t,e){t.exports=function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])},t(e,n)},e=function(){return e=Object.assign||function(t){for(var e,n=1,i=arguments.length;n<i;n++)for(var r in e=arguments[n])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t},e.apply(this,arguments)};function n(t,e,n,i){return new(n||(n=Promise))((function(r,o){function a(t){try{l(i.next(t))}catch(t){o(t)}}function s(t){try{l(i.throw(t))}catch(t){o(t)}}function l(t){var e;t.done?r(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(a,s)}l((i=i.apply(t,e||[])).next())}))}function i(t,e){var n,i,r,o,a={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return o={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(o){return function(s){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,i&&(r=2&o[0]?i.return:o[0]?i.throw||((r=i.return)&&r.call(i),0):i.next)&&!(r=r.call(i,o[1])).done)return r;switch(i=0,r&&(o=[2&o[0],r.value]),o[0]){case 0:case 1:r=o;break;case 4:return a.label++,{value:o[1],done:!1};case 5:a.label++,i=o[1],o=[0];continue;case 7:o=a.ops.pop(),a.trys.pop();continue;default:if(!((r=(r=a.trys).length>0&&r[r.length-1])||6!==o[0]&&2!==o[0])){a=0;continue}if(3===o[0]&&(!r||o[1]>r[0]&&o[1]<r[3])){a.label=o[1];break}if(6===o[0]&&a.label<r[1]){a.label=r[1],r=o;break}if(r&&a.label<r[2]){a.label=r[2],a.ops.push(o);break}r[2]&&a.ops.pop(),a.trys.pop();continue}o=e.call(t,a)}catch(t){o=[6,t],i=0}finally{n=r=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,s])}}}var r="__BEACON_",o="__BEACON_deviceId",a="last_report_time",s="sending_event_ids",l="beacon_config",u="beacon_config_request_time",c=function(){function t(){var t=this;this.emit=function(e,n){if(t){var i,r=t.__EventsList[e];if(null==r?void 0:r.length){r=r.slice();for(var o=0;o<r.length;o++){i=r[o];try{var a=i.callback.apply(t,[n]);if(1===i.type&&t.remove(e,i.callback),!1===a)break}catch(t){throw t}}}return t}},this.__EventsList={}}return t.prototype.indexOf=function(t,e){for(var n=0;n<t.length;n++)if(t[n].callback===e)return n;return-1},t.prototype.on=function(t,e,n){if(void 0===n&&(n=0),this){var i=this.__EventsList[t];if(i||(i=this.__EventsList[t]=[]),-1===this.indexOf(i,e)){var r={name:t,type:n||0,callback:e};return i.push(r),this}return this}},t.prototype.one=function(t,e){this.on(t,e,1)},t.prototype.remove=function(t,e){if(this){var n=this.__EventsList[t];if(!n)return null;if(!e){try{delete this.__EventsList[t]}catch(t){}return null}if(n.length){var i=this.indexOf(n,e);n.splice(i,1)}return this}},t}();function p(t,e){for(var n={},i=0,r=Object.keys(t);i<r.length;i++){var o=r[i],a=t[o];if("string"==typeof a)n[d(o)]=d(a);else{if(e)throw new Error("value mast be string  !!!!");n[d(String(o))]=d(String(a))}}return n}function d(t){if("string"!=typeof t)return t;try{return t.replace(new RegExp("\\|","g"),"%7C").replace(new RegExp("\\&","g"),"%26").replace(new RegExp("\\=","g"),"%3D").replace(new RegExp("\\+","g"),"%2B")}catch(t){return""}}function h(t){return String(t.A99)+String(t.A100)}var f=function(){},v=function(){function t(t){var n=this;this.lifeCycle=new c,this.uploadJobQueue=[],this.additionalParams={},this.delayTime=0,this._normalLogPipeline=function(t){if(!t||!t.reduce||!t.length)throw new TypeError("createPipeline 方法需要传入至少有一个 pipe 的数组");return 1===t.length?function(e,n){t[0](e,n||f)}:t.reduce((function(t,e){return function(n,i){return void 0===i&&(i=f),t(n,(function(t){return null==e?void 0:e(t,i)}))}}))}([function(t){n.send({url:n.strategy.getUploadUrl(),data:t,method:"post",contentType:"application/json;charset=UTF-8"},(function(){var e=n.config.onReportSuccess;"function"==typeof e&&e(JSON.stringify(t.events))}),(function(){var e=n.config.onReportFail;"function"==typeof e&&e(JSON.stringify(t.events))}))}]),function(t,e){if(!t)throw e instanceof Error?e:new Error(e)}(Boolean(t.appkey),"appkey must be initial"),this.config=e({},t)}return t.prototype.onUserAction=function(t,e){this.preReport(t,e,!1)},t.prototype.onDirectUserAction=function(t,e){this.preReport(t,e,!0)},t.prototype.preReport=function(t,e,n){t?this.strategy.isEventUpOnOff()&&(this.strategy.isBlackEvent(t)||this.strategy.isSampleEvent(t)||this.onReport(t,e,n)):this.errorReport.reportError("602"," no eventCode")},t.prototype.addAdditionalParams=function(t){for(var e=0,n=Object.keys(t);e<n.length;e++){var i=n[e];this.additionalParams[i]=t[i]}},t.prototype.setChannelId=function(t){this.commonInfo.channelID=String(t)},t.prototype.setOpenId=function(t){this.commonInfo.openid=String(t)},t.prototype.setUnionid=function(t){this.commonInfo.unid=String(t)},t.prototype.getDeviceId=function(){return this.commonInfo.deviceId},t.prototype.getCommonInfo=function(){return this.commonInfo},t.prototype.removeSendingId=function(t){try{var e=JSON.parse(this.storage.getItem(s)),n=e.indexOf(t);-1!=n&&(e.splice(n,1),this.storage.setItem(s,JSON.stringify(e)))}catch(t){}},t}(),g=function(){function t(t,e,n,i){this.requestParams={},this.network=i,this.requestParams.attaid="00400014144",this.requestParams.token="6478159937",this.requestParams.product_id=t.appkey,this.requestParams.platform=n,this.requestParams.uin=e.deviceId,this.requestParams.model="",this.requestParams.os=n,this.requestParams.app_version=t.appVersion,this.requestParams.sdk_version=e.sdkVersion,this.requestParams.error_stack="",this.uploadUrl=t.isOversea?"https://htrace.wetvinfo.com/kv":"https://h.trace.qq.com/kv"}return t.prototype.reportError=function(t,e){this.requestParams._dc=Math.random(),this.requestParams.error_msg=e,this.requestParams.error_code=t,this.network.get(this.uploadUrl,{params:this.requestParams}).catch((function(t){}))},t}(),m=function(){function t(t,e,n,i,r){this.strategy={isEventUpOnOff:!0,httpsUploadUrl:"https://otheve.beacon.qq.com/analytics/v2_upload",requestInterval:30,blacklist:[],samplelist:[]},this.realSample={},this.appkey="",this.needQueryConfig=!0,this.appkey=e.appkey,this.storage=i,this.needQueryConfig=t;try{var o=JSON.parse(this.storage.getItem(l));o&&this.processData(o)}catch(t){}e.isOversea&&(this.strategy.httpsUploadUrl="https://svibeacon.onezapp.com/analytics/v2_upload"),!e.isOversea&&this.needRequestConfig()&&this.requestConfig(e.appVersion,n,r)}return t.prototype.requestConfig=function(t,e,n){var i=this;this.storage.setItem(u,Date.now().toString()),n.post("https://oth.str.beacon.qq.com/trpc.beacon.configserver.BeaconConfigService/QueryConfig",{platformId:"undefined"==typeof wx?"3":"4",mainAppKey:this.appkey,appVersion:t,sdkVersion:e.sdkVersion,osVersion:e.userAgent,model:"",packageName:"",params:{A3:e.deviceId}}).then((function(t){if(0==t.data.ret)try{var e=JSON.parse(t.data.beaconConfig);e&&(i.processData(e),i.storage.setItem(l,t.data.beaconConfig))}catch(t){}else i.processData(null),i.storage.setItem(l,"")})).catch((function(t){}))},t.prototype.processData=function(t){var e,n,i,r,o;this.strategy.isEventUpOnOff=null!==(e=null==t?void 0:t.isEventUpOnOff)&&void 0!==e?e:this.strategy.isEventUpOnOff,this.strategy.httpsUploadUrl=null!==(n=null==t?void 0:t.httpsUploadUrl)&&void 0!==n?n:this.strategy.httpsUploadUrl,this.strategy.requestInterval=null!==(i=null==t?void 0:t.requestInterval)&&void 0!==i?i:this.strategy.requestInterval,this.strategy.blacklist=null!==(r=null==t?void 0:t.blacklist)&&void 0!==r?r:this.strategy.blacklist,this.strategy.samplelist=null!==(o=null==t?void 0:t.samplelist)&&void 0!==o?o:this.strategy.samplelist;for(var a=0,s=this.strategy.samplelist;a<s.length;a++){var l=s[a].split(",");2==l.length&&(this.realSample[l[0]]=l[1])}},t.prototype.needRequestConfig=function(){if(!this.needQueryConfig)return!1;var t=Number(this.storage.getItem(u));return Date.now()-t>60*this.strategy.requestInterval*1e3},t.prototype.getUploadUrl=function(){return this.strategy.httpsUploadUrl+"?appkey="+this.appkey},t.prototype.isBlackEvent=function(t){return-1!=this.strategy.blacklist.indexOf(t)},t.prototype.isEventUpOnOff=function(){return this.strategy.isEventUpOnOff},t.prototype.isSampleEvent=function(t){return!!Object.prototype.hasOwnProperty.call(this.realSample,t)&&this.realSample[t]<Math.floor(Math.random()*Math.floor(1e4))},t}(),y="session_storage_key",b=function(){function t(t,e,n){this.getSessionStackDepth=0,this.beacon=n,this.storage=t,this.duration=e,this.appkey=n.config.appkey}return t.prototype.getSession=function(){this.getSessionStackDepth+=1;var t=this.storage.getItem(y);if(!t)return this.createSession();var e="",n=0;try{var i=JSON.parse(t)||{sessionId:void 0,sessionStart:void 0};if(!i.sessionId||!i.sessionStart)return this.createSession();var r=Number(this.storage.getItem(a));if(Date.now()-r>this.duration)return this.createSession();e=i.sessionId,n=i.sessionStart,this.getSessionStackDepth=0}catch(t){}return{sessionId:e,sessionStart:n}},t.prototype.createSession=function(){var t=Date.now(),e={sessionId:this.appkey+"_"+t.toString(),sessionStart:t};this.storage.setItem(y,JSON.stringify(e)),this.storage.setItem(a,t.toString());var n="is_new_user",i=this.storage.getItem(n);return this.getSessionStackDepth<=1&&this.beacon.onDirectUserAction("rqd_applaunched",{A21:i?"N":"Y"}),this.storage.setItem(n,JSON.stringify(!1)),e},t}();function w(){var t=navigator.userAgent,e=t.indexOf("compatible")>-1&&t.indexOf("MSIE")>-1,n=t.indexOf("Edge")>-1&&!e,i=t.indexOf("Trident")>-1&&t.indexOf("rv:11.0")>-1;if(e){new RegExp("MSIE (\\d+\\.\\d+);").test(t);var r=parseFloat(RegExp.$1);return 7==r?7:8==r?8:9==r?9:10==r?10:6}return n?-2:i?11:-1}function P(t,e){var n,i;return(n="static/js/fp.js",void 0===i&&(i=Date.now()+"-"+Math.random()),new Promise((function(t,e){if(document.getElementById(i))t(void 0);else{var r=document.getElementsByTagName("head")[0],o=document.createElement("script");o.onload=function(){return function(){o.onload=null,t(void 0)}},o.onerror=function(t){o.onerror=null,r.removeChild(o),e(t)},o.src=n,o.id=i,r.appendChild(o)}}))).then((function(){(new Fingerprint).getQimei36(t,e)})).catch((function(t){})),""}var _,E=function(){return(E=Object.assign||function(t){for(var e,n=1,i=arguments.length;n<i;n++)for(var r in e=arguments[n])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}).apply(this,arguments)},I=function(){function t(t,e){void 0===e&&(e={}),this.reportOptions={},this.config=t,this.reportOptions=e}return t.canUseDB=function(){return!!(null===window||void 0===window?void 0:window.indexedDB)},t.prototype.openDB=function(){var e=this;return new Promise((function(n,i){if(!t.canUseDB())return i({message:"当前不支持 indexeddb"});var r=e.config,o=r.name,a=r.version,s=r.stores,l=indexedDB.open(o,a);l.onsuccess=function(){e.db=l.result,n(),E({result:1,func:"open",params:JSON.stringify(e.config)},e.reportOptions)},l.onerror=function(t){var n,r;i(t),E({result:0,func:"open",params:JSON.stringify(e.config),error_msg:null===(r=null===(n=t.target)||void 0===n?void 0:n.error)||void 0===r?void 0:r.message},e.reportOptions)},l.onupgradeneeded=function(){e.db=l.result;try{null==s||s.forEach((function(t){e.createStore(t)}))}catch(t){E({result:0,func:"open",params:JSON.stringify(e.config),error_msg:t.message},e.reportOptions),i(t)}}}))},t.prototype.useStore=function(t){return this.storeName=t,this},t.prototype.deleteDB=function(){var t=this;return this.closeDB(),new Promise((function(e,n){var i=indexedDB.deleteDatabase(t.config.name);i.onsuccess=function(){return e()},i.onerror=n}))},t.prototype.closeDB=function(){var t;null===(t=this.db)||void 0===t||t.close(),this.db=null},t.prototype.getStoreCount=function(){var t=this;return new Promise((function(e,n){var i=t.getStore("readonly").count();i.onsuccess=function(){return e(i.result)},i.onerror=n}))},t.prototype.clearStore=function(){var t=this;return new Promise((function(e,n){var i=t.getStore("readwrite").clear();i.onsuccess=function(){return e()},i.onerror=n}))},t.prototype.add=function(t,e){var n=this;return new Promise((function(i,r){var o=n.getStore("readwrite").add(t,e);o.onsuccess=function(){i(o.result)},o.onerror=r}))},t.prototype.put=function(t,e){var n=this;return new Promise((function(i,r){var o=n.getStore("readwrite").put(t,e);o.onsuccess=function(){i(o.result)},o.onerror=r}))},t.prototype.getStoreAllData=function(){var t=this;return new Promise((function(e,n){var i=t.getStore("readonly").openCursor(),r=[];i.onsuccess=function(){var t;if(null===(t=i.result)||void 0===t?void 0:t.value){var n=i.result.value;r.push(n),i.result.continue()}else e(r)},i.onerror=n}))},t.prototype.getDataRangeByIndex=function(t,e,n,i,r){var o=this;return new Promise((function(a,s){var l=o.getStore().index(t),u=IDBKeyRange.bound(e,n,i,r),c=[],p=l.openCursor(u);p.onsuccess=function(){var t;(null===(t=null==p?void 0:p.result)||void 0===t?void 0:t.value)?(c.push(null==p?void 0:p.result.value),null==p||p.result.continue()):a(c)},p.onerror=s}))},t.prototype.removeDataByIndex=function(t,e,n,i,r){var o=this;return new Promise((function(a,s){var l=o.getStore("readwrite").index(t),u=IDBKeyRange.bound(e,n,i,r),c=l.openCursor(u),p=0;c.onsuccess=function(t){var e=t.target.result;e?(p+=1,e.delete(),e.continue()):a(p)},c.onerror=s}))},t.prototype.createStore=function(t){var e=t.name,n=t.indexes,i=void 0===n?[]:n,r=t.options;if(this.db){this.db.objectStoreNames.contains(e)&&this.db.deleteObjectStore(e);var o=this.db.createObjectStore(e,r);i.forEach((function(t){o.createIndex(t.indexName,t.keyPath,t.options)}))}},t.prototype.getStore=function(t){var e;return void 0===t&&(t="readonly"),null===(e=this.db)||void 0===e?void 0:e.transaction(this.storeName,t).objectStore(this.storeName)},t}(),R="event_table_v3",S="eventId",O=function(){function t(t){this.isReady=!1,this.taskQueue=Promise.resolve(),this.db=new I({name:"Beacon_"+t+"_V3",version:1,stores:[{name:R,options:{keyPath:S},indexes:[{indexName:S,keyPath:S,options:{unique:!0}}]}]}),this.open()}return t.prototype.getCount=function(){var t=this;return this.readyExec((function(){return t.db.getStoreCount()}))},t.prototype.setItem=function(t,e){var n=this;return this.readyExec((function(){return n.db.add({eventId:t,value:e})}))},t.prototype.getItem=function(t){return n(this,void 0,void 0,(function(){var e=this;return i(this,(function(n){return[2,this.readyExec((function(){return e.db.getDataRangeByIndex(S,t,t)}))]}))}))},t.prototype.removeItem=function(t){var e=this;return this.readyExec((function(){return e.db.removeDataByIndex(S,t,t)}))},t.prototype.updateItem=function(t,e){var n=this;return this.readyExec((function(){return n.db.put({eventId:t,value:e})}))},t.prototype.iterate=function(t){var e=this;return this.readyExec((function(){return e.db.getStoreAllData().then((function(e){e.forEach((function(e){t(e.value)}))}))}))},t.prototype.open=function(){return n(this,void 0,void 0,(function(){var t=this;return i(this,(function(e){switch(e.label){case 0:return this.taskQueue=this.taskQueue.then((function(){return t.db.openDB()})),[4,this.taskQueue];case 1:return e.sent(),this.isReady=!0,this.db.useStore(R),[2]}}))}))},t.prototype.readyExec=function(t){return this.isReady?t():(this.taskQueue=this.taskQueue.then((function(){return t()})),this.taskQueue)},t}(),k=function(){function t(t){this.keyObject={},this.storage=t}return t.prototype.getCount=function(){return this.storage.getStoreCount()},t.prototype.removeItem=function(t){this.storage.removeItem(t),delete this.keyObject[t]},t.prototype.setItem=function(t,e){var n=JSON.stringify(e);this.storage.setItem(t,n),this.keyObject[t]=e},t.prototype.iterate=function(t){for(var e=Object.keys(this.keyObject),n=0;n<e.length;n++){var i=this.storage.getItem(e[n]);t(JSON.parse(i))}},t}(),T=function(){function t(t,e){var n=this;this.dbEventCount=0,w()>0||!window.indexedDB||/X5Lite/.test(navigator.userAgent)?(this.store=new k(e),this.dbEventCount=this.store.getCount()):(this.store=new O(t),this.getCount().then((function(t){n.dbEventCount=t})).catch((function(t){})))}return t.prototype.getCount=function(){return n(this,void 0,void 0,(function(){return i(this,(function(t){switch(t.label){case 0:return t.trys.push([0,2,,3]),[4,this.store.getCount()];case 1:return[2,t.sent()];case 2:return t.sent(),[2,Promise.reject()];case 3:return[2]}}))}))},t.prototype.insertEvent=function(t,e){return n(this,void 0,void 0,(function(){var n,r;return i(this,(function(i){switch(i.label){case 0:if(this.dbEventCount>=1e4)return[2,Promise.reject()];n=h(t.mapValue),i.label=1;case 1:return i.trys.push([1,3,,4]),this.dbEventCount++,[4,this.store.setItem(n,t)];case 2:return[2,i.sent()];case 3:return r=i.sent(),e&&e(r,t),this.dbEventCount--,[2,Promise.reject()];case 4:return[2]}}))}))},t.prototype.getEvents=function(){return n(this,void 0,void 0,(function(){var t;return i(this,(function(e){switch(e.label){case 0:t=[],e.label=1;case 1:return e.trys.push([1,3,,4]),[4,this.store.iterate((function(e){t.push(e)}))];case 2:case 3:return e.sent(),[2,Promise.all(t)];case 4:return[2]}}))}))},t.prototype.removeEvent=function(t){return n(this,void 0,void 0,(function(){var e;return i(this,(function(n){switch(n.label){case 0:e=h(t.mapValue),n.label=1;case 1:return n.trys.push([1,3,,4]),this.dbEventCount--,[4,this.store.removeItem(e)];case 2:return[2,n.sent()];case 3:return n.sent(),this.dbEventCount++,[2,Promise.reject()];case 4:return[2]}}))}))},t}(),A=function(){return(A=Object.assign||function(t){for(var e,n=1,i=arguments.length;n<i;n++)for(var r in e=arguments[n])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}).apply(this,arguments)};function C(t){try{return decodeURIComponent(t.replace(/\+/g," "))}catch(t){return null}}function N(t,e){var n=[null,void 0,"",NaN].includes(t);if(e.isSkipEmpty&&n)return null;var i=!e.isSkipEmpty&&n?"":t;try{return e.encode?encodeURIComponent(i):i}catch(t){return null}}function x(t,e){void 0===e&&(e={encode:!0,isSkipEmpty:!1});var n=t.url,i=t.query,r=void 0===i?{}:i,o=t.hash,a=n.split("#"),s=a[0],l=a[1],u=void 0===l?"":l,c=s.split("?")[0],p=[],d=N(o||u,e),h=A(A({},function(t){var e=t.split("#"),n=e[0],i=e[1],r=void 0===i?"":i,o=n.split("?"),a=o[0],s=o[1],l=void 0===s?"":s,u=C(r),c=Object.create(null);return l.split("&").forEach((function(t){var e=t.split("="),n=e[0],i=e[1],r=void 0===i?"":i,o=C(n),a=C(r);null===o||null===a||""===o&&""===a||c[o]||(c[o]=a)})),{url:a,query:c,hash:u}}(n).query),r);return Object.keys(h).forEach((function(t){var n=N(t,e),i=N(h[t],e);null!==n&&null!==i&&p.push(n+"="+i)})),c+(p.length?"?"+p.join("&"):"")+(d?"#"+d:"")}function D(t,e){return new Promise((function(n,i){if(e&&document.querySelectorAll("script[data-tag="+e+"]").length)return n();var r=document.createElement("script"),o=A({type:"text/javascript",charset:"utf-8"},t);Object.keys(o).forEach((function(t){return function(t,e,n){if(t)return void 0===n?t.getAttribute(e):t.setAttribute(e,n)}(r,t,o[t])})),e&&(r.dataset.tag=e),r.onload=function(){return n()},r.onreadystatechange=function(){var t=r.readyState;["complete","loaded"].includes(t)&&(r.onreadystatechange=null,n())},r.onerror=i,document.body.appendChild(r)}))}!function(t){t[t.equal=0]="equal",t[t.low=-1]="low",t[t.high=1]="high"}(_||(_={}));var M=function(){return(M=Object.assign||function(t){for(var e,n=1,i=arguments.length;n<i;n++)for(var r in e=arguments[n])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}).apply(this,arguments)};function j(t,e,n,i){return new(n||(n=Promise))((function(r,o){function a(t){try{l(i.next(t))}catch(t){o(t)}}function s(t){try{l(i.throw(t))}catch(t){o(t)}}function l(t){var e;t.done?r(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(a,s)}l((i=i.apply(t,e||[])).next())}))}function B(t,e){var n,i,r,o,a={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return o={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(o){return function(s){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,i&&(r=2&o[0]?i.return:o[0]?i.throw||((r=i.return)&&r.call(i),0):i.next)&&!(r=r.call(i,o[1])).done)return r;switch(i=0,r&&(o=[2&o[0],r.value]),o[0]){case 0:case 1:r=o;break;case 4:return a.label++,{value:o[1],done:!1};case 5:a.label++,i=o[1],o=[0];continue;case 7:o=a.ops.pop(),a.trys.pop();continue;default:if(!((r=(r=a.trys).length>0&&r[r.length-1])||6!==o[0]&&2!==o[0])){a=0;continue}if(3===o[0]&&(!r||o[1]>r[0]&&o[1]<r[3])){a.label=o[1];break}if(6===o[0]&&a.label<r[1]){a.label=r[1],r=o;break}if(r&&a.label<r[2]){a.label=r[2],a.ops.push(o);break}r[2]&&a.ops.pop(),a.trys.pop();continue}o=e.call(t,a)}catch(t){o=[6,t],i=0}finally{n=r=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,s])}}}var L=function(){function t(){this.interceptors=[]}return t.prototype.use=function(t,e){return this.interceptors.push({resolved:t,rejected:e}),this.interceptors.length-1},t.prototype.traverse=function(t,e){void 0===e&&(e=!1);var n=Promise.resolve(t);return(e?Array.prototype.reduceRight:Array.prototype.reduce).call(this.interceptors,(function(t,e){if(e){var i=e.resolved,r=e.rejected;n=n.then(i,r)}return t}),""),n},t.prototype.eject=function(t){this.interceptors[t]&&(this.interceptors[t]=null)},t}(),V={defaults:{timeout:0,method:"GET",mode:"cors",redirect:"follow",credentials:"same-origin"},headers:{common:{Accept:"application/json, text/plain, */*"},POST:{"Content-Type":"application/x-www-form-urlencoded"},PUT:{"Content-Type":"application/x-www-form-urlencoded"},PATCH:{"Content-Type":"application/x-www-form-urlencoded"}},baseURL:"",polyfillUrl:"static/js/fetch.min.js",interceptors:{request:new L,response:new L}},U=/^([a-z][a-z\d+\-.]*:)?\/\//i,q=Object.prototype.toString;function H(t){return j(this,void 0,void 0,(function(){var e;return B(this,(function(n){switch(n.label){case 0:if(window.fetch)return[2];n.label=1;case 1:return n.trys.push([1,3,,4]),[4,D({src:t})];case 2:return n.sent(),[3,4];case 3:throw e=n.sent(),new Error("加载 polyfill "+t+" 失败: "+e.message);case 4:return[2]}}))}))}function W(t){return["Accept","Content-Type"].forEach((function(e){return n=e,void((i=t.headers)&&Object.keys(i).forEach((function(t){t!==n&&t.toUpperCase()===n.toUpperCase()&&(i[n]=i[t],delete i[t])})));var n,i})),function(t){if("[object Object]"!==q.call(t))return!1;var e=Object.getPrototypeOf(t);return null===e||e===Object.prototype}(t.body)&&(t.body=JSON.stringify(t.body),t.headers&&(t.headers["Content-Type"]="application/json;charset=utf-8")),t}function F(t){return j(this,void 0,void 0,(function(){var e,n,i,r,o,a,s,l,u,c,p,d,h,f,v,g,m;return B(this,(function(y){switch(y.label){case 0:return e=V.baseURL,n=V.defaults,i=V.interceptors,[4,H(V.polyfillUrl)];case 1:return y.sent(),(r=M(M({},n),t)).headers||(r.headers=function(t){void 0===t&&(t="GET");var e=V.headers[t]||{};return M(M({},V.headers.common),e)}(r.method)),W(r),[4,i.request.traverse(r,!0)];case 2:if((o=y.sent())instanceof Error)throw o;return o.url=function(t,e){return!t||U.test(e)?e:t.replace(/\/+$/,"")+"/"+e.replace(/^\/+/,"")}(e,o.url),a=o.url,s=o.timeout,l=o.params,u=o.method,c=["GET","DELETE","OPTIONS","HEAD"].includes(void 0===u?"GET":u)&&!!l,p=c?x({url:a,query:l}):a,d=[],s&&!o.signal&&(v=new Promise((function(t){h=setTimeout((function(){t(new Error("timeout"))}),s)})),d.push(v),f=new AbortController,o.signal=f.signal),d.push(fetch(p,o).catch((function(t){return t}))),[4,Promise.race(d)];case 3:return g=y.sent(),h&&clearTimeout(h),[4,i.response.traverse(g)];case 4:if((m=y.sent())instanceof Error)throw null==f||f.abort(),m;return[2,m]}}))}))}var J=function(){function t(t){V.interceptors.request.use((function(n){var i=n.url,r=n.method,o=n.body,a=o;if(t.onReportBeforeSend){var s=t.onReportBeforeSend({url:i,method:r,data:o?JSON.parse(o):null});a=(null==s?void 0:s.data)?JSON.stringify(s.data):null}return"GET"!=r&&a?e(e({},n),{body:a}):n}))}return t.prototype.get=function(t,r){return n(this,void 0,void 0,(function(){var n,o;return i(this,(function(i){switch(i.label){case 0:return[4,F(e({url:t},r))];case 1:return[4,(n=i.sent()).json()];case 2:return o=i.sent(),[2,Promise.resolve({data:o,status:n.status,statusText:n.statusText,headers:n.headers})]}}))}))},t.prototype.post=function(t,r,o){return n(this,void 0,void 0,(function(){var n,a;return i(this,(function(i){switch(i.label){case 0:return[4,F(e({url:t,body:r,method:"POST"},o))];case 1:return[4,(n=i.sent()).json()];case 2:return a=i.sent(),[2,Promise.resolve({data:a,status:n.status,statusText:n.statusText,headers:n.headers})]}}))}))},t}(),G=function(){function t(t){this.appkey=t}return t.prototype.getItem=function(t){try{return window.localStorage.getItem(this.getStoreKey(t))}catch(t){return""}},t.prototype.removeItem=function(t){try{window.localStorage.removeItem(this.getStoreKey(t))}catch(t){}},t.prototype.setItem=function(t,e){try{window.localStorage.setItem(this.getStoreKey(t),e)}catch(t){}},t.prototype.setSessionItem=function(t,e){try{window.sessionStorage.setItem(this.getStoreKey(t),e)}catch(t){}},t.prototype.getSessionItem=function(t){try{return window.sessionStorage.getItem(this.getStoreKey(t))}catch(t){return""}},t.prototype.getStoreKey=function(t){return r+this.appkey+"_"+t},t.prototype.createDeviceId=function(){try{var t=window.localStorage.getItem(o);return t||(t=function(t){for(var e="ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz0123456789",n="",i=0;i<t;i++)n+=e.charAt(Math.floor(Math.random()*e.length));return n}(32),window.localStorage.setItem(o,t)),t}catch(t){return""}},t.prototype.clear=function(){try{for(var t=window.localStorage.length,e=0;e<t;e++){var n=window.localStorage.key(e);(null==n?void 0:n.substr(0,9))==r&&window.localStorage.removeItem(n)}}catch(t){}},t.prototype.getStoreCount=function(){var t=0;try{t=window.localStorage.length}catch(t){}return t},t}(),Q="logid_start",K="4.5.16-web";return function(n){function i(t){var e=n.call(this,t)||this;e.qimei36="",e.uselessCycleTaskNum=0,e.underWeakNet=!1,e.pauseSearching=!1,e.send=function(t,n,i){e.storage.setItem(a,Date.now().toString()),e.network.post(e.uploadUrl||e.strategy.getUploadUrl(),t.data).then((function(i){var r;100==(null===(r=null==i?void 0:i.data)||void 0===r?void 0:r.result)?e.delayTime=1e3*i.data.delayTime:e.delayTime=0,n&&n(t.data),t.data.events.forEach((function(t){e.store.removeEvent(t).then((function(){e.removeSendingId(h(t.mapValue))}))})),e.doCustomCycleTask()})).catch((function(n){var r=t.data.events;e.errorReport.reportError(n.code?n.code.toString():"600",n.message),i&&i(t.data);var o=JSON.parse(e.storage.getItem(s));r.forEach((function(t){o&&-1!=o.indexOf(h(t))&&e.store.insertEvent(t,(function(t,n){t&&e.errorReport.reportError("604","insertEvent fail!")})),e.removeSendingId(h(t))})),e.monitorUploadFailed()}))};var i,r,o=w();return e.isUnderIE8=o>0&&o<8,e.isUnderIE8||(e.isUnderIE=o>0,t.needInitQimei&&P(t.appkey,(function(t){e.qimei36=t.q36})),e.network=new J(t),e.storage=new G(t.appkey),e.initCommonInfo(t),e.store=new T(t.appkey,e.storage),e.errorReport=new g(e.config,e.commonInfo,"web",e.network),e.strategy=new m(null==t.needQueryConfig||t.needQueryConfig,e.config,e.commonInfo,e.storage,e.network),e.logidStartTime=e.storage.getItem(Q),e.logidStartTime||(e.logidStartTime=Date.now().toString(),e.storage.setItem(Q,e.logidStartTime)),i=e.logidStartTime,r=Date.now()-parseFloat(i),Math.floor(r/864e5)>=365&&e.storage.clear(),e.initSession(t),e.onDirectUserAction("rqd_js_init",{}),setTimeout((function(){return e.lifeCycle.emit("init")}),0),e.initDelayTime=t.delay?t.delay:1e3,e.cycleTask(e.initDelayTime)),e}return function(e,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function i(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}(i,n),i.prototype.initSession=function(t){var e=18e5;t.sessionDuration&&t.sessionDuration>3e4&&(e=t.sessionDuration),this.beaconSession=new b(this.storage,e,this)},i.prototype.initCommonInfo=function(t){var e=Number(this.storage.getItem(a));try{var n=JSON.parse(this.storage.getItem(s));(Date.now()-e>3e4||!n)&&this.storage.setItem(s,JSON.stringify([]))}catch(t){}t.uploadUrl&&(this.uploadUrl=t.uploadUrl+"?appkey="+t.appkey);var i=[window.screen.width,window.screen.height];window.devicePixelRatio&&i.push(window.devicePixelRatio),this.commonInfo={deviceId:this.storage.createDeviceId(),language:navigator&&navigator.language||"zh_CN",query:window.location.search,userAgent:navigator.userAgent,pixel:i.join("*"),channelID:t.channelID?String(t.channelID):"",openid:t.openid?String(t.openid):"",unid:t.unionid?String(t.unionid):"",sdkVersion:K},this.config.appVersion=t.versionCode?String(t.versionCode):"",this.config.strictMode=t.strictMode},i.prototype.cycleTask=function(t){var e=this;this.intervalID=window.setInterval((function(){e.pauseSearching||e.store.getEvents().then((function(t){0==t.length&&(e.pauseSearching=!0);var n=[],i=JSON.parse(e.storage.getItem(s));i||(i=[]),t&&t.forEach((function(t){var e=h(t.mapValue);-1==i.indexOf(e)&&(n.push(t),i.push(e))})),i.length>1e3&&(i=[]),0!=n.length&&(e.storage.setItem(s,JSON.stringify(i)),e._normalLogPipeline(e.assembleData(n)))})).catch((function(t){}))}),t)},i.prototype.onReport=function(t,e,n){var i=this;if(this.isUnderIE8)this.errorReport.reportError("601","UnderIE8");else{this.pauseSearching=!1;var r=this.generateData(t,e,n);if(n&&0==this.delayTime&&!this.underWeakNet)this._normalLogPipeline(this.assembleData(r));else{var o=r.shift();o&&this.store.insertEvent(o,(function(t){t&&i.errorReport.reportError("604","insertEvent fail!")})).catch((function(t){i._normalLogPipeline(i.assembleData(r))}))}}},i.prototype.onSendBeacon=function(t,e){if(this.isUnderIE)this.errorReport.reportError("605","UnderIE");else{this.pauseSearching=!1;var n=this.assembleData(this.generateData(t,e,!0));"function"==typeof navigator.sendBeacon&&navigator.sendBeacon(this.uploadUrl||this.strategy.getUploadUrl(),JSON.stringify(n))}},i.prototype.generateData=function(t,n,i){var r=[],o="4.5.16-web_"+(i?"direct_log_id":"normal_log_id"),a=Number(this.storage.getItem(o));return a=a||1,n=e(e({},n),{A99:i?"Y":"N",A100:a.toString(),A72:K,A88:this.logidStartTime}),a++,this.storage.setItem(o,a.toString()),r.push({eventCode:t,eventTime:Date.now().toString(),mapValue:p(n,this.config.strictMode)}),r},i.prototype.assembleData=function(t){var n=this.beaconSession.getSession();return{appVersion:this.config.appVersion?d(this.config.appVersion):"",sdkId:"js",sdkVersion:K,mainAppKey:this.config.appkey,platformId:3,common:p(e(e({},this.additionalParams),{A2:this.commonInfo.deviceId,A8:this.commonInfo.openid,A12:this.commonInfo.language,A17:this.commonInfo.pixel,A23:this.commonInfo.channelID,A50:this.commonInfo.unid,A76:n.sessionId,A101:this.commonInfo.userAgent,A102:window.location.href,A104:document.referrer,A119:this.commonInfo.query,A153:this.qimei36}),!1),events:t}},i.prototype.monitorUploadFailed=function(){this.uselessCycleTaskNum++,this.uselessCycleTaskNum>=5&&(window.clearInterval(this.intervalID),this.cycleTask(6e4),this.underWeakNet=!0)},i.prototype.doCustomCycleTask=function(){this.uselessCycleTaskNum>=5&&(window.clearInterval(this.intervalID),this.cycleTask(this.initDelayTime)),this.uselessCycleTaskNum=0,this.underWeakNet=!1},i}(v)}()})),at=function(t){var e=t.replace(/[[]/,"\\[").replace(/[\]]/,"\\]"),n=new RegExp("[\\?&]"+e+"=([^&#]*)").exec(window.location.search||window.location.hash);return null===n?"":decodeURIComponent(n[1].replace(/\+/g," "))},st=function(t,e){return Object.keys(t).filter((function(t){return!e.includes(t)})).map((function(e){var n;return e in t?((n={})[e]=t[e],n):{}})).reduce((function(t,e){return Object.assign(t,e)}),{})},lt=function(t){return"object"==typeof HTMLElement?t instanceof HTMLElement:t&&"object"==typeof t&&1===t.nodeType&&"string"==typeof t.nodeName},ut=function(t){var e=t;return"string"==typeof e&&(e=document.querySelector(e)),lt(e)?e:null},ct=function(t,e){return void 0===e&&(e=Date.now()+"-"+Math.random()),new Promise((function(n,i){if(document.getElementById(e))n(null);else{var r=document.getElementsByTagName("head")[0],o=document.createElement("script"),a=function(){o.onload=null,o.onreadystatechange=null,this.readyState&&"loaded"!==this.readyState&&"complete"!==this.readyState||n(null)};o.onload=a,o.onreadystatechange=a,o.onerror=function(t){o.onerror=null,r.removeChild(o),i(t)},o.src=t,o.id=e,o.crossOrigin="anonymous",r.appendChild(o)}}))},pt=function(t){var e;if("string"==typeof t||"object"==typeof t&&(null===(e=t.baseInfo)||void 0===e?void 0:e.appkey)&&t.onUserAction&&t.onDirectUserAction)return!0},dt=function(t,e){var i,r;return void 0===e&&(e="udf_kv"),[S.PGIN,S.PGOUT].includes(t.eventName)?n(n({},t.publicParams),((i={})[e]=JSON.stringify(n({},t.pageParams)),i)):n(n({},t.publicParams),((r={})[e]=JSON.stringify(n({cur_pg:n({},t.pageParams)},t.businessParams)),r))},ht=function(t,e,i){var r;void 0===e&&(e=1),void 0===i&&(i={});for(var o=i.eid?[i.eid]:[],a=i.eid?[i]:[],s=i,l=t;l;){var u=null===(r=l.data)||void 0===r?void 0:r.eid;u&&(e?s=n(n({eid:u},l.data.params),s):(o.push(u),a.push(n({eid:u},l.data.params)))),l=l.parent}return e?s:{element_path:o,element_params:a}},ft=function(t){return t&&"[object Object]"===Object.prototype.toString.call(t)?Object.entries(t).reduce((function(t,e){var n,i=e[0].substring(3);return Object.assign(t,((n={})[i]=e[1],n))}),{}):{}},vt=function(t,e){var n;return void 0===e&&(e=0),function(){for(var i=this,r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];clearTimeout(n),n=setTimeout((function(){t.apply(i,r)}),e)}},gt=function(t){var e=[];if(null==t?void 0:t[0])for(var n=t.length-1;n>=0;n--){var i=t[n];1===i.nodeType&&e.unshift(i)}return e},mt=function(){function t(t){if(this.type=O.BEACON,!pt(t.beacon))throw new Error("\n        [universalReport] beacon must be a beacon application appkey or a beacon instance")}return t.prototype.init=function(t){var e=this;return new Promise((function(n,i){"object"==typeof t.beacon?(e.beacon=t.beacon,n()):"string"==typeof t.beacon?(!t.useBuildInBeacon&&window.BeaconAction||(window.BeaconAction=ot),n(e.initBeacon(t))):i(new Error("\n          [universalReport] beacon must be a beacon application appkey or a beacon instance"))}))},t.prototype.initBeacon=function(t){var e;try{this.beacon=new window.BeaconAction(n({appkey:t.beacon,versionCode:null!==(e=t.version)&&void 0!==e?e:"",delay:1e3,isDebug:t.debug,needQueryConfig:!1,onReportFail:function(t){}},t.beaconOptions))}catch(e){null==t||t.beaconFailCallback({err_message:e,result:1,fail_case:"beaconInitFail"})}},t.prototype.report=function(t){var e;if(t.eventName){var n=dt(t);t.isSendBeacon?null===(e=this.beacon)||void 0===e||e.onSendBeacon(t.eventName,n):~[S.PGIN,S.PGOUT].indexOf(t.eventName)&&this.beacon.onDirectUserAction?this.beacon.onDirectUserAction(t.eventName,n):this.beacon.onUserAction&&this.beacon.onUserAction(t.eventName,n)}},t}();function yt(t){return new mt(t)}var bt=function(){function t(t){if(this.type=O.APP,this.beaconReportPool=[],!pt(t.beacon))throw new Error("\n        [universalReport] beacon must be a beacon application appkey or a beacon instance");this.appkey="string"==typeof t.beacon?t.beacon:t.beacon.baseInfo.appkey,this.options=t,this.enableBeaconReportRetry=t.enableBeaconReportRetry;try{this.topWindow=window.top,this.topWindow.dtResponseCallbacks={}}catch(t){this.topWindow=window,window.dtResponseCallbacks={}}}return t.prototype.init=function(){var t,e,n,i,r,s;return o(this,void 0,void 0,(function(){var o,l,u;return a(this,(function(a){switch(a.label){case 0:return[4,this.waitForDtJsReporterReady()];case 1:return a.sent(),this.nativeReportBridge?(null===(e=(t=this.options).webviewVisibilityCallback)||void 0===e||e.call(t,{visible:!1}),[4,this.checkWebviewVisibility(!0)]):[3,3];case 2:a.sent(),a.label=3;case 3:return this.nativeReportBridge?(this.initFallbackChannel(),[2,Promise.resolve({reason:M.REPORT_EXIST})]):(null===(i=null===(n=this.topWindow)||void 0===n?void 0:n.DtJsReporter)||void 0===i?void 0:i.reportEvent)?this.hardwareOS===X?[3,5]:(o=this,[4,this.getJsApiCallbackAccess()]):[2,Promise.reject({channel:O.BEACON,reason:M.REPORT_NIL})];case 4:return o.hasJsApiCallback=a.sent()||!1,this.initFallbackChannel(),[2,Promise.resolve({reason:M.REPORT_FALLBACK})];case 5:return(null==(l=null===(s=null===(r=this.topWindow)||void 0===r?void 0:r.DtJsReporter)||void 0===s?void 0:s.isSupport)?void 0:l())?(u=this,[4,this.getJsApiCallbackAccess()]):[3,7];case 6:return u.hasJsApiCallback=a.sent()||!1,this.initFallbackChannel(),[2,Promise.resolve({reason:M.REPORT_FALLBACK})];case 7:return[2,Promise.reject({channel:O.BEACON,reason:M.ANDROID_NOT_SUPPORT})]}}))}))},t.prototype.checkWebviewVisibility=function(t){var e=this;return void 0===t&&(t=!1),new Promise((function(n){var i;t&&(i=setTimeout((function(){var t,r;i=null,null===(r=(t=e.options).webviewVisibilityCallback)||void 0===r||r.call(t,{visible:!0}),n()}),500));var r=""+Date.now()+Math.random().toString().slice(2,5);e.topWindow.dtResponseCallbacks[r]=function(t){var r,o;i&&clearTimeout(i);var a={};try{a="string"==typeof t?JSON.parse(t):t}catch(t){return}null===(o=(r=e.options).webviewVisibilityCallback)||void 0===o||o.call(r,a),n()};var o=JSON.stringify({handlerName:j.CHECK_WEBVIEW,callbackId:r});e.nativeReportBridge.postMessage(o)}))},t.prototype.report=function(t){var e,n;t.isSendBeacon&&"ready"===this.beaconChannelStatus?this.beaconChannel.report(t):this.nativeReportBridge?this.nativeReport(t):(null===(n=null===(e=this.topWindow)||void 0===e?void 0:e.DtJsReporter)||void 0===n?void 0:n.reportEvent)&&this.bridgeReport(t)},t.prototype.getJsApiCallbackAccess=function(){var t=this;return new Promise((function(e){var n;setTimeout((function(){e(!1)}),500),null===(n=t.topWindow.DtJsReporter)||void 0===n||n.getSdkVersion({onCallback:function(t){var n=t.data;e((null==n?void 0:n.sdkVersion)>2100)}})})).catch((function(t){}))},t.prototype.initBeaconChannel=function(t){var e=this;return this.beaconChannel=yt(t),Promise.resolve(this.beaconChannel.init(t)).then((function(){e.beaconChannelStatus="ready";for(var t=null;t=e.beaconReportPool.shift();)e.beaconChannel.report(t)})).catch((function(t){}))},t.prototype.bridgeReport=function(t){var e,n,i,r=this,o=null===(n=null===(e=this.topWindow)||void 0===e?void 0:e.DtJsReporter)||void 0===n?void 0:n.reportEvent,a=dt(t);this.hasJsApiCallback&&(i=setTimeout((function(){i=null,r.callbackTimeoutHandler(t,F)}),1e3)),o({params:a,eventId:t.eventName,appKey:this.appkey,onCallback:this.nativeCallback(t,i,F)})},t.prototype.beaconReport=function(t){this.enableBeaconReportRetry&&("ready"!==this.beaconChannelStatus?this.beaconReportPool.push(t):this.beaconChannel.report(t))},t.prototype.fallbackReport=function(t){this.beaconReport(t)},t.prototype.nativeCallback=function(t,e,n){var i=this;return function(r){var o,a,s,l;e&&clearTimeout(e);var u={ret:"",msg:""};try{u="string"==typeof r?JSON.parse(r):r}catch(t){u={ret:L.ERROR,msg:"callback result parse error"}}var c=u.ret,p=u.msg;c!==L.SUCCESS&&i.fallbackReport(t),null===(a=null===(o=i.options)||void 0===o?void 0:o.jsApiReportCallback)||void 0===a||a.call(o,{code:c,msg:p,step:n,event_type:null==t?void 0:t.eventName,pgid:null==t?void 0:t.pgid,eid:null===(s=null==t?void 0:t.businessParams)||void 0===s?void 0:s.eid,event_id:null===(l=null==t?void 0:t.publicParams)||void 0===l?void 0:l.dt_eventid_h5})}},t.prototype.waitForDtJsReporterReady=function(){var t=this;return new Promise((function(e){t.setNativeReportBridge(),t.nativeReportBridge?e():setTimeout((function(){t.setNativeReportBridge(),e()}),1e3)}))},t.prototype.initFallbackChannel=function(){this.initBeaconChannel(this.options),this.beaconChannelStatus="init"},t.prototype.nativeReport=function(t){var e=this;if(this.nativeReportBridge){var n,i=""+Date.now()+Math.random().toString().slice(2,5),r=dt(t);n=setTimeout((function(){n=null,e.callbackTimeoutHandler(t,J),delete e.topWindow.dtResponseCallbacks[i]}),1e3);this.topWindow.dtResponseCallbacks[i]=function(r){n&&(e.nativeCallback(t,n,J)(r),delete e.topWindow.dtResponseCallbacks[i])};var o=JSON.stringify({handlerName:j.REPORT_EVENT,callbackId:i,data:{params:r,eventId:t.eventName,appKey:this.appkey}});this.nativeReportBridge.postMessage(o)}},t.prototype.callbackTimeoutHandler=function(t,e){var n,i,r;this.fallbackReport(t),null===(i=null===(n=this.options)||void 0===n?void 0:n.jsApiReportCallback)||void 0===i||i.call(n,{code:L.OVER_TIME,msg:"timeout",step:e,event_type:null==t?void 0:t.eventName,event_id:null===(r=null==t?void 0:t.publicParams)||void 0===r?void 0:r.dt_eventid_h5})},t.prototype.setNativeReportBridge=function(){var t,e,n,i,r,o;this.hardwareOS===X&&(null===(e=null===(t=this.topWindow)||void 0===t?void 0:t.dtBridge)||void 0===e?void 0:e.postMessage)&&(this.nativeReportBridge=this.topWindow.dtBridge),this.hardwareOS===z&&(null===(o=null===(r=null===(i=null===(n=this.topWindow)||void 0===n?void 0:n.webkit)||void 0===i?void 0:i.messageHandlers)||void 0===r?void 0:r.dtBridge)||void 0===o?void 0:o.postMessage)&&(this.nativeReportBridge=this.topWindow.webkit.messageHandlers.dtBridge)},t}();var wt={app:function(t){return new bt(t)},beacon:yt},Pt=Object.keys(wt);var _t=function(){},Et=function(t){function r(e,n,i,r){void 0===n&&(n=""),void 0===i&&(i=""),void 0===r&&(r="");var o=t.call(this)||this;return o.options=e,o.scenePlat=n,o.sceneVersion=i,o.hardwareOS=r,o.channelReady=!1,o.visibilityReady=!0,o.pool=[],o.loadChannel(e),o}return e(r,t),r.prototype.loadChannel=function(t){this.setChannel(t),this.initChannel(t)},r.prototype.setChannel=function(t){var e=t.channel,n=void 0===e?O.APP:e;if(this.scenePlat===tt&&n===O.APP&&this.isLowerThanRequiredVersion()&&(n=O.BEACON),~Pt.indexOf(n))this.curChannel=wt[n](t);else if("function"==typeof n)this.curChannel=n(t);else{if("object"!=typeof n)throw new Error("[universalReport] channel must be "+Pt.map((function(t){return'"'+t+'"'})).join(", ")+", a function or a object");this.curChannel=n}if(n===O.APP&&(this.curChannel.hardwareOS=this.hardwareOS),!this.curChannel.report)throw new Error("[universalReport] no report function found in channel")},r.prototype.isLowerThanRequiredVersion=function(){var t,e=null===(t=q[this.scenePlat])||void 0===t?void 0:t[this.hardwareOS];return!!e&&function(t,e){[t,e].forEach(C);for(var n=T(t),i=T(e),r=0;r<Math.max(n.length-1,i.length-1);r++){var o=parseInt(n[r]||"0",10),a=parseInt(i[r]||"0",10);if(o>a)return E.high;if(a>o)return E.low}var s=n[n.length-1],l=i[i.length-1];if(s&&l){var u=s.split(".").map(A),c=l.split(".").map(A);for(r=0;r<Math.max(u.length,c.length);r++){if(void 0===u[r]||"string"==typeof c[r]&&"number"==typeof u[r])return-1;if(void 0===c[r]||"string"==typeof u[r]&&"number"==typeof c[r])return 1;if(u[r]>c[r])return E.high;if(c[r]>u[r])return E.low}}else if(s||l)return s?E.low:E.high;return E.equal}(this.sceneVersion,e)<0},r.prototype.initChannel=function(t){var e,i=this,r=t.channel;Object.assign(t,{beaconFailCallback:function(t){var e=t.fail_case;i.emit(e,t)},jsApiReportCallback:function(t){i.emit("jsApiReport",t)},webviewVisibilityCallback:function(t){t.visible?(i.visibilityReady=!0,i.flushReport()):i.visibilityReady=!1}}),this.curChannel.init=null!==(e=this.curChannel.init)&&void 0!==e?e:_t,Promise.resolve(this.curChannel.init(t)).then((function(t){var e;i.emit("initChannel",{report_type:r,result:0,deviceId:(null===(e=i.curChannel.beacon)||void 0===e?void 0:e.getDeviceId())||""}),i.curChannel.type===O.APP&&i.emit("jsApiReport",n({step:H,result:"success"},null!=t?t:{})),i.channelReady=!0,i.flushReport()})).catch((function(t){var e,r=t,o=r.channel,a=r.reason;if(i.curChannel.type===O.APP&&i.emit("jsApiReport",{step:H,result:"fail",reason:a}),!("string"==typeof(e=o)&&~Pt.indexOf(e)||"function"==typeof e||"object"==typeof e&&"report"in e))throw t;i.loadChannel(n(n({},i.options),{channel:o}))})).catch((function(t){i.emit("initChannel",{report_type:r,result:1,err_message:t})}))},r.prototype.flushReport=function(){if(this.channelReady&&this.visibilityReady)for(var t=null;t=this.pool.shift();)t.call(this)},r.prototype.report=function(t){var e=this,r=t.uid,o=i(t,["uid"]),a=n({uid:r},o),s=function(){var t,n,i;e.curChannel.report(o),e.emit("report",a),(null==o?void 0:o.monitoringReport)&&e.emit("monitoringReport",{device_id:null===(t=e.curChannel.beacon)||void 0===t?void 0:t.getDeviceId(),report_type:e.curChannel.type,event_type:(null==o?void 0:o.eventName)||"",publicParams:null==o?void 0:o.publicParams}),(null==o?void 0:o.monitoringJsApiReport)&&e.emit("jsApiReport",{pgid:null==o?void 0:o.pgid,eid:null===(n=null==o?void 0:o.businessParams)||void 0===n?void 0:n.eid,step:W,event_type:null==o?void 0:o.eventName,report_type:e.curChannel.type,event_id:null===(i=null==o?void 0:o.publicParams)||void 0===i?void 0:i.dt_eventid_h5})};this.channelReady&&this.visibilityReady?s():(this.visibilityReady||this.curChannel.type!==O.APP||a.eventName!==S.CLCK||this.curChannel.checkWebviewVisibility(),this.pool.push(s))},r}(U);var It={attaUrl:"https://h.trace.qq.com/kv",attaId:"0b300050665",token:"6554817333"},Rt={attaUrl:"https://h.trace.qq.com/kv",attaId:"***********",token:"9671122527"};function St(t,e){void 0===e&&(e=It);try{var i=function(t,e){var i,r=[],o=Math.random().toString(36).substr(2,8);i=e.attaUrl+"?attaid="+e.attaId+"&token="+e.token+"&_dc="+o;var a=encodeURIComponent((""+window.location.host+window.location.pathname).replace(/\./g,"_")),s=n(n({Attaid:e.attaId,token:e.token,url:encodeURIComponent(window.location.href),path:a},t),{dc:o});return Object.keys(s).forEach((function(t){r.push(t+"="+encodeURIComponent(s[t]))})),{url:i,data:r.join("&")}}(t,e);!function(t,e){var n=new XMLHttpRequest;n.open("POST",t,!0),n.timeout=1e4,n.setRequestHeader("content-type","application/x-www-form-urlencoded"),n.onreadystatechange=function(){4===n.readyState&&200!==n.status&&n.status},n.ontimeout=function(){n.abort()},n.send(e)}(i.url,i.data)}catch(t){throw new Error("[universalReport] atta report fail")}}var Ot=function(){function t(t){this.context=t,this.events={}}return t.prototype.on=function(t,e){var n=this.events[t]||[];return n.push(e),this.events[t]=n,this},t.prototype.off=function(t,e){if(void 0===e)this.events[t]=[];else for(var n=this.events[t]||[],i=n.length-1;i>=0;i--)if(n[i]===e){n[i]=null;break}return this},t.prototype.emit=function(t,e,n){var i="click"===t,r=this.events[t]||[];"boolean"==typeof n&&(i=n);for(var o=r.length-1;o>=0;o--){var a=r[o];if("function"==typeof a)if(i)setTimeout((function(t,e,n){null==t||t.call(e,n)}),0,a,this.context,e);else try{a.call(this.context,e)}catch(t){}}return this},t.prototype.clear=function(){this.events={}},t.prototype.listeners=function(t){return this.events[t]||[]},t}(),kt=function(t,e,n){var i=e(t,n),r=t.childNodes;if("boolean"==typeof i)return i;for(var o=0,a=r.length;o<a;o++){var s=r[o];if(s&&1===s.nodeType)if(kt(s,e,i))return!0}},Tt=function(t,e){var n=e(t),i=t.childNodes;if(n)return n;for(var r=i.length-1;r>=0;r--){var o=i[r];if(o&&1===o.nodeType){var a=Tt(o,e);if(a)return a}}},At=function(t,e){for(var n=t;n;){var i=e(n);if("boolean"==typeof i&&i)break;n=n.parentElement}return n},Ct=function(t,e){for(var n=[t];n.length>0;){var i=n.shift(),r=e(i);if(!0===r)return r;!1!==r&&(i.children.length>0&&(n=i.children.concat(n)))}},Nt=function(){function t(t,e,n){this.data=n||{},this.parent=null,this.children=[],this.uid="",this.emitter=new Ot(this),this.unbinds={},this.width=0,this.height=0,this.effectiveWidth=0,this.effectiveHeight=0,this.top=0,this.left=0,this.scrollTop=0,this.scrollLeft=0,this.maxScrollTop=0,this.maxScrollLeft=0,this.maxIntersectArea=0,this.tree=t,this.el=e,this.lazy=!0,this.delegates={},this.visible=!1,this.pt=0,this.pl=0,this.overflowHide=!0,this.isScroller=!1,this.scrolling=!1,this.rect={top:0,left:0,right:0,bottom:0},this.viewport={top:0,left:0,right:0,bottom:0},this.lazy=!1,this.lastHash="",this.domNativeEvents={click:!0,scroll:!0},this.fastTriggeredEvents={click:!0},this.attach(e)}return t.getWindowNode=function(){if(t.windowNode)return t.windowNode;var e=new t;return e.scrollLeft=window.scrollX,e.scrollTop=window.scrollY,e.top=0,e.left=0,e.width=window.innerWidth,e.height=window.innerHeight,e.isScroller=!0,e.viewport={top:0,left:0,right:window.innerWidth,bottom:window.innerHeight},e.rect={top:0,left:0,right:window.innerWidth,bottom:window.innerHeight},e.getScrollParent=function(){return null},e.attachScroller=function(t,n){var i=t||window,r=n.throttle,o=function(){var t=i.scrollX-e.scrollLeft,n=i.scrollY-e.scrollTop;e.scrollLeft=i.scrollX,e.scrollTop=i.scrollY,e.moveTree(t,n,!0),e.scrolling=!1,e.emitter.emit("scrollend")};if(r>0){var a=vt(o,r);o=function(){e.scrolling=!0,a()}}i.addEventListener("scroll",o)},window.addEventListener("resize",(function(){e.width=window.innerWidth,e.height=window.innerHeight,e.viewport={top:0,left:0,right:window.innerWidth,bottom:window.innerHeight},e.rect={top:0,left:0,right:window.innerWidth,bottom:window.innerHeight},e.emit("resize")})),t.windowNode=e,e},t.getNode=function(e){return t.store[e[t.sessionDomKey]]},t.prototype.updateData=function(e,n,i){var r=this,o=e.attributes,a=function(t){var n=o[t.name];if(n)return t.func.call(r,n.value,e)},s=t.globalNodeConfig,l=!1;if(this.stopBubbleFlag=s.stopBubbleFlag,void 0===n)Object.keys(s.map).forEach((function(t){return a(s.map[t])}));else{var u=s.map[n];u&&(l=a(u))}s.events.forEach((function(t){var a=t.name;r.off(a,t.action),t.condition.call(r,o,e)&&r.on(a,t.action);var s=t.keyAttrs;s instanceof Array&&n&&l&&s.indexOf(n)>-1&&(!i||i!==r.lastHash)&&(r.lastHash=i,a===B.EXPOSE?r.visible&&r.emit(a):r.emit(a))})),void 0===n?(this.updateOfData(o),this.updateScrollData(o,e),this.updateLazyData(o)):n===D.OVERFLOWHIDE_ATTR?this.updateOfData(o):n===D.ISSCROLLER_ATTR?this.updateScrollData(o,e):n===D.LAZY_ATTR&&this.updateLazyData(o)},t.prototype.updateOfData=function(t){var e=t[D.OVERFLOWHIDE_ATTR];this.overflowHide=!(e&&"false"===e.value)},t.prototype.updateScrollData=function(t,e){t[D.ISSCROLLER_ATTR]?this.attachScroller(e):this.off("scroll")},t.prototype.updateLazyData=function(t){var e=t[D.LAZY_ATTR];this.lazy=!(!e||"true"!==e.value)},t.prototype.on=function(t,e){var n=this,i=t===B.EXPOSE&&this.emitter.listeners(B.EXPOSE).length<=0;return this.elOn(t,e),i&&this.elOn(B.VISIBILITYCHANGED,(function(t){t?n.emit(B.EXPOSE):n.emit(B.HIDE)})),this},t.prototype.attach=function(e){var n=!1,i=e;if(this.detach(!0),i){var r=i[t.sessionDomKey];n=!r,this.el=i,n&&(r=t.sessionId+"_"+t.autoId,t.autoId+=1,this.uid=r,i[t.sessionDomKey]=r,t.store[r]=this),this.updateData(i)}return n},t.prototype.detach=function(e){void 0===e&&(e=!1);var n=this.el,i=this.unbinds;if(this.unbinds={},n){var r=n[t.sessionDomKey];r&&(this.data.keepReport&&t.removedStore.set(n,t.store[r]),t.store[r]=null,n[t.sessionDomKey]=null)}e&&this.emitter.clear(),Object.keys(i).forEach((function(t){try{i[t]()}catch(t){}}))},t.prototype.attachScroller=function(t,e){var n=this;if(!nt){var i=function(){var e=t.scrollLeft-n.scrollLeft,i=t.scrollTop-n.scrollTop;n.scrollTop=t.scrollTop,n.scrollLeft=t.scrollLeft,n.maxScrollTop=Math.max(n.maxScrollTop,n.scrollTop),n.maxScrollLeft=Math.max(n.maxScrollLeft,n.scrollLeft),n.moveTree(e,i,!0),n.scrolling=!1,n.emitter.emit("scrollend")},r=null==e?void 0:e.throttle;if(r>0){var o=vt(i,r);i=function(){n.scrolling=!0,o()}}this.scrollTop=t.scrollTop,this.scrollLeft=t.scrollLeft,this.isScroller=!0,this.elOn("scroll",i)}},t.prototype.getScrollParent=function(){for(var e=this.parent;e&&!e.isScroller;)e=e.parent;return e||t.getWindowNode()},t.prototype.getScrollingParent=function(){for(var t=this.getScrollParent();t&&!t.scrolling;)t=t.getScrollParent();return(null==t?void 0:t.scrolling)?t:void 0},t.prototype.getRect=function(){return this.rect},t.prototype.recalc=function(){var t=this;if(!nt){var e=this.el;if(e){var n=this.getScrollParent(),i=this.getScrollingParent();if(i)return this.scrollEndHandle&&i.emitter.off("scrollend",this.scrollEndHandle),this.scrollEndHandle=function(){t.recalc(),i.emitter.off("scrollend",t.scrollEndHandle),t.scrollEndHandle=null},void i.emitter.on("scrollend",this.scrollEndHandle);var r=e.getBoundingClientRect(),o=n.getRect(),a=r.top-o.top+n.scrollTop;this.top=a;var s=r.left-o.left+n.scrollLeft;this.left=s,this.width=r.width,this.height=r.height,this.isScroller?(this.scrollTop=this.el.scrollTop,this.scrollLeft=this.el.scrollLeft,this.pt=n.pt+a-this.scrollTop,this.pl=n.pl+s-this.scrollLeft,this.effectiveWidth=e.scrollWidth||this.effectiveWidth,this.effectiveHeight=e.scrollHeight||this.effectiveHeight):(this.effectiveWidth=r.width||this.effectiveWidth,this.effectiveHeight=r.height||this.effectiveHeight);var l=this.rect.top!==r.top||this.rect.left!==r.left||this.rect.right!==r.right||this.rect.bottom!==r.bottom;return this.rect.top=r.top,this.rect.left=r.left,this.rect.right=r.right,this.rect.bottom=r.bottom,this.calcViewPort(),this.syncVisible(),l}}},t.prototype.move=function(t,e){this.isScroller&&(this.pt=this.pt-e,this.pl=this.pl-t);var n=this.rect;n.top=n.top-e,n.left=n.left-t,n.right=n.right-t,n.bottom=n.bottom-e,this.calcViewPort(),this.syncVisible()},t.prototype.calcViewPort=function(){if(!nt){var e=this.rect,n=this.parent||t.getWindowNode(),i=this.viewport,r=n.viewport;this.overflowHide?(i.top=Math.max(e.top,r.top),i.left=Math.max(e.left,r.left),i.right=Math.min(e.right,r.right),i.bottom=Math.min(e.bottom,r.bottom)):(i.top=r.top,i.left=r.left,i.right=r.right,i.bottom=r.bottom)}},t.prototype.calcExposeArea=function(){var t=this.rect,e=this.viewport,n=Math.max(t.left,e.left),i=Math.min(t.right,e.right),r=Math.max(e.bottom-t.top,0);return Math.max(r*(i-n),0)},t.prototype.updateNode=function(){this.maxScrollTop=this.scrollTop,this.maxScrollLeft=this.scrollLeft,this.effectiveWidth=this.width,this.effectiveHeight=this.height,this.maxIntersectArea=this.calcExposeArea()},t.prototype.syncVisible=function(){if(!nt){var t=this.calcVisible();return t!==this.visible?(this.visible=t,t&&this.updateNode(),this.emitter.emit(B.VISIBILITYCHANGED,t),!0):void 0}},t.prototype.moveTree=function(e,n,i){var r,o=this;if(this.el&&!i&&(At(this.el,(function(e){if(o.el!==e&&t.getNode(e))return!0;if(1!==e.nodeType)return!0;var n=getComputedStyle(e);return("fixed"===n.position||"sticky"===n.position)&&(r=e,!0)})),r))return;i||this.move(e,n);for(var a=this.children.length-1;a>=0;a--)this.children[a].moveTree(e,n)},t.prototype.calcVisible=function(){var e=this.rect,n=(this.parent||t.getWindowNode()).viewport,i=Math.max(this.calcExposeArea(),this.maxIntersectArea||0);return this.maxIntersectArea=Math.max(i,0),!(e.left>=n.right||e.right<=n.left||e.top>=n.bottom||e.bottom<=n.top)},t.prototype.destroy=function(){this.remove(),this.detach()},t.prototype.elOn=function(e,n){var i,r=this,o=this.emitter;if(o.on(e,n),this.domNativeEvents[e]){var a=this.unbinds,s=this.el;if(!a[e]){if(this.fastTriggeredEvents[e]){var l=this.tree.root;if(l&&!l.delegates[e]&&l.el){var u=function(n){var i=n,r=At(i.target,(function(t){return!!(null==t?void 0:t.getAttribute("dt-eid"))}));if(r){var o=t.getNode(r)||t.removedStore.get(r);!o||o.lazy||i["_vn_id_"+o.uid]||(o.emitter.emit(e,i,o.lazy),i["_vn_id_"+o.uid]=!0)}};l.delegates[e]=u,l.el.addEventListener(e,u)}}var c=function(t){var n=t;if(!n["_vn_id_"+r.uid]){if(r.stopBubbleFlag){if(n.reported)return;n.reported=!0}o.emit(e,n,r.lazy),n["_vn_id_"+r.uid]=!0}};s.addEventListener(e,c),a[e]=function(){s.removeEventListener(e,c)}}}else if(nt&&e===B.VISIBILITYCHANGED){if(!(a=this.unbinds)[e]){var p=this.el;null===(i=t.iobserver)||void 0===i||i.observe(p),a[e]=function(){var e;null===(e=t.iobserver)||void 0===e||e.unobserve(p)}}}return this},t.prototype.off=function(t,e){return this.emitter.off(t,e),this},t.prototype.emit=function(t,e){return this.emitter.emit(t,e),this},t.prototype.setData=function(t){Object.assign(this.data,t)},t.prototype.mapParent=function(t){var e=[];if(this.parent)for(var n=this.parent;n;){var i=n;if("function"==typeof t&&(i=t(n)),"boolean"==typeof i&&i)break;e.push(i),n=n.parent}return e},t.prototype.index=function(){var t=this.parent;return t?t.children.indexOf(this):0},t.prototype.append=function(t){var e=t;e&&(e.remove(),e.parent=this,this.children.push(e),Mt([e]))},t.prototype.insert=function(t,e){var n=this;e&&(e.forEach((function(t){var e=t;e.remove(),e.parent=n})),Array.prototype.splice.apply(this.children,s([t,0],e)))},t.prototype.remove=function(){var t=this.parent;this.parent=null,t&&(t.children=t.children.filter((function(e){return e.parent===t})))},t.prototype.clear=function(){if(this.children.length>0){var t=this.children;this.children=[];for(var e=0,n=t.length;e<n;e++){t[e].parent=null}}},t.store={},t.removedStore=new WeakMap,t.autoId=1,t.sessionId=Math.random().toString(36).substring(2),t.sessionDomKey="__doid_"+t.sessionId,t.windowNode=null,t}(),xt=function(t,e){var n,i;if(t){for(var r=null,o=t;!r&&o;)if(r=null===(n=o.data)||void 0===n?void 0:n[e],o=o.parent,!r&&Dt(o)){r=null===(i=o.data)||void 0===i?void 0:i[e];break}return r}},Dt=function(t){var e,n=null==t?void 0:t.el;return null===(e=null==n?void 0:n.hasAttribute)||void 0===e?void 0:e.call(n,"dt-pgid")},Mt=function(t){t.forEach((function(t){var e=xt(t,"pgid");e&&(t.data.keepReportPgId=e)}))},jt=function(){function t(){var t;this.root=null,this.rootDom=null,this.genHash=(t=0,function(){return t+=1,""+(new Date).getTime()+t})}return t.attachDom=function(e){if(void 0===e&&(e=document.body),!lt(e))throw new Error('parameter 1 is not of type "Node"');var n=t.getVtreeMap().get(e)||null;return n||(n=new this,t.getVtreeMap().set(e,n),n.attach(e),Nt.getWindowNode().on("resize",(function(){n.reflow(n.root)})),Nt.getWindowNode().attachScroller(window,{throttle:200}),Nt.getWindowNode().append(n.root),n)},t.detachDom=function(e){var n;if(!lt(e))throw new Error('parameter 1 is not of type "Node"');var i=t.getVtreeMap().get(e)||null;i&&((null===(n=t.reporter)||void 0===n?void 0:n.options.enableSPAMode)||i.observer.disconnect(),i.detach())},t.getVtreeMap=function(){return t.vTreeMap},t.shouldBeNode=function(e){var n=e.attributes,i=t.treeConfigs,r=i.overFlowAttr,o=i.isScrollerAttr,a=i.lazyAttr;return!!t.getVtreeMap().get(e)||(!!(n[r]||n[o]||n[a])||t.attrs.some((function(t){return n[t]})))},t.shouldBePageNode=function(t){return!!t.attributes["dt-pgid"]},t.prototype.attach=function(e){var n,i=this;if(!t.shouldBeNode(e))throw new Error("root should be a node");this.rootDom=e,this.root=Nt.getNode(e)||new Nt(this,e),this.update({el:e}),this.reflow({el:e}),this.observer=new MutationObserver((function(t){try{var e=i.handleMutations(t),n=e.updateArr,r=e.reflowArr;n&&n.length>0&&i.update(n),r&&r.length>0&&i.reflow(r)}catch(t){}})),this.observer.observe((null===(n=t.reporter)||void 0===n?void 0:n.options.enableSPAMode)?document.body:e,{characterData:!0,attributes:!0,childList:!0,subtree:!0,attributeFilter:s(K,Object.keys(Nt.globalNodeConfig.map))})},t.prototype.detach=function(){t.getVtreeMap().delete(this.rootDom),this.remove({all:[this.root.el]})},t.prototype.update=function(t){var e=this,n=this.genHash();(t instanceof Array?t:[t]).forEach((function(t){e[t.action||"add"](t,n)}))},t.prototype.createTree=function(e){var n=this,i=[],r=[];return kt(e,(function(e,o){var a,s;if(t.shouldBeNode(e)){var l=Nt.getNode(e);if(t.shouldBePageNode(e)){var u=null===(a=e.getAttribute("dt-pgid"))||void 0===a?void 0:a.trim(),c=t.reporter.getDomPageParams(u,e);t.reporter.setPage(c,e)}return l?l.clear():l=new Nt(n,e),(null===(s=null==l?void 0:l.data)||void 0===s?void 0:s.keepReport)&&r.push(l),o?o.append(l):i.push(l),l}return o})),{noParentNodes:i,keepReportNodes:r}},t.prototype.add=function(t){for(var e=t.el,n=[],i=[],r=t.all||[t.el],o=0,a=r.length;o<a;o++){var s=r[o],l=this.createTree(s);n=n.concat(l.noParentNodes),i=i.concat(l.keepReportNodes)}if(0!==n.length){var u=null,c=null;At(e,(function(t){if(e!==t&&(u=Nt.getNode(t)))return!0;for(var n=t.previousElementSibling;n&&(c=Tt(n,(function(t){var e=Nt.getNode(t);if(e)return e})),!c);)n=n.previousElementSibling;return!!c})),!c&&u?(u.insert(0,n),Mt(i)):c&&(u=u||c.parent)&&(u.insert(u.children.indexOf(c)+1,n),Mt(i))}},t.prototype.remove=function(t){for(var e=t.all||[t.el],n=0,i=e.length;n<i;n++){var r=e[n];kt(r,(function(t){var e=Nt.getNode(t);e&&e.destroy()}))}},t.prototype.reflow=function(t){var e,n=this;if(!nt){var i=null,r=this.genHash(),o=1===(i=t instanceof Array?t:[t]).length&&Nt.getNode(i[0]);if(!o){var a={},s=0,l=""+Math.floor(1e4*Math.random());i.forEach((function(t){var e=null==t?void 0:t.el;if(e){var n=l+(s+=1);e.__tmpid=n,a[n]=t}}));for(var u=[this.root.el];u.length>0;){var c=u.shift();if(!c)break;if(o=Nt.getNode(c)||o,a[c.__tmpid]&&o)break;(null===(e=c.children)||void 0===e?void 0:e.length)>0&&(u=[].slice.call(c.children).concat(u))}}o&&(!function(t,e,n,i){for(var r=[],o=t,a=n;o&&(r.push(o),o!==i);)o=o.parent;for(var s=r.length-1;s>=0&&("boolean"!=typeof(a=e(r[s],a))||!a);s--);}(o,(function(t){var e,i=t;if(i.el&&(null===(e=i.el)||void 0===e?void 0:e.__rechash)!==r&&(i.el.__rechash=r,i.recalc(),n.root!==i))for(var o=i.parent?i.parent.children:[],a=o.indexOf(i),s=o.length;a<s;){var l=o[a];if(Ct(l,(function(t){var e,n=t;(null==n?void 0:n.el)&&(null===(e=n.el)||void 0===e?void 0:e.__rechash)!==r&&(n.el.__rechash=r,n.recalc())})))break;a+=1}}),!1,this.root),Ct(o,(function(t){var e=t;o!==e&&e.el&&e.el.__rechash!==r&&(e.el.__rechash=r,e.recalc())})))}},t.prototype.getSiblingReportNode=function(t){for(var e=t,n=null;e&&!(n=Nt.getNode(e));)e=e.nextElementSibling;return n},t.prototype.handleMutations=function(t){for(var e,n=[],i=[],r=[],o=0,a=t;o<a.length;o++){var s=(c=a[o]).target;if("childList"===c.type)e=this.handleChildList(c);else if("attributes"===c.type){if(!s)return;"dt-cmd"===c.attributeName?(r.push(c),e={reflowArr:[],updateArr:[]}):e=this.handleAttributes(c)}else{if(!s)return;1!==s.nodeType&&(s=s.parentNode),e={reflowArr:[{el:s}],updateArr:[]}}n.push.apply(n,e.updateArr),i.push.apply(i,e.reflowArr)}for(var l=0,u=r;l<u.length;l++){var c=u[l];e=this.handleAttributes(c),n.push.apply(n,e.updateArr),i.push.apply(i,e.reflowArr)}return{updateArr:n,reflowArr:i}},t.prototype.handleChildList=function(e){var n,i,r,o=[],a=[],s=e.target,l=gt(e.addedNodes),u=gt(e.removedNodes),c=e.previousSibling;if(l[0]){o.push({el:l[0],action:"add",all:l});var p=null===(n=t.reporter.pluginManager)||void 0===n?void 0:n.visualTracker;p&&this.handleVisualTrackRules(p)}var d=u[0];if(d){if(t.shouldBePageNode(d)){t.reporter.traverseReportHide(d);var h=null===(i=d.getAttribute("dt-pgid"))||void 0===i?void 0:i.trim();null===(r=t.reporter.pageManager)||void 0===r||r.removePage(h),t.reporter.firstPVReportMap[h]=null}o.push({el:d,action:"remove",all:u})}if(c&&1!==c.nodeType&&(c=c.previousElementSibling),c){var f=c.nextElementSibling;f?a.push({el:f}):s&&a.push({el:s,ignoreChild:!0})}else s&&a.push({el:s});return{updateArr:o,reflowArr:a}},t.prototype.handleAttributes=function(e){var n=[],i=[],r=this.genHash(),o=e.target,a=e.attributeName;o.__atmrhash===r||"class"!==a&&"style"!==a||(o.__atmrhash=r,i.push({el:o}));var s=Nt.getNode(o),l=t.shouldBeNode(o);return s&&!l||!s&&l&&(o.__atmuhash!==r&&(o.__atmuhash=r,n.push({el:o,action:"add",all:[o]})),o.__atmrhash!==r&&(o.__atmrhash=r,i.push({el:o}))),s&&s.updateData(o,a,r),{updateArr:n,reflowArr:i}},t.prototype.handleVisualTrackRules=function(t){t.processUnhandledRules()},t.attrs=[],t.vTreeMap=new WeakMap,t}(),Bt=function(t,e,n){void 0===e&&(e=B.EXPOSE),t&&t.length&&t.forEach((function(t){t&&(t.visible&&(n?n(t):t.emit(e)),Bt(t.children,e,n))}))},Lt=function(t){return{top:Math.round(t.top),bottom:Math.round(t.bottom),left:Math.round(t.left),right:Math.round(t.right)}};var Vt=function(){function t(t,e){this.reportNode=Nt,this.reportTree=jt,this.initIntersectionObserver(),this.addConfig(t,e)}return t.prototype.getReportNode=function(t){return t?this.reportNode.getNode(t):null},t.prototype.getClosestParentNode=function(t){for(var e,n=t,i=null;!i&&!(null===(e=null==n?void 0:n.hasAttribute)||void 0===e?void 0:e.call(n,"dt-pgid"))&&(n=n.parentNode);)i=this.getReportNode(n);return i},t.prototype.addConfig=function(t,e){var n,i,r,o=t.attrs,a=t.events,s=this.getAttrConfig(o),l=this.getEventConfig(a);this.reportTree.attrs=Object.keys(o),this.reportTree.treeConfigs={overFlowAttr:null!==(n=t.overflowHide)&&void 0!==n?n:D.OVERFLOWHIDE_ATTR,isScrollerAttr:null!==(i=t.isScroller)&&void 0!==i?i:D.ISSCROLLER_ATTR,lazyAttr:null!==(r=t.lazy)&&void 0!==r?r:D.LAZY_ATTR},this.reportTree.reporter=e,this.reportNode.globalNodeConfig={map:s,events:l,stopBubbleFlag:t.stopBubbleFlag}},t.prototype.initIntersectionObserver=function(){nt&&(this.reportNode.iobserver=new IntersectionObserver((function(t){try{t.forEach((function(t){var e=t.target,n=Nt.getNode(e);if(n){n.rect={top:t.boundingClientRect.top,bottom:t.boundingClientRect.bottom,left:t.boundingClientRect.left,right:t.boundingClientRect.right};var i=void 0!==t.isIntersecting?t.isIntersecting:t.intersectionRatio>0;if(n.visible!==i){if(n.visible=i,n.width=t.boundingClientRect.width,n.height=t.boundingClientRect.height,i){n.effectiveWidth=n.width,n.effectiveHeight=n.height;var r=t.intersectionRect,o=r.width,a=r.height;n.maxIntersectArea=o*a}n.emit(B.VISIBILITYCHANGED,i)}n.recalc()}}))}catch(t){}})))},t.prototype.getAttrConfig=function(t){var e=Object.keys(t),n={};return t&&e.forEach((function(e){var i,r=t[e];"string"==typeof r?i=function(t){var e=this.data[r]!==t;return this.data[r]=t,e}:"object"==typeof r&&(i=function(t){var n,i=r.prop||e,o=r.dataType;if("JSON"===o)try{n=JSON.parse(t)}catch(t){}else n="KV"===o?function(t){var e={};if("string"==typeof t)for(var n=t.split("&"),i=0,r=n.length;i<r;i++){var o=n[i],a=/^([^=]+)=(.*)$/.exec(o);if(a)try{e[a[1]]=decodeURIComponent(a[2])}catch(t){}}return e}(t):t;var a=!1;if("dt-params"===e){var s=this.data.reuseIdentifier;s&&this.data[s]!==n[s]&&this.visible&&(a=!0)}return this.data[i]=n||{},a&&this.emit(B.EXPOSE),!0});var o={name:e,func:i};n[e]=o})),n},t.prototype.getEventConfig=function(t){if(t)return Object.keys(t).map((function(e){var n=t[e],i={name:e,action:n.action,condition:null,keyAttrs:null},r=n.condition;return i.condition=function(t){return r.some((function(e){return t[e]}))},"click"!==e&&(i.keyAttrs=r),n.keyAttrs&&(i.keyAttrs=n.keyAttrs),i}))},t}();function Ut(t,e){var i;jt.reporter=t;var r={click:{condition:["dt-eid"],action:function(){var e,i=xt(this,"pgid")||t.pageManager.fixPgid()||(null===(e=this.data)||void 0===e?void 0:e.keepReportPgId),r=this.data.eid,o=xt(this,"cmd")||{},a="true"===this.data.clckIgnore,s="true"===this.data.sendBeacon,l=t.pageManager.hasPage(i);i&&r&&"true"!==o.hold&&!a&&l&&t.reportElement(this.el,{pgid:i,eventName:S.CLCK,eventTime:Date.now(),businessParams:n({eid:r},this.data.params),isSendBeacon:s})}},expose:{condition:["dt-pgid","dt-eid"],action:function(){var e=xt(this,"pgid")||t.pageManager.fixPgid(),i=xt(this,"cmd")||{};if(e&&"true"!==i.hold)if(Dt(this)){var r="true"===this.data.pgIgnore,o="true"===this.data.pginIgnore;if(r||o)return;var a=t.firstPVReportMap[e];if(t.setFirstPVFlag(e,"reported"),"first_reported"===a)return;t.reportPV(e)}else{var s=this.data.eid,l="true"===this.data.impIgnore||"true"===this.data.impStartIgnore,u="true"===this.data.impOnce,c=t.pageManager.hasPage(e);if(!s||l||u&&this.data.impReported||!c)return;this.data.impReported=!0,t.reportElement(this.el,{pgid:e,eventName:S.IMP,eventTime:Date.now(),businessParams:n({eid:s},this.data.params)})}}},hide:{condition:["dt-pgid","dt-eid"],action:function(){var e=xt(this,"pgid")||t.pageManager.fixPgid(),i=xt(this,"cmd")||{};if(e&&"true"!==i.hold)if(Dt(this)){var r="true"===this.data.pgIgnore,o="true"===this.data.pgoutIgnore;if(r||o)return;t.reportPgOut(e)}else{var a=this.data.eid,s="true"===this.data.impIgnore||"true"===this.data.impEndIgnore,l="true"===this.data.impOnce,u=t.pageManager.hasPage(e);if(!a||s||l&&this.data.impEndReported||!u)return;this.data.impEndReported=!0;var c=this.effectiveWidth*this.effectiveHeight,p=this.isScroller?(this.maxScrollLeft+this.width)*(this.maxScrollTop+this.height):this.maxIntersectArea,d=c?Math.min(p/c,1):0,h=this.el.showTime,f={dt_element_area:c.toFixed(2),dt_ele_imp_area:p.toFixed(2),dt_element_lvtm:h?Date.now()-h:0,dt_ele_imp_rate:d.toFixed(2)};t.reportElement(this.el,{pgid:e,eventName:S.IMPEND,eventTime:Date.now(),publicParams:n({},f),businessParams:n(n({eid:a},ft(f)),this.data.params)})}}}},o=new Vt({isScroller:"dt-scroll",overflowHide:"dt-overflow-hide",lazy:"dt-lazy",stopBubbleFlag:e,attrs:(i={},i["dt-pgid"]="pgid",i["dt-eid"]="eid",i["dt-params"]={prop:"params",dataType:"KV"},i["dt-cmd"]={prop:"cmd",dataType:"KV"},i["dt-pg-ignore"]="pgIgnore",i["dt-pgin-ignore"]="pginIgnore",i["dt-pgout-ignore"]="pgoutIgnore",i["dt-imp-ignore"]="impIgnore",i["dt-imp-end-ignore"]="impEndIgnore",i["dt-imp-start-ignore"]="impStartIgnore",i["dt-clck-ignore"]="clckIgnore",i["dt-send-beacon"]="sendBeacon",i["dt-imp-once"]="impOnce",i["dt-keep-report"]="keepReport",i["dt-reuse-identifier"]="reuseIdentifier",i),events:r},t);Object.assign(t,{eventsConfig:r,observer:o}),function(t){var e=t.observer,n=t.pluginManager,i=null==n?void 0:n.isEnablePlugin;if(e){var r=function(t){l?t():s.push(t)},o=e.reportNode.prototype,a=o.updateData;if(o.updateData=function(e,n,o){var s,l,u,c=this,p=a.call(this,e,n,o),d=(null===(s=this.data)||void 0===s?void 0:s.pgid)||"",h=null===(u=null===(l=this.data)||void 0===l?void 0:l.cmd)||void 0===u?void 0:u.hold;return"dt-pgid"===n&&this.oldPgid&&d!==this.oldPgid?t.emit("pageIdChange",e,this.oldPgid):"dt-params"===n&&d?t.emit("pageParamsChange",e,d):"dt-cmd"===n&&"false"===h&&h!==this.oldHold&&Bt([this],B.EXPOSE,null),this.oldPgid=d,this.oldHold=h||"false",i&&r((function(){var e=c.emitter,n=e.events.click,i=e.events.expose;t.emit("mutation",{action:"updateData",node:c,data:{data:c.data,exp:i?1:void 0,cli:n?1:void 0,of:c.overflowHide?void 0:1}})})),p},i){var s=[],l=!1;t.on("viewPluginReady",(function(){l=!0;for(var t=null;t=s.shift();)t()}));var u=o.append;o.append=function(){for(var e=this,n=[],i=0;i<arguments.length;i++)n[i]=arguments[i];var o=u.apply(this,n);return r((function(){t.emit("mutation",{action:"append",node:e})})),o};var c=o.insert;o.insert=function(){for(var e=this,n=[],i=0;i<arguments.length;i++)n[i]=arguments[i];var o=c.apply(this,n),a=n[1]||[];return r((function(){t.emit("mutation",{action:"insert",node:e,data:a})})),o};var p=o.calcViewPort;o.calcViewPort=function(){for(var e=this,n=[],i=0;i<arguments.length;i++)n[i]=arguments[i];var o=p.apply(this,n);return r((function(){t.emit("mutation",{action:"calcViewPort",node:e,data:{viewport:Lt(e.viewport)}})})),o};var d=o.recalc;o.recalc=function(){for(var e=this,n=[],i=0;i<arguments.length;i++)n[i]=arguments[i];var o=d.apply(this,n);return r((function(){t.emit("mutation",{action:"recalc",node:e,data:{viewport:Lt(e.viewport)}})})),o};var h=o.move;o.move=function(){for(var e=this,n=[],i=0;i<arguments.length;i++)n[i]=arguments[i];var o=h.apply(this,n);return r((function(){t.emit("mutation",{action:"move",node:e,data:{viewport:Lt(e.viewport),rect:Lt(e.rect)}})})),o};var f=o.remove;o.remove=function(){for(var e=this,n=[],i=0;i<arguments.length;i++)n[i]=arguments[i];return r((function(){t.emit("mutation",{action:"remove",node:e})})),f.apply(this,n)};var v=o.clear;o.clear=function(){for(var e=this,n=[],i=0;i<arguments.length;i++)n[i]=arguments[i];return r((function(){t.emit("mutation",{action:"clear",node:e})})),v.apply(this,n)}}}}(t)}var qt,Ht=function(){function t(){this.pageMap={}}return t.prototype.hasPage=function(t){return t&&!!this.pageMap[t]},t.prototype.addPage=function(t){var e,i=null===(e=null==t?void 0:t.pageParams)||void 0===e?void 0:e.pgid,r=ut(null==t?void 0:t.pageRoot);if(i&&r){var o=R.parse(at("ref_pg")||""),a=R.parse(at("ref_ele")||"");this.pageMap[i]=n(n({},t),{pageParams:n({ref_pg:o.pgid?o:{},ref_ele:a.eid?a:{}},t.pageParams),pageRoot:r})}else;},t.prototype.getPage=function(t){return this.hasPage(t)?this.pageMap[t]:null},t.prototype.setPage=function(t){var e,i=null===(e=null==t?void 0:t.pageParams)||void 0===e?void 0:e.pgid,r=this.pageMap[i];this.hasPage(i)&&(this.pageMap[i]=n(n(n({},r),t),{pageParams:n(n({},r.pageParams),t.pageParams)}))},t.prototype.removePage=function(t){this.hasPage(t)&&delete this.pageMap[t]},t.prototype.updatePage=function(t){var e,n;if(this.hasPage(t)){var i=Number(null!==(n=null===(e=this.getPage(t).pageParams)||void 0===e?void 0:e.pg_stp)&&void 0!==n?n:0)+1;this.setPage({pageParams:{pgid:t,pg_stp:i},showTime:Date.now(),clckFlag:"0"})}},t.prototype.fixPgid=function(t){if(t)return t;var e=Object.keys(this.pageMap);return 1===e.length?e[0]:""},t}(),Wt="function",Ft="object",Jt="model",Gt="name",Qt="type",Kt="vendor",zt="version",Xt="console",Yt="phone",$t="tablet",Zt="tv",te=function(t,e){return"string"==typeof t&&-1!==e.toLowerCase().indexOf(t.toLowerCase())},ee={rgx:function(t,e){for(var n,i,r,o,a,s,l=0;l<e.length&&!a;){var u=e[l],c=e[l+1];for(n=i=0;n<u.length&&!a;)if(a=u[n++].exec(t))for(r=0;r<c.length;r++)s=a[++i],typeof(o=c[r])===Ft&&o.length>0?2==o.length?typeof o[1]==Wt?this[o[0]]=o[1].call(this,s):this[o[0]]=o[1]:3==o.length?typeof o[1]!==Wt||o[1].exec&&o[1].test?this[o[0]]=s?s.replace(o[1],o[2]):void 0:this[o[0]]=s?o[1].call(this,s,o[2]):void 0:4==o.length&&(this[o[0]]=s?o[3].call(this,s.replace(o[1],o[2])):void 0):this[o]=s||void 0;l+=2}},str:function(t,e){for(var n in e)if(typeof e[n]===Ft&&e[n].length>0){for(var i=0;i<e[n].length;i++)if(te(e[n][i],t))return"?"===n?void 0:n}else if(te(e[n],t))return"?"===n?void 0:n;return t}},ne={os:{windows:{version:{ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"}}}},ie={device:[[/\((ipad|playbook);[\w\s\),;-]+(rim|apple)/i],[Jt,Kt,[Qt,$t]],[/applecoremedia\/[\w\.]+ \((ipad)/],[Jt,[Kt,"Apple"],[Qt,$t]],[/(apple\s{0,1}tv)/i],[[Jt,"Apple TV"],[Kt,"Apple"],[Qt,Zt]],[/(archos)\s(gamepad2?)/i,/(hp).+(touchpad)/i,/(hp).+(tablet)/i,/(kindle)\/([\w\.]+)/i,/\s(nook)[\w\s]+build\/(\w+)/i,/(dell)\s(strea[kpr\s\d]*[\dko])/i],[Kt,Jt,[Qt,$t]],[/(kf[A-z]+)(\sbuild\/|\)).+silk\//i],[Jt,[Kt,"Amazon"],[Qt,$t]],[/android.+aft([bms])\sbuild/i],[Jt,[Kt,"Amazon"],[Qt,Zt]],[/\((ip[honed|\s\w*]+);.+(apple)/i],[Jt,Kt,[Qt,Yt]],[/\((ip[honed|\s\w*]+);/i],[Jt,[Kt,"Apple"],[Qt,Yt]],[/(blackberry)[\s-]?(\w+)/i,/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[\s_-]?([\w-]*)/i,/(hp)\s([\w\s]+\w)/i,/(asus)-?(\w+)/i],[Kt,Jt,[Qt,Yt]],[/\(bb10;\s(\w+)/i],[Jt,[Kt,"BlackBerry"],[Qt,Yt]],[/android.+(transfo[prime\s]{4,10}\s\w+|eeepc|slider\s\w+|nexus 7|padfone|p00c)/i],[Jt,[Kt,"Asus"],[Qt,$t]],[/(sony)\s(tablet\s[ps])\sbuild\//i,/(sony)?(?:sgp.+)\sbuild\//i],[[Kt,"Sony"],[Jt,"Xperia Tablet"],[Qt,$t]],[/android.+\s([c-g]\d{4}|so[-l]\w+)(?=\sbuild\/|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[Jt,[Kt,"Sony"],[Qt,Yt]],[/\s(ouya)\s/i,/(nintendo)\s([wids3u]+)/i],[Kt,Jt,[Qt,Xt]],[/android.+;\s(shield)\sbuild/i],[Jt,[Kt,"Nvidia"],[Qt,Xt]],[/(playstation\s[34portablevi]+)/i],[Jt,[Kt,"Sony"],[Qt,Xt]],[/(htc)[;_\s-]{1,2}([\w\s]+(?=\)|\sbuild)|\w+)/i,/(zte)-(\w*)/i,/(alcatel|geeksphone|nexian|panasonic|(?=;\s)sony)[_\s-]?([\w-]*)/i],[Kt,[Jt,/_/g," "],[Qt,Yt]],[/(nexus\s9)/i],[Jt,[Kt,"HTC"],[Qt,$t]],[/d\/huawei([\w\s-]+)[;\)]/i,/android.+\s(nexus\s6p|vog-[at]?l\d\d|ane-[at]?l[x\d]\d|eml-a?l\d\da?|lya-[at]?l\d[\dc]|clt-a?l\d\di?)/i],[Jt,[Kt,"Huawei"],[Qt,Yt]],[/android.+(bah2?-a?[lw]\d{2})/i],[Jt,[Kt,"Huawei"],[Qt,$t]],[/(microsoft);\s(lumia[\s\w]+)/i],[Kt,Jt,[Qt,Yt]],[/[\s\(;](xbox(?:\sone)?)[\s\);]/i],[Jt,[Kt,"Microsoft"],[Qt,Xt]],[/(kin\.[onetw]{3})/i],[[Jt,/\./g," "],[Kt,"Microsoft"],[Qt,Yt]],[/android.+\s(mz60\d|xoom[\s2]{0,2})\sbuild\//i],[Jt,[Kt,"Motorola"],[Qt,$t]],[/hbbtv.+maple;(\d+)/i],[[Jt,/^/,"SmartTV"],[Kt,"Samsung"],[Qt,Zt]],[/\(dtv[\);].+(aquos)/i],[Jt,[Kt,"Sharp"],[Qt,Zt]],[/android.+((sch-i[89]0\d|shw-m380s|SM-P605|SM-P610|gt-p\d{4}|gt-n\d+|sgh-t8[56]9|nexus 10))/i,/((SM-T\w+))/i],[[Kt,"Samsung"],Jt,[Qt,$t]],[/smart-tv.+(samsung)/i],[Kt,[Qt,Zt],Jt],[/((s[cgp]h-\w+|gt-\w+|galaxy\snexus|sm-\w[\w\d]+))/i,/(sam[sung]*)[\s-]*(\w+-?[\w-]*)/i,/sec-((sgh\w+))/i],[[Kt,"Samsung"],Jt,[Qt,Yt]],[/sie-(\w*)/i],[Jt,[Kt,"Siemens"],[Qt,Yt]],[/(maemo|nokia).*(n900|lumia\s\d+)/i,/(nokia)[\s_-]?([\w-]*)/i],[[Kt,"Nokia"],Jt,[Qt,Yt]],[/android[x\d\.\s;]+\s([ab][1-7]\-?[0178a]\d\d?)/i],[Jt,[Kt,"Acer"],[Qt,$t]],[/android.+([vl]k\-?\d{3})\s+build/i],[Jt,[Kt,"LG"],[Qt,$t]],[/android\s3\.[\s\w;-]{10}(lg?)-([06cv9]{3,4})/i],[[Kt,"LG"],Jt,[Qt,$t]],[/linux;\snetcast.+smarttv/i,/lg\snetcast\.tv-201\d/i],[[Kt,"LG"],Jt,[Qt,Zt]],[/(nexus\s[45])/i,/lg[e;\s\/-]+(\w*)/i,/android.+lg(\-?[\d\w]+)\s+build/i],[Jt,[Kt,"LG"],[Qt,Yt]],[/(lenovo)\s?(s(?:5000|6000)(?:[\w-]+)|tab(?:[\s\w]+))/i],[Kt,Jt,[Qt,$t]],[/android.+(ideatab[a-z0-9\-\s]+)/i],[Jt,[Kt,"Lenovo"],[Qt,$t]],[/(lenovo)[_\s-]?([\w-]+)/i],[Kt,Jt,[Qt,Yt]],[/android.+;\s(oppo)\s?([\w\s]+)\sbuild/i],[Kt,Jt,[Qt,Yt]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[Jt,[Kt,"Vivo"],[Qt,Yt]],[/(HM\sNOTE)/i,/(HM\s\w+)/i,/(MI\s\w+)/i,/(MI-ONE\sPlus)/i,/(M1)\sBuild/i,/(HM\d+)/i,/Xiaomi[\s_]?([\w_]+)/i],[[Jt,/_/g," "],[Kt,"Xiaomi"],[Qt,Yt]],[/android.+(mi[\s\-_]?(?:pad)(?:[\s_]?[\w\s]+))(?:\sbuild|\))/i],[[Jt,/_/g," "],[Kt,"Xiaomi"],[Qt,$t]],[/android.+;\s(m[1-5]\snote)\sbuild/i],[Jt,[Kt,"Meizu"],[Qt,Yt]],[/(mz)-([\w-]{2,})/i],[[Kt,"Meizu"],Jt,[Qt,Yt]],[/android.+a000(1)\s+build/i,/android.+oneplus\s(a\d{4})[\s)]/i],[Jt,[Kt,"OnePlus"],[Qt,Yt]],[/android .+?; ([^;]+?)(?: build|\) applewebkit).+? mobile safari/i],[Jt,[Qt,Yt]],[/android .+?;\s([^;]+?)(?: build|\) applewebkit).+?(?! mobile) safari/i],[Jt,[Qt,$t]],[/\s(mobile)(?:[;\/]|\ssafari)/i],[[Qt,Yt],Kt,Jt],[/\s(tablet|tab)[;\/]/i],[[Qt,$t],Kt,Jt],[/[\s\/\(](smart-?tv)[;\)]/i],[[Qt,Zt]],[/(android[\w\.\s\-]{0,9});.+build/i],[Jt,[Kt,"Generic"]]],engine:[[/windows.+\sedge\/([\w\.]+)/i],[zt,[Gt,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[zt,[Gt,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/(khtml|tasman|links)[\/\s]\(?([\w\.]+)/i,/(icab)[\/\s]([23]\.[\d\.]+)/i],[Gt,zt],[/rv\:([\w\.]{1,9}).+(gecko)/i],[zt,Gt]],os:[[/microsoft\s(windows)\s(vista|xp)/i],[Gt,zt],[/(windows)\snt\s6\.2;\s(arm)/i,/(windows\sphone(?:\sos)*)[\s\/]?([\d\.\s\w]*)/i,/(windows\smobile|windows)[\s\/]?([ntce\d\.\s]+\w)/i],[Gt,[zt,ee.str,ne.os.windows.version]],[/(win(?=3|9|n)|win\s9x\s)([nt\d\.]+)/i],[[Gt,$],[zt,ee.str,ne.os.windows.version]],[/(android)[\/\s-]?([\w\.]*)/i],[Gt,zt],[/cfnetwork\/.+darwin/i,/ip[honead]{2,4}(?:.*os\s([\w]+)\slike\smac|;\sopera)/i],[[zt,/_/g,"."],[Gt,z]],[/(mac\sos\sx)\s?([\w\s\.]*)/i,/(macintosh|mac(?=_powerpc)\s)/i],[[Gt,Y],[zt,/_/g,"."]]]},re=function(){function t(t,e){var n,i;this.ua=t||(null!==(i=null===(n=null===window||void 0===window?void 0:window.navigator)||void 0===n?void 0:n.userAgent)&&void 0!==i?i:""),this.rgxmap=ie}return t.prototype.getDevice=function(){var t={vendor:void 0,model:void 0,type:void 0};return ee.rgx.call(t,this.ua,this.rgxmap.device),t},t.prototype.getEngine=function(){var t={name:void 0,version:void 0};return ee.rgx.call(t,this.ua,this.rgxmap.engine),t},t.prototype.getOS=function(){var t={name:void 0,version:void 0};return ee.rgx.call(t,this.ua,this.rgxmap.os),t},t.prototype.getResult=function(){return{engine:this.getEngine(),os:this.getOS(),device:this.getDevice()}},t}(),oe=function(){if(!document.body)return 0;var t=document.createElement("div");t.style.height="1in",t.style.width="1in",t.style.top="-100%",t.style.left="-100%",t.style.position="absolute",document.body.appendChild(t);var e=t.offsetHeight;return document.body.removeChild(t),e},ae=function(t){for(var e={},n=document.cookie.split(";"),i=0,r=n.length;i<r;i++){var o=n[i].replace(/^\s+/,"").split("="),a=o[0],s=o[1];e[a]=s}return t?e[t]:e},se=["dt_omgid"],le=function(){function t(t){void 0===t&&(t={}),this.clientInfo=new re(navigator.userAgent).getResult(),this.init();var e=function(t){void 0===t&&(t={});var e=n({qq:et.QQ,wx:et.WX,qqvideo:et.QQ_VIDEO,qqnews:et.QQ_NEWS,qqreading:et.QQ_READING,qqbrowser:et.QQ_BROWSER,qzone:et.Q_ZONE,kuaibao:et.KUAIBAO,weibo:et.WEIBO},t),i="",r="";return{scenePlat:Object.keys(e).some((function(t){i=t;var n=navigator.userAgent.match(e[t]);return n&&2===n.length&&(r=n[1]),n}))?i:"",sceneVersion:r}}(t.scenePlat);this.set(n(n({},t.publicParams),{web_version:t.version||"",scenes_plat:e.scenePlat,scenes_version:e.sceneVersion}),"publicParams").set(t.accountInfo||V,"accountInfo")}return t.prototype.init=function(){var t,e,n,i,r,o,a=this.clientInfo,s=a.os,l=a.device,u=a.engine;this.publicParams={platform:(o=this.clientInfo,void 0===o.device.type?"pc":o.device.type),hardware_os:(null===(t=s.name)||void 0===t?void 0:t.toLowerCase())||"",os_version:s.version||"",target_sdk:parseInt(s.version,10)||0,resolution:window.screen.width*window.devicePixelRatio,dpi:oe(),brand:(null===(e=l.vendor)||void 0===e?void 0:e.toLowerCase())||"",mf:(null===(n=l.vendor)||void 0===n?void 0:n.toLowerCase())||"",model:(null===(i=l.model)||void 0===i?void 0:i.toLowerCase())||"",web_version:"",web_core_type:(null===(r=u.name)||void 0===r?void 0:r.toLowerCase())||"",web_core_version:u.version||"",web_channel:at("web_channel")||"",scenes_plat:"",network_type:navigator.connection?navigator.connection.effectiveType:"unknown",dt_sdkversion_h5:"3.6.10",dt_omgid:ae("video_omgid")||"",dt_omgbzid:"",dt_sessionid_h5:(new Date).getTime()+Math.random().toString().slice(2,5)}},t.prototype.get=function(t){return t?this.publicParams[t]:this.publicParams},t.prototype.set=function(e,n){var i=this;void 0===e&&(e={}),void 0===n&&(n="publicParams");var r=t.checkRules[n];if(r)return Object.keys(e).filter((function(t){return r(t)})).forEach((function(t){i.publicParams[t]=e[t]})),this},t.prototype.remove=function(e){var n=this;void 0===e&&(e="");var i=t.checkRules.publicParams;(Array.isArray(e)?e:[e]).forEach((function(t){i(t,'[universalReport] "'+t+'" cannot be deleted')&&delete n.publicParams[t]}))},t.prototype.getPublicPageParams=function(t){var e,n,i,r;return{url:window.location.href,ref_url:document.referrer,dt_pgid:null!==(e=null==t?void 0:t.pgid)&&void 0!==e?e:"",dt_ref_pgid:null!==(i=null===(n=null==t?void 0:t.ref_pg)||void 0===n?void 0:n.pgid)&&void 0!==i?i:"",dt_pgstp:null!==(r=null==t?void 0:t.pg_stp)&&void 0!==r?r:1}},t.checkRules=((qt={}).publicParams=function(t,e){var n=-1!==String(t).indexOf("dt_")&&se.indexOf(t)<0;return!n},qt.accountInfo=function(t){return Object.keys(V).includes(t)},qt),t}(),ue=function(){function t(){this.privateParams={}}return t.prototype.set=function(t,e,n){return void 0===e&&(e=""),void 0===n&&(n=[]),t&&n.length?(this.privateParams[t]={value:e,events:n},this):this},t.prototype.get=function(t){return t?this.privateParams[t]:this.privateParams},t.prototype.remove=function(t){t&&delete this.privateParams[t]},t.prototype.getEventPrivateParams=function(t){return Object.entries(this.privateParams).filter((function(e){return e[1].events.includes(t)})).reduce((function(t,e){var n;return Object.assign(t,((n={})[e[0]]=e[1].value,n))}),{})},t}(),ce=function(){function t(t,e){this.options=t,this.reporter=e,this.options.isDisableDtPlugin||(this.initVisualDebugPlugin(),this.initTrackPlugin())}return t.prototype.initVisualDebugPlugin=function(){var t=at(x.REALTIME_DEBUG);if(at(x.REPORT_VIEW)===N.ENABLE_FEATURE)return this.loadDtPlugin(),this.setPluginTokenByParam(),this;var e=ae(x.REALTIME_DEBUG_ID),n=ae(x.REALTIME_APP_ID);return t===N.ENABLE_FEATURE||e&&n?(e=at(x.REALTIME_DEBUG_ID)||e,n=at(x.REALTIME_APP_ID)||n,this.dtDebugId=e,this.dtAppId=n,this.setRealTimeCookie(x.REALTIME_DEBUG_ID,e),this.setRealTimeCookie(x.REALTIME_APP_ID,n),this.isRealTimeDebug=!0,this.loadDtPlugin(),this):(this.loadDtPluginByToken(),this)},t.prototype.setRealTimeCookie=function(t,e,n,i){!function(t,e,n,i,r){var o="";o="string"==typeof n?"; expires="+n:n instanceof Date?"; expires="+n.toUTCString():"; expires="+new Date(Date.now()+6048e5).toUTCString();var a=i||location.hostname,s=r||"/";document.cookie=[t,"=",escape(e),o,";domain=",a,";path=",s].join("")}(t,e,null!=n?n:""+new Date(Date.now()+18e5).toUTCString(),(null!=i?i:location.hostname.indexOf(Q)>-1)?Q:location.hostname)},t.prototype.loadDtPlugin=function(){var t,e=this;this.isEnablePlugin=!0;var n=this.options.beacon;"string"==typeof n?this.beaconAppkey=n:"object"==typeof n&&(this.beaconAppkey=null===(t=null==n?void 0:n.baseInfo)||void 0===t?void 0:t.appkey),ct("https://staticfile.qq.com/datong/universalReportH5-plugin-view\n/latest/universal-report-plugin-view.min.js").then((function(){e.use(window.universalReportPluginView,e.isRealTimeDebug)})).catch((function(t){}))},t.prototype.loadDtPluginByToken=function(){if(window.localStorage){var t=localStorage.getItem(x.REPORT_VIEW_PLUGIN_TOKEN);if(t)Date.now()<parseInt(t,10)?this.loadDtPlugin():localStorage.removeItem(x.REPORT_VIEW_PLUGIN_TOKEN)}},t.prototype.setPluginTokenByParam=function(){if(window.localStorage){var t=Date.now()+6048e5;at(x.REPORT_VIEW_SET_TOKEN)===N.ENABLE_FEATURE&&localStorage.setItem(x.REPORT_VIEW_PLUGIN_TOKEN,""+t)}},t.prototype.initTrackPlugin=function(){var t=this;return this.options.enableVisualTrack&&ct("https://staticfile.qq.com/datong/universalReportH5\n-visual-track-plugin/latest/index.min.js").then((function(){t.visualTracker=t.use(window.UniversalReportTrackPlugin.default,t.options.visualTrackOptions,t.isVisualTrackDebug),t.reporter.initPage()})),at(x.VISUAL_TRACK_PLUGIN)===N.ENABLE_FEATURE&&(this.isVisualTrackDebug=!1,this.loadVisualTrackPlugin()),this},t.prototype.loadVisualTrackPlugin=function(){var t=this;ct("https://staticfile.qq.com/datong/dt-messager\n/latest/index.min.js").then((function(){var e=new window.DTMessager({allowOrigins:[rt,it],debug:!0,appKey:t.beaconAppkey});e.initChildMessager(e),e.setSameSiteNone()})).catch((function(t){}))},t.prototype.use=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var n=t[0],i=t.slice(1);return n.apply(void 0,s([this.reporter],i))},t}();var pe,de,he=new Promise((function(t){"undefined"!=typeof document&&"loading"===document.readyState?document.addEventListener("DOMContentLoaded",(function(){t()})):t()})),fe=(pe=[],de=!1,he.then((function(){var t;for(de=!0;t=pe.shift();)null==t||t()})),function(t,e,i){return n(n({},i),{value:function(){for(var t=this,e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];var r=i.value;return de?r.apply(this,e):(pe.push((function(){r.apply(t,e)})),this)}})}),ve=function(t){function o(e){var n,i=t.call(this)||this;return i.options=e,i.deviceId="",i.monitoringReportId="",i.monitoringReport=!1,i.monitoringJsApiReport=!1,i.enableBeaconReportRetry=!1,i.attaPublicParams={},i.firstPVReportMap={},i.eventsConfig={},i.isCrawler=!1,i.eventId=0,i.monitoringReportId=e.monitoringReportId,i.monitoringReport=e.monitoringReport,i.monitoringJsApiReport=e.monitoringJsApiReport,i.init(),i.options.enableCrawlerFilter&&(i.isCrawler=(n=navigator.userAgent,/(googlebot|bingbot|yandex|twitterbot|facebookexternalhit|rogerbot|linkedinbot|embedly|quora link preview|showyoubot|outbrain|pinterest\/0\.|pinterestbot|slackbot|vkShare|W3C_Validator|whatsapp|petalbot|applebot|mpcrawler|spider)/i.test(n))),i.checkBrowserEnvironment()?(i.setEventHandler(),i.initPlugin().initObserver(),i.options.isDisableInitPage||i.initPage(),i.onPageAttrChange().onChannelReport().addPageVisibilityChangeListener(),i.monitoringReport&&i.bindSdkMonitoringEvent(),i.monitoringJsApiReport&&i.bindJsApiMonitoringEvent(),i):i}return e(o,t),o.prototype.init=function(){this.publicParams=new le(this.options),this.attaPublicParams=this.getAttaReportPublicParams(),this.privateParams=new ue,this.pageManager=new Ht;var t=[this.publicParams.get("scenes_plat"),this.publicParams.get("scenes_version"),this.publicParams.get("hardware_os")];return this.channel=new(Et.bind.apply(Et,s([void 0,this.options],t))),this.extraChannel=this.options.extraDefaultChannel&&this.getExtraChannel(t),this},o.prototype.getExtraChannel=function(t){return"function"==typeof this.options.channel||"object"==typeof this.options.channel?new(Et.bind.apply(Et,s([void 0,Object.assign(this.options,{channel:this.options.extraDefaultChannel})],t))):null},o.prototype.bindSdkMonitoringEvent=function(){var t=this;return this.channel&&this.channel.on("monitoringReport",(function(e){var r=e.publicParams,o=i(e,["publicParams"]);St(n(n(n({step:6},t.attaPublicParams),o),{is_return:null==r?void 0:r.dt_pg_isreturn,eid:null==r?void 0:r.dt_eid,event_id:null==r?void 0:r.dt_eventid_h5,omgid:null==r?void 0:r.dt_omgid}))})),this},o.prototype.bindJsApiMonitoringEvent=function(){var t=this;this.channel.on("jsApiReport",(function(e){St(n(n(n({},t.attaPublicParams),e),{appkey:t.options.beacon}),Rt)}))},o.prototype.checkBrowserEnvironment=function(){return!!navigator.cookieEnabled},o.prototype.setFirstPVFlag=function(t,e){this.firstPVReportMap[t]=e},o.prototype.initPlugin=function(){return this.pluginManager=new ce(this.options,this),this},o.prototype.processVisualOption=function(t){var e,n;null===(n=null===(e=this.pluginManager)||void 0===e?void 0:e.visualTracker)||void 0===n||n.processVisualOptions(t)},o.prototype.initObserver=function(){return this.options.isDisableObserver||this.use(Ut,this.options.isDisableBubble),this},o.prototype.initPage=function(t){var e=this;this.options.enableVisualTrack&&this.processVisualOption(this.options.visualTrackOptions);var n=t?[t]:document.querySelectorAll("[dt-pgid]");return n.length?([].slice.call(n).forEach((function(t){var n,i=null===(n=t.getAttribute("dt-pgid"))||void 0===n?void 0:n.trim();if(i&&!e.pageManager.hasPage(i)){var r=e.getDomPageParams(i,t);e.setPage(r,t),e.observe(i)}})),this):this},o.prototype.observe=function(t){var e,n,i=this.pageManager.fixPgid(t),r=null===(e=this.pageManager.getPage(i))||void 0===e?void 0:e.pageRoot;return i&&r?(r.getAttribute("dt-pgid")!==i&&r.setAttribute("dt-pgid",i),null===(n=this.observer)||void 0===n||n.reportTree.attachDom(r),this.options.isDisableInitPage||this.firstPVReportMap[i]||"true"===r.getAttribute("dt-pg-ignore")||"true"===r.getAttribute("dt-pgin-ignore")||"hold=true"===r.getAttribute("dt-cmd")||(this.setFirstPVFlag(i,"first_reported"),this.reportPV(i)),this):this},o.prototype.unobserve=function(t){var e,n,i,r=null===(e=this.pageManager)||void 0===e?void 0:e.fixPgid(t),o=null===(i=null===(n=this.pageManager)||void 0===n?void 0:n.getPage(r))||void 0===i?void 0:i.pageRoot;return r&&o?(this.options.enableSPAMode||o.removeAttribute("dt-pgid"),this.pageManager.removePage(r),null==this||this.observer.reportTree.detachDom(o),this.firstPVReportMap[r]=null,this):this},o.prototype.getDomPageParams=function(t,e){var i,r,o,a=R.parse(null!==(i=e.getAttribute("dt-params"))&&void 0!==i?i:"");if(a.ref_pg){var s=R.parse(a.ref_pg);a.ref_pg=s.pgid?s:{}}if(a.ref_ele){var l=R.parse(a.ref_ele);a.ref_ele=l.eid?l:{}}var u=null!==(o=null===(r=this.options.contentidSet)||void 0===r?void 0:r[t])&&void 0!==o?o:"";return u&&(a.contentid=u),n({pgid:t},a)},o.prototype.onPageAttrChange=function(){var t=this;return this.on("pageIdChange",(function(e,n){var i;t.pageManager.removePage(n),null===(i=t.observer)||void 0===i||i.reportTree.detachDom(e),t.initPage(e)})),this.on("pageParamsChange",(function(e,i){var r,o=R.parse(null!==(r=e.getAttribute("dt-params"))&&void 0!==r?r:""),a=o.ref_pg,s=o.ref_ele;a&&(o.ref_pg=R.parse(a)),s&&(o.ref_ele=R.parse(s)),t.setPageParams(n({pgid:i},o))})),this},o.prototype.setEventHandler=function(){var t=this;this.pageVisibilityHandler=function(){"hidden"===document.visibilityState&&t.reportAllVisiblePage(S.PGOUT),"visible"===document.visibilityState&&t.reportAllVisiblePage(S.PGIN)}},o.prototype.addPageVisibilityChangeListener=function(){return!this.options.isDisableVisibilitychangeListener&&document.addEventListener("visibilitychange",this.pageVisibilityHandler),this},o.prototype.removePageVisibilityChangeListener=function(){return document.removeEventListener("visibilitychange",this.pageVisibilityHandler),this},o.prototype.onChannelReport=function(){var t=this;return this.channel.on("report",(function(e){t.emit("report",{type:"dt",event:e.eventName,data:dt(e),uid:e.uid,eventTime:e.eventTime})})),this},o.prototype.setPage=function(t,e){var i,r=null==t?void 0:t.pgid;if(this.pageManager.hasPage(r))return this;var o=null===(i=this.options.contentidSet)||void 0===i?void 0:i[r];return this.pageManager.addPage({pageParams:n(n({},o?{contentid:o}:null),t),pageRoot:e}),this},o.prototype.reportPV=function(t){var e,n,i,r,o=this.pageManager.fixPgid(t),a=null===(e=this.pageManager.getPage(o))||void 0===e?void 0:e.pageRoot,s=null===(n=this.observer)||void 0===n?void 0:n.getReportNode(a),l=null===(i=null==s?void 0:s.data)||void 0===i?void 0:i.pgIgnore,u=null===(r=null==s?void 0:s.data)||void 0===r?void 0:r.pginIgnore,c=null==s?void 0:s.uid;if(!o||!s||l||u)return this;s.updateNode(),this.pageManager.updatePage(o);var p=this.pageManager.getPage(o).pageParams,d=p.contentid,h=p.last_contentid;if(d){var f=0;h===d?f=1:this.pageManager.setPage({pageParams:{pgid:o,last_contentid:d}}),this.privateParams.set("dt_pg_isreturn",f,[S.PGIN])}else this.privateParams.remove("dt_pg_isreturn");return this.reportEvent({pgid:o,uid:c,eventName:S.PGIN,eventTime:Date.now()}),this},o.prototype.reportAllVisiblePage=function(t){var e=this,n=this.pageManager.pageMap;return Object.keys(n).forEach((function(i){var r,o=n[i].pageRoot,a=null===(r=e.observer)||void 0===r?void 0:r.getReportNode(o);(null==a?void 0:a.visible)&&(t===S.PGIN&&e.reportPV(i),t===S.PGOUT&&e.reportPgOut(i))})),this},o.prototype.reportPgOut=function(t,e,n){var i,r,o,a,s,l;void 0===n&&(n={});var u=this.pageManager.fixPgid(t),c=this.pageManager.getPage(u),p=null===(i=this.observer)||void 0===i?void 0:i.getReportNode(null==c?void 0:c.pageRoot),d=null===(r=null==p?void 0:p.data)||void 0===r?void 0:r.pgIgnore,h=null===(o=null==p?void 0:p.data)||void 0===o?void 0:o.pgoutIgnore,f=null==p?void 0:p.uid;if(!u||!c||!p||d||h)return this;var v=null!==(a=null==n?void 0:n.area)&&void 0!==a?a:p.effectiveWidth*p.effectiveHeight,g=null!==(s=null==n?void 0:n.impArea)&&void 0!==s?s:p.maxIntersectArea,m=v?Math.min(g/v,1):0,y={dt_pg_area:v.toFixed(2),dt_pg_imp_area:g.toFixed(2),dt_lvtm:c.showTime?Date.now()-c.showTime:0,dt_clck_flag:null!==(l=c.clckFlag)&&void 0!==l?l:"0",dt_pg_imp_rate:m.toFixed(2)};return this.reportEvent({pgid:u,uid:f,eventName:S.PGOUT,eventTime:Date.now(),publicParams:y,pageParams:ft(y),isSendBeacon:e}),this},o.prototype.traverseReport=function(t,e){var n,i=this,r=ut(t),o=null===(n=this.observer)||void 0===n?void 0:n.getReportNode(r);o&&Bt([o],e,(function(t){var n;null===(n=null==i?void 0:i.eventsConfig[e])||void 0===n||n.action.apply(t)}))},o.prototype.traverseReportHide=function(t){this.traverseReport(t,B.HIDE)},o.prototype.traverseReportExpose=function(t){this.traverseReport(t,B.EXPOSE)},o.prototype.reportElement=function(t,e){var i,r,o,a=ut(t),s=null===(i=null==e?void 0:e.businessParams)||void 0===i?void 0:i.eid;if(!s||!a)return this;var l=this.pageManager.fixPgid(null==e?void 0:e.pgid),u=null==e?void 0:e.eventName,c=this.pageManager.hasPage(l),p=[S.IMP,S.IMPEND,S.CLCK].includes(u),d={};if(!c||!p)return this;var h=null===(r=this.observer)||void 0===r?void 0:r.getClosestParentNode(a),f=ht(h,this.options.formatMode,n({},e.businessParams));u===S.CLCK?this.pageManager.getPage(l).clckFlag="1":u===S.IMP?a.showTime=Date.now():d=function(t){if(!t)return{};var e=t.scrollWidth*t.scrollHeight,n=(t.scrollLeft+t.clientWidth)*(t.scrollTop+t.clientHeight),i=e?Math.min(n/e,1):0,r=t.showTime;return{dt_element_area:e.toFixed(2),dt_ele_imp_area:n.toFixed(2),dt_element_lvtm:r?Date.now()-r:0,dt_ele_imp_rate:i.toFixed(2)}}(a);var v=null===(o=this.observer)||void 0===o?void 0:o.getReportNode(a),g=null==v?void 0:v.uid;return this.reportEvent(n(n({pgid:l,uid:g,eventTime:Date.now()},e),{publicParams:n(n({dt_eid:s},d),e.publicParams),businessParams:n(n({},ft(d)),f)})),this},o.prototype.reportEvent=function(t){var e,n,i,r,o,a,s;void 0===t&&(t={});var l=t;this.monitoringReport&&(l.monitoringReport=!0),this.monitoringJsApiReport&&(l.monitoringJsApiReport=!0);var u=null==l?void 0:l.eventName;if(!u)return this;if(this.isCrawler)return this;var c=null==l?void 0:l.uid;l.eventTime=(null==l?void 0:l.eventTime)||Date.now(),l.publicParams=null!==(e=l.publicParams)&&void 0!==e?e:{},l.publicParams.dt_eventid_h5=this.eventId,(null===(n=this.pluginManager)||void 0===n?void 0:n.isRealTimeDebug)&&Object.assign(l.publicParams,{dt_debugid:null===(i=this.pluginManager)||void 0===i?void 0:i.dtDebugId,dt_uid:c,dt_appid:null===(r=this.pluginManager)||void 0===r?void 0:r.dtAppId}),this.eventId+=1,u===S.PGOUT&&(null===(a=null===(o=this.channel)||void 0===o?void 0:o.curChannel)||void 0===a?void 0:a.type)!==O.APP&&(l.isSendBeacon=!0);var p=this.publicParams.get("scenes_plat"),d=this.publicParams.get("hardware_os");p===Z&&d===z&&(l.isSendBeacon=!1);var h=this.formatContext(l);return this.channel.report(h),null===(s=this.extraChannel)||void 0===s||s.report(h),this},o.prototype.reportBeaconEvent=function(t){return void 0===t&&(t={}),Object.assign(t,{isSendBeacon:!0}),this.reportEvent(t)},o.prototype.formatContext=function(t){var e,i=this.pageManager.fixPgid(null==t?void 0:t.pgid),r=null==t?void 0:t.eventName,o=null===(e=null==t?void 0:t.businessParams)||void 0===e?void 0:e.eid,a=this.getPageParams(i),s=this.getPublicParams(),l=this.privateParams.getEventPrivateParams(r),u=this.publicParams.getPublicPageParams(a),c=st(n(n(n(n(n(n({},o?{dt_eid:o}:null),s),l),u),{dt_pgid:i}),null==t?void 0:t.publicParams),G),p=st(n(n({},a),null==t?void 0:t.pageParams),G),d=st((null==t?void 0:t.businessParams)||{},G);return n(n({},t),{eventName:r,pgid:i,publicParams:c,pageParams:p,businessParams:d})},o.prototype.getReportParams=function(t,e){var i;void 0===e&&(e=this.options.formatMode);var r=ut(t);if(!r)return"";var o=null===(i=this.observer)||void 0===i?void 0:i.getReportNode(r);if(!o)return"";var a=xt(o,"pgid")||this.pageManager.fixPgid(),s=o.data.eid;if(!a||!s)return"";var l=ht(o,e,n({eid:s},o.data.params)),u=this.formatContext({pgid:a,businessParams:l});return dt(u).udf_kv},o.prototype.setPageParams=function(t){var e;return null===(e=this.pageManager)||void 0===e||e.setPage({pageParams:t}),this},o.prototype.getPageParams=function(t,e){var n,i,r,o=this.pageManager.fixPgid(t);if(!o)return{};var a=null!==(i=null===(n=this.pageManager.getPage(o))||void 0===n?void 0:n.pageParams)&&void 0!==i?i:{};return e?null!==(r=null==a?void 0:a[e])&&void 0!==r?r:"":a},o.prototype.getPublicParams=function(t){return this.publicParams.get(t)},o.prototype.setPublicParams=function(t,e){return void 0===t&&(t={}),void 0===e&&(e="publicParams"),this.publicParams.set(t,e),this},o.prototype.removePublicParams=function(t){return void 0===t&&(t=""),this.publicParams.remove(t),this},o.prototype.setAccountInfo=function(t){return this.setPublicParams(t,"accountInfo"),this},o.prototype.clearAccountInfo=function(){return this.setPublicParams(V,"accountInfo"),this},o.prototype.setCommand=function(t,e){var i,r=ut(t);if(!r||!e)return this;var o=R.parse(null!==(i=r.getAttribute("dt-cmd"))&&void 0!==i?i:"");r.setAttribute("dt-cmd",R.stringify(n(n({},o),e)))},o.prototype.setPageContentId=function(t){if(t)return this.privateParams.set("dt_pg_contentid",t,[S.PGIN,S.PGOUT,S.IMP,S.IMPEND,S.CLCK]),this},o.prototype.setMultiPageContentids=function(t){var e=this;if(t)return Object.keys(t).forEach((function(n){if(e.pageManager.hasPage(n)){var i=t[n];e.pageManager.setPage({pageParams:{pgid:n,contentid:i}})}})),this},o.prototype.getAttaReportPublicParams=function(){return{platform:this.publicParams.get("platform"),hardware_os:this.publicParams.get("hardware_os"),os_version:this.publicParams.get("os_version"),session_id:this.publicParams.get("dt_sessionid_h5"),sdk_version:this.publicParams.get("dt_sdkversion_h5"),omgid:this.publicParams.get("dt_omgid"),monitoring_id:this.monitoringReportId,monitoring_report_id:this.monitoringReportId,ua:null===navigator||void 0===navigator?void 0:navigator.userAgent,scenes_plat:this.publicParams.get("scenes_plat"),scenes_version:this.publicParams.get("scenes_version")}},r([fe],o.prototype,"processVisualOption",null),r([fe],o.prototype,"initObserver",null),r([fe],o.prototype,"initPage",null),r([fe],o.prototype,"observe",null),r([fe],o.prototype,"unobserve",null),r([fe],o.prototype,"reportPV",null),r([fe],o.prototype,"reportEvent",null),r([fe],o.prototype,"reportBeaconEvent",null),o}(U);return ve}));
