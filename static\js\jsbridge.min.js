﻿window.JsBridge=function(a,l,m){a=a||{};a.iOS=/iPad|iPhone|iPod/.test(navigator.userAgent);a.iOS&&navigator.userAgent.indexOf("Android")>=0&&(a.iOS=false);var g=[];function k(){var n=document.createElement("iframe");g.push(n);n.style.cssText="position:absolute;left:0;top:0;width:0;height:0;visibility:hidden;";n.frameBorder="0";document.body.appendChild(n);return n}a._createMultiCallback=function(n,o){return function(p){n.result[o]=p;n.count--;n.count==0&&n.callback&&(p=n.callback(n.result),p&&typeof p=="object"&&a.multiCall.apply(a,[p].concat(n.callbackChain)))}};a._callWithScheme=a.callWithScheme=function(p){var n;for(var o=0;n=g[o];o++){if(!n._busy){break}}(!n||n._busy)&&(n=k());n._busy=true;n.src=p;setTimeout(function(){n._busy=false},0)};var c=[],h=function(n){c=[n]};a.onResume=h;a.addResumeEventListener=function(n){var o=c.indexOf(n);o<0&&c.push(n)};a.removeResumeEventListener=function(o){var n=c.indexOf(o);n>=0&&(c=c.slice(0,n).concat(c.slice(n+1)))};a._onResume=function(){a.onResume!=h?typeof a.onResume=="function"?a.onResume():c=[]:c.slice(0).forEach(function(n){n()})};var d=[],i=function(n){d=[n]};a.onPause=i;a.addPauseEventListener=function(n){var o=d.indexOf(n);o<0&&d.push(n)};a.removePauseEventListener=function(o){var n=d.indexOf(o);n>=0&&(d=d.slice(0,n).concat(d.slice(n+1)))};a._onPause=function(){a.onPause!=i?typeof a.onPause=="function"?a.onPause():d=[]:d.slice(0).forEach(function(n){n()})};a.ready=false;var j=[];a.onReady=function(n){a.ready?n&&n():n&&j.push(n)};a._readyCallback=function(){if(a.ready){return}a.ready=true;j.slice(0).forEach(function(n){n()})};a._coreReadyCallback=a._readyCallback;var b=function(n,o,p){if(!(this instanceof b)){return new b(n,o,p)}this.args=n||{};this.callback=o;this.context=p;this.identifier=0;this.state=0;this.percentage=0;this.installedVersion=0;this.downloadedVersion=0;this.initializing=true;b._instances[this.args.packageName]=this;b._instancePool.push(this);this._init()};b._instances={};b._instancePool=[];b.reset=function(){b._instances={};b._instancePool=[]};b.HAS_PERCENTAGE=true;var e=[],f=[];b.addInstallationStatusRetrievedEventListener=function(n){var o=e.indexOf(n);o<0&&e.push(n)};b.removeInstallationStatusRetrievedEventListener=function(o){var n=e.indexOf(o);n>=0&&(e=e.slice(0,n).concat(e.slice(n+1)))};b._dispatchInstallationStatusRetrievedEvent=function(n){e.slice(0).forEach(function(o){o(n.slice(0))})};b.addDownloadingStatusRetrievedEventListener=function(n){var o=f.indexOf(n);o<0&&f.push(n)};b.removeDownloadingStatusRetrievedEventListener=function(o){var n=f.indexOf(o);n>=0&&(f=f.slice(0,n).concat(f.slice(n+1)))};b._dispatchDownloadingStatusRetrievedEvent=function(n){n.forEach(function(o){o.initializing=false;if(o.args.autoStart){switch(o.state){case a.Download.STATE_READY:case a.Download.STATE_UPDATE:case a.Download.STATE_PAUSED:o.start();break;case a.Download.STATE_DOWNLOADED:o.install();break}}});f.slice(0).forEach(function(o){o(n.slice(0))})};b.STATE_READY=1;b.STATE_UPDATE=2;b.STATE_QUEUING=3;b.STATE_DOWNLOADING=4;b.STATE_PAUSED=5;b.STATE_DOWNLOADED=6;b.STATE_INSTALLING=7;b.STATE_INSTALLED=8;b._getDownloadState=function(n){return b._stateMap[n]};b.prototype._setIdentifier=function(n){this.identifier&&delete b._instances[this.identifier];this.identifier=n||0;this.identifier&&(b._instances[this.identifier]=this)};b.prototype._callback=function(n,o){if(!n){return}n==b.STATE_READY&&this.args.checkUpdate&&this.installedVersion&&this.args.versionCode>this.installedVersion&&(n=b.STATE_UPDATE);if(this.state==n&&n!=b.STATE_PAUSED&&(!b.HAS_PERCENTAGE||n!=b.STATE_DOWNLOADING)){return}this.state=n;switch(n){case b.STATE_READY:case b.STATE_UPDATE:this.percentage=0;break;case b.STATE_DOWNLOADED:case b.STATE_INSTALLING:case b.STATE_INSTALLED:this.percentage=100;break}this.callback&&this.callback.call(this,this.state,this.percentage,this.context,o||{})};b.prototype.doAction=function(n){if(this.initializing){return}switch(this.state){case b.STATE_QUEUING:case b.STATE_DOWNLOADING:if(this.args.preventStopWhenDoAction){break}this.stop();n&&n.onStop&&n.onStop.call(this);break;case b.STATE_DOWNLOADED:this.install();n&&n.onInstall&&n.onInstall.call(this);break;case b.STATE_INSTALLED:this.open();n&&n.onOpen&&n.onOpen.call(this);break;case b.STATE_INSTALLING:break;default:this.start();n&&n.onStart&&n.onStart.call(this);break}};a.Download=b;a.SCENE_NONE=0;a.SCENE_DOWNLOADER=1;a.SCENE_DOWNLOADER_DETAIL=2|a.SCENE_DOWNLOADER;a.SCENE_DOWNLOADER_EXTERNAL=4|a.SCENE_DOWNLOADER;a.SCENE_DOWNLOADER_SDK=8|a.SCENE_DOWNLOADER;a.SCENE_MOBILEQ=0x10;a.SCENE_WECHAT=0x20;a.SCENE_YUNOS=0x30;a.SCENE_QQMUSIC=0x40;a.SCENE_SZUSE=0x50;a.SCENE_WEISHI=0x60;a.SCENE_VIDEO=0x100;a.SCENE_WESECURE_SERIES=0x2000;a.SCENE_WESECURE=0x2200;a.SCENE_WIFIMANAGER=0x2300;a.SCENE_WIFIMANAGER_SUPPORT_DOWNLOAD=0x2400;a.SCENE_QQPIM=0x2500;a.SCENE_QQPIM_SUPPORT_DOWNLOAD=0x2600;a.SCENE_QQPIM_SUPPORT_DOWNLOAD_V2=0x2700;a.SCENE_YSDK=0x2800;a.SCENE_QQ_BROWSER=0x2900;a.SCENE_MOBILEQ_GAMECENTER=a.SCENE_MOBILEQ;a.SCENE=a.SCENE_NONE;a.SHARE_USER_SELECTION=0;a.SHARE_MOBILEQ=1;a.SAHRE_QZONE=2;a.SAHRE_WECHAT=3;a.SAHRE_WECHAT_TIMELINE=4;a.SHARE_USER_SELECTION_POPUP=5;a._shareInfo={iconUrl:"",jumpUrl:location.href,title:document.title,summary:location.href,message:"",appBarInfo:"",contentType:0,dataUrl:""};a.setShareInfo=function(o){o=o||{};o.allowShare==1||o.allowShare===true?a._showShareButton&&a._showShareButton():(o.allowShare==0||o.allowShare===false)&&a._hideShareButton&&a._hideShareButton();var n=a._shareInfo;n.iconUrl=o.iconUrl||n.iconUrl;n.jumpUrl=o.jumpUrl||n.jumpUrl;n.title=o.title||n.title;n.summary=o.summary||n.summary;n.message=o.message||n.message;n.appBarInfo=o.appBarInfo||n.appBarInfo;n.contentType=o.contentType||n.contentType;n.dataUrl=o.dataUrl||n.dataUrl;n.back=o.back||false;n.scene=o.scene||"";n.sourceName=o.sourceName||"腾讯应用宝";n.callback=o.callback||function(){};a._setShareInfo&&a._setShareInfo(o)};a.getCookie=function(o){var p=new RegExp("(?:^|;+|\\s+)"+o+"=([^;]*)"),n=document.cookie.match(p);return !n?"":n[1]};a.setCookie=function(r,s,p,q,n){if(n){var o=new Date();o.setTime(o.getTime()+3600000*n)}document.cookie=r+"="+s+"; "+(n?"expires="+o.toGMTString()+"; ":"")+(q?"path="+q+"; ":"")+(p?"domain="+p+";":"")};a.delCookie=function(p,n,o){document.cookie=p+"=; expires=Mon, 26 Jul 1997 05:00:00 GMT; "+(o?"path="+o+"; ":"")+(n?"domain="+n+";":"")};a.getLoginUin=function(){var n=a.getCookie("uin");if(!n){return 0}n=/^o(\d+)$/.exec(n);if(n&&(n=new Number(n[1])+0)>10000){return n}return 0};a.getLoginSkey=function(){return a.getCookie("skey")};a.getLoginVkey=function(){return a.getCookie("vkey")};a._greaterThanOrEqual=function(o,p){o=String(o).split(".");p=String(p).split(".");try{for(var n=0,s=Math.max(o.length,p.length);n<s;n++){var q=isFinite(o[n])&&Number(o[n])||0,r=isFinite(p[n])&&Number(p[n])||0;if(q<r){return false}else if(q>r){return true}}}catch(t){return false}return true};return a}(window.JsBridge,window);(function(a,d,p){if(!a){return}if(a.SCENE==a.SCENE_NONE){var n=navigator.userAgent.match(/\/qqdownloader\/(\d+)(?:\/(appdetail|external|sdk))?/);if(n){switch(n[2]){case"appdetail":a.SCENE=a.SCENE_DOWNLOADER_DETAIL;break;case"external":a.SCENE=a.SCENE_DOWNLOADER_EXTERNAL;break;case"sdk":a.SCENE=a.SCENE_DOWNLOADER_SDK;break;default:a.SCENE=a.SCENE_DOWNLOADER;break}}}if(!(a.SCENE&a.SCENE_DOWNLOADER)){return}a.allowBatchCall=true;var e=1,f={},g=[],i=0;function o(x,t,y,z){var u=["jsb:/",x,e,"JsBridge.callback?"].join("/"),v=[];for(var w in t){v.push(encodeURIComponent(w)+"="+encodeURIComponent(t[w]+""))}u+=v.join("&");f[e++]={callback:y,callbackChain:z};a._callWithScheme(u)}function q(){i=0;if(g.length==1){var t=g[0];o(t.name,t.args,t.callback,t.callbackChain)}else{var v=[];for(var w=0,t;t=g[w];w++){if(t.args){for(var u in t.args){t.args.hasOwnProperty(u)&&t.args[u]&&(t.args[u]=encodeURIComponent(t.args[u]))}}v.push({method:t.name,seqid:e,args:t.args,callback:"JsBridge.callback"});f[e++]={callback:t.callback}}var x=["jsb://callBatch",e++,"JsBridge.callback?param="].join("/");x+=encodeURIComponent(JSON.stringify(v));a._callWithScheme(x)}g=[]}var c=a._call=a.call=function(u,t,v){t=t||{};var w=[].slice.call(arguments,3);a.allowBatchCall?(g.push({name:u,args:t,callback:v,callbackChain:w}),!i&&(i=setTimeout(q,0))):o(u,t,v,w)};a.multiCall=function(u,z){var v=[],t={callback:z,callbackChain:[].slice.call(arguments,2),count:0,result:{}};for(var w in u){var x=u[w];v.push({method:x.name,seqid:e,args:x.args||{},callback:"JsBridge.callback"});f[e++]={callback:a._createMultiCallback(t,w)};t.count++}if(t.count==0){return}var y=["jsb://callBatch",e++,"JsBridge.callback?param="].join("/");y+=encodeURIComponent(JSON.stringify(v));a._callWithScheme(y)};a.callback=function(u){var v,t,w;f[u.seqid]&&(v=f[u.seqid],w=v.callbackChain,t=v.callback&&v.callback(u),delete f[u.seqid]);t&&typeof t=="object"&&c.apply(null,[t.name,t.args||{}].concat(w||[]))};d.activityStateCallback=function(t){t.data=="onResume"?a._onResume():t.data=="onPause"&&a._onPause()};d.readyCallback=a._coreReadyCallback;a.__onReady=a.onReady;a.onReady=function(t){!a.ready&&!a.onReady.called&&(a.onReady.called=1,a.call("isInterfaceReady",{},function(u){}));a.__onReady(t)};var b=a.Download;b._stateMap={UPDATE:b.STATE_UPDATE,QUEUING:b.STATE_QUEUING,DOWNLOADING:b.STATE_DOWNLOADING,PAUSED:b.STATE_PAUSED,DOWNLOADED:b.STATE_DOWNLOADED,INSTALLING:b.STATE_INSTALLING,INSTALLED:b.STATE_INSTALLED,FAIL:b.STATE_PAUSED};b.UITYPE_WISE_NO_WIFI_BOOKING_DOWNLOAD="WISE_NO_WIFI_BOOKING_DOWNLOAD";b.UITYPE_WISE_NO_WIFI_DOWNLOAD_DIRECTLY="WISE_NO_WIFI_DOWNLOAD_DIRECTLY";var j=0,k=[],r=function(){j=0;var t=k;k=[];var v=[],w={},u={};t.forEach(function(x){v.push(x.args.packageName)});a.multiCall({appInstalledVersion:{name:"getAppInfo",args:{packagenames:v.join(","),noupdateinfo:1}}},function(x){var y={};x.appInstalledVersion.result==0&&(y=JSON.parse(x.appInstalledVersion.data));var z=false;t.forEach(function(A){var B=y[A.args.packageName];if(B&&B.install==1){A.installedVersion=B.verCode;if(!A.args.checkUpdate||!A.args.versionCode||A.args.versionCode<=A.installedVersion){A._callback(b.STATE_INSTALLED);return}}u[A.args.packageName]={name:"queryDownload",args:{appid:A.args.hnAppId,packagename:A.args.packageName,versioncode:A.args.versionCode||"",grayversioncode:A.args.grayVersionCode||""}};z=true});b._dispatchInstallationStatusRetrievedEvent(t);if(z){return u}b._dispatchDownloadingStatusRetrievedEvent(t)},function(z){u={};var A=false;for(var B in z){var x=b._instances[B],C=z[B];if(C.result==0){var y=JSON.parse(C.data),D=b._getDownloadState(y.appstate);D==b.STATE_UPDATE?(x.identifier&&x._setIdentifier(),x._callback(b.STATE_READY)):(y.state=D,y.percentage=y.downpercent,w[x.args.packageName]=y,u[x.args.packageName]={name:"createDownload",args:{appid:x.args.hnAppId,packagename:x.args.packageName,versioncode:x.args.versionCode||"",channelid:x.args.channelId||"",scene:x.args.scene||"",sourceScene:x.args.sourceScene||"",via:x.args.via||"",recommendId:x.args.recommendId||"",status:x.args.status||"",slotId:x.args.slotId||"",sourceSceneSlotId:x.args.sourceSceneSlotId||"",modelType:x.args.modelType||"",sourceModelType:x.args.sourceModelType||"",subPosition:x.args.subPosition||"",params:x.args.params||"",hostpname:x.args.hostpname||"",autoOpen:x.args.autoOpen||0,applink:x.args.applink?"tmast://applink?actionurl="+encodeURIComponent(encodeURIComponent(x.args.applink)):"",isNoWifiDialogShow:!x.args.noWifiDialogShow}},A=true)}else{x.identifier&&x._setIdentifier();x._callback(b.STATE_READY)}}if(A){return u}b._dispatchDownloadingStatusRetrievedEvent(t)},function(A){for(var z in A){var x=b._instances[z],B=A[z];if(B.result==0){var C=JSON.parse(B.data),y=w[z];x.downloadedVersion=C.versioncode;(!x.args.versionCode||x.args.versionCode<=x.downloadedVersion)&&(x._setIdentifier(C.apkid),y.percentage&&(x.percentage=y.percentage),x._callback(y.state,y))}}b._dispatchDownloadingStatusRetrievedEvent(t)})};b.prototype._init=function(){this._callback(b.STATE_READY);k.push(this);!j&&(j=setTimeout(r,0))};b.prototype.bookingDownload=function(u){if(this.initializing){return}switch(this.state){case b.STATE_READY:case b.STATE_UPDATE:if(!this.identifier){var t=this;c("createDownload",{hostpname:this.args.hostpname||"",appid:this.args.hnAppId,packagename:this.args.packageName,versioncode:this.args.versionCode||"",reCreate:this.downloadedVersion?1:0,channelid:this.args.channelId||"",scene:this.args.scene||"",slotId:this.args.slotId||"",sourceScene:this.args.sourceScene||"",sourceSlotId:this.args.sourceSlotId||"",params:this.args.params||"",via:this.args.via||"",status:this.args.status||"",recommendId:this.args.recommendId||"",modelType:this.args.modelType||"",sourceModelType:this.args.sourceModelType||"",subPosition:this.args.subPosition||"",bookingFlag:1,oplist:typeof this.args.oplist=="undefined"?"2":(this.args.oplist+"").replace(/;?1;?/,""),autoOpen:this.args.autoOpen||0,applink:t.args.applink?"tmast://applink?actionurl="+encodeURIComponent(encodeURIComponent(t.args.applink)):"",isNoWifiDialogShow:!t.args.noWifiDialogShow},function(v){v.result==0?(c("startDownload",{apkid:t.identifier,scene:t.args.scene||"",sourceScene:t.args.sourceScene||"",via:t.args.via||"",recommendId:t.args.recommendId||"",packagename:t.args.packageName,versioncode:t.args.versionCode||"",status:t.args.status||"",slotId:t.args.slotId||"",sourceSceneSlotId:t.args.sourceSceneSlotId||"",modelType:t.args.modelType||"",sourceModelType:t.args.sourceModelType||"",subPosition:t.args.subPosition||"",grayversioncode:t.args.grayVersionCode||"",params:t.args.params||""}),u({success:true})):t.downloadedVersion?(t.downloadedVersion=0,t.bookingDownload(u)):u({err:"内部错误"})});break}else{u({err:"已有任务"})}break;default:u({err:"已有任务"})}};b.prototype.start=function(){if(this.initializing){return}switch(this.state){case b.STATE_READY:case b.STATE_UPDATE:if(!this.identifier){var t=this;c("createDownload",{hostpname:this.args.hostpname||"",appid:this.args.hnAppId,packagename:this.args.packageName,versioncode:this.args.versionCode||"",reCreate:this.downloadedVersion?1:0,channelid:this.args.channelId||"",scene:this.args.scene||"",sourceScene:this.args.sourceScene||"",via:this.args.via||"",recommendId:this.args.recommendId||"",status:this.args.status||"",slotId:this.args.slotId||"",sourceSceneSlotId:this.args.sourceSceneSlotId||"",modelType:this.args.modelType||"",sourceModelType:this.args.sourceModelType||"",subPosition:this.args.subPosition||"",params:this.args.params||"",oplist:typeof this.args.oplist=="undefined"?"2":(this.args.oplist+"").replace(/;?1;?/,""),autoOpen:this.args.autoOpen||0,applink:t.args.applink?"tmast://applink?actionurl="+encodeURIComponent(encodeURIComponent(t.args.applink)):"",isNoWifiDialogShow:!t.args.noWifiDialogShow},function(u){if(u.result==0){var v=JSON.parse(u.data);t._setIdentifier(v.apkid);c("startDownload",{apkid:t.identifier,scene:t.args.scene||"",sourceScene:t.args.sourceScene||"",via:t.args.via||"",recommendId:t.args.recommendId||"",packagename:t.args.packageName,versioncode:t.args.versionCode||"",status:t.args.status||"",slotId:t.args.slotId||"",sourceSceneSlotId:t.args.sourceSceneSlotId||"",modelType:t.args.modelType||"",sourceModelType:t.args.sourceModelType||"",subPosition:t.args.subPosition||"",grayversioncode:t.args.grayVersionCode||"",params:t.args.params||""})}else{		t.downloadedVersion&&(t.downloadedVersion=0,t.start())}});break}case b.STATE_PAUSED:c("startDownload",{apkid:this.identifier,scene:this.args.scene||"",sourceScene:this.args.sourceScene||"",via:this.args.via||"",recommendId:this.args.recommendId||"",packagename:this.args.packageName,versioncode:this.args.versionCode||"",status:this.args.status||"",slotId:this.args.slotId||"",sourceSceneSlotId:this.args.sourceSceneSlotId||"",modelType:this.args.modelType||"",sourceModelType:this.args.sourceModelType||"",subPosition:this.args.subPosition||"",grayversioncode:this.args.grayVersionCode||"",params:this.args.params||""});break}};b.prototype.stop=function(){if(this.initializing){return}c("pauseDownload",{apkid:this.identifier})};b.prototype.install=function(){if(this.initializing){return}if(!this.identifier){var t=this;c("createDownload",{hostpname:this.args.hostpname||"",appid:this.args.hnAppId,packagename:this.args.packageName,versioncode:this.args.versionCode||"",channelid:this.args.channelId||"",scene:this.args.scene||"",sourceScene:this.args.sourceScene||"",via:this.args.via||"",recommendId:this.args.recommendId||"",status:this.args.status||"",slotId:this.args.slotId||"",sourceSceneSlotId:this.args.sourceSceneSlotId||"",modelType:this.args.modelType||"",sourceModelType:this.args.sourceModelType||"",subPosition:this.args.subPosition||"",params:this.args.params||"",autoOpen:this.args.autoOpen||0,applink:t.args.applink?"tmast://applink?actionurl="+encodeURIComponent(encodeURIComponent(t.args.applink)):"",isNoWifiDialogShow:!t.args.noWifiDialogShow},function(u){if(u.result==0){var v=JSON.parse(u.data);t._setIdentifier(v.apkid);t.install()}});return}c("startDownload",{apkid:this.identifier,scene:this.args.scene||"",sourceScene:this.args.sourceScene||"",via:this.args.via||"",recommendId:this.args.recommendId||"",status:this.args.status||"",slotId:this.args.slotId||"",sourceSceneSlotId:this.args.sourceSceneSlotId||"",modelType:this.args.modelType||"",sourceModelType:this.args.sourceModelType||"",subPosition:this.args.subPosition||"",params:this.args.params||"",packagename:this.args.packageName,versioncode:this.args.versionCode||"",grayVersioncode:this.args.grayVersionCode||""})};b.prototype.open=function(){if(this.initializing){return}a.startApp(this.args.packageName,this.args.scene,this.args.applink||"")};d.stateCallback=function(w){if(w.result!=0){return}var t=JSON.parse(w.data),u=b._instances[t.apkid]||b._instances[t.packageName];if(!u){return}!u.identifier&&u._setIdentifier(t.apkid);var v=b._getDownloadState(t.appstate);if(t.action=="1009"){v!=b.STATE_DOWNLOADED&&v!=b.STATE_INSTALLED&&u._callback(b.STATE_READY,t);u._setIdentifier();return}(t.down_percent_decimal||t.down_percent)&&(u.percentage=t.down_percent_decimal||t.down_percent);u._callback(v,t)};d.appInstallUninstall=function(v){if(v.result==0){var u=JSON.parse(v.data),t=b._instances[u.packageName];t&&a.getAppInstalledVersion(u.packageName,function(w){t.installedVersion=w[u.packageName]||0;u.state==1&&(!t.args.checkUpdate||!t.args.versionCode||t.args.versionCode<=t.installedVersion)?t._callback(b.STATE_INSTALLED):t._callback(b.STATE_READY)})}};a.getAppInstalledVersion=function(t,u){if({}.toString.call(t)!="[object Array]"){return a.getAppInstalledVersion([t],u)}c("getAppInfo",{packagenames:t.join(","),noupdateinfo:1},function(x){var y={};if(x.result==0){var v=JSON.parse(x.data);for(var w in v){v[w].install==1&&(y[w]=v[w].verCode)}}u&&u(y)})};a.startApp=function(t,u,v){c("startOpenApp",{packageName:t,scene:u||"",applink:v||""})};a._setShareInfo=function(t){t&&t.callback&&(d.shareCallback=function(u){t.callback(u)})};a._showShareButton=function(){c("setWebView",{buttonVisible:1})};a._hideShareButton=function(){c("setWebView",{buttonVisible:0})};a.share=function(u){var t=a._shareInfo;c("share",{title:t.title,summary:t.summary,iconUrl:t.iconUrl,jumpUrl:t.jumpUrl,type:u||a.SHARE_USER_SELECTION,message:t.message,appBarInfo:t.appBarInfo,contentType:t.contentType,dataUrl:t.dataUrl,scene:t.scene||""})};d.clickCallback=function(){a.share()};a.showPictures=function(u,t){c("showPics",{urls:JSON.stringify(u),position:isFinite(t)&&t>=0&&t<u.length?t:0})};a.openNewWindow=function(u,t){t=t||{};t.url=u;c("openNewWindow",t)};var h;a.LOGIN_TYPE_MOBILEQ_QUICK="QMOBILEQ";a.LOGIN_TYPE_MOBILEQ="MOBILEQ";a.LOGIN_TYPE_WECHAT="WX";a.LOGIN_TYPE_USER_SELECTION="DEFAULT";a.LOGIN_TYPE_NONE="NONE";a.login=function(t,w,u){a.getLoginType()!=a.LOGIN_TYPE_NONE&&a.logout();c("openLoginActivity",{logintype:w||a.LOGIN_TYPE_MOBILEQ_QUICK});var v=d.activityStateCallback;h=function(){d.activityStateCallback=v;t&&t.apply(null,arguments)};d.activityStateCallback=function(x){x.data=="onResume"&&(d.activityStateCallback=v,u&&u())}};a.logout=function(){a.setCookie("logintype",a.LOGIN_TYPE_NONE,"qq.com","/");a.delCookie("openid","qq.com","/");a.delCookie("accesstoken","qq.com","/");a.delCookie("qopenid","qq.com","/");a.delCookie("qaccesstoken","qq.com","/");a.delCookie("openappid","qq.com","/");a.delCookie("uin","qq.com","/");a.delCookie("skey","qq.com","/");a.delCookie("sid","qq.com","/");a.delCookie("vkey","qq.com","/")};a.getLoginType=function(){return a.getCookie("logintype")||a.LOGIN_TYPE_NONE};a.getLoginOpenId=function(){return a.getCookie("openid")};a.getLoginAccessToken=function(){return a.getCookie("accesstoken")};a.refreshLoginToken=function(u,t){if(a.getLoginType()!=u){return t(false)}u==a.LOGIN_TYPE_MOBILEQ||u==a.LOGIN_TYPE_MOBILEQ_QUICK?c("refreshUserLoginToken",{ticket:"qskey"},function(v){v.result==0&&v.data=="true"?t(true):t(false)}):t(false)};d.loginCallback=function(t){h&&h(t);h=p};var l=[],m=0;function s(){var t=[];l.forEach(function(v){t.push({method:"report",seqid:0,args:v})});var u="jsb://callBatch/0/JsBridge.callback?param="+encodeURIComponent(JSON.stringify(t));a._callWithScheme(u);l=[];m=0}a.report=function(t){l.push(t||{});!m&&(m=setTimeout(s,1000))};a.reportImmediate=function(t){c("report",t)};d.userFitCallback=function(){}})(window.JsBridge,window);(function(a,e,q){if(!a){return}if(a.SCENE==a.SCENE_NONE){var l=navigator.userAgent.match(/(?:\bV1_AND_SQI?_|QQ\/)([\d\.]+)/);l&&a._greaterThanOrEqual(l[1],"4.6")&&(a.SCENE=a.SCENE_MOBILEQ)}if(!(a.SCENE&a.SCENE_MOBILEQ)){return}a._coreReadyCallback=function(){};a.iOS=/(iPad|iPhone|iPod).*? (IPad)?QQ\/([\d\.]+)/.test(navigator.userAgent);a.iOS&&/\bV1_AND_SQI?_([\d\.]+)(.*? QQ\/([\d\.]+))?/.test(navigator.userAgent)&&(a.iOS=false);a.isPublicAccount=a.iOS||navigator.userAgent.indexOf("Agent/")<0&&navigator.userAgent.indexOf("V1_AND_SQ")>=0;var g=1,d={},c=a._call=a.call=function(v){var s=[],t=[],u=false;[].slice.call(arguments,1).forEach(function(w){typeof w=="function"?(t.push(w),u=true):!u&&s.push(w)});var r=["jsbridge:/",v];a.iOS?(r=r.join("/"),s.forEach(function(w,x){r+=(x==0?"?p=":"&p"+x+"=")+encodeURIComponent(typeof w=="object"?JSON.stringify(w):w+"")}),r+="#"+g):(r.push(g),s.forEach(function(w){r.push(encodeURIComponent(typeof w=="object"?JSON.stringify(w):w+""))}),r=r.join("/"));d[g++]={callback:t[0],callbackChain:t.slice(1)};a._callWithScheme(r)};a.multiCall=function(r,v){var s={callback:v,callbackChain:[].slice.call(arguments,2),count:0,result:{}};for(var t in r){var u=r[t];s.count++;c.apply(null,[u.name].concat(u.args||[]).concat([a._createMultiCallback(s,t)]))}};a.callback=function(u,r){if(u=="resume"){a._onResume();return}var s,t,v;r&&r.r==0&&(isFinite(u)?d[u]&&(s=d[u],v=s.callbackChain,t=s.callback&&s.callback(r.result),delete d[u]):r.guid&&d[r.guid]&&(s=d[r.guid],v=s.callbackChain,t=s.callback&&s.callback(r.data),delete d[r.guid]));t&&typeof t=="object"&&c.apply(null,[t.name].concat(t.args||[]).concat(v||[]))};e.QzoneApp={fire:function(r,s){a.callback(r,s)}};a._coreReadyCallback();if(a.iOS){delete a.Download}else{var b=a.Download;b._stateMap={20:b.STATE_QUEUING,2:b.STATE_DOWNLOADING,12:b.STATE_DOWNLOADING,3:b.STATE_PAUSED,4:b.STATE_DOWNLOADED,5:b.STATE_INSTALLING,6:b.STATE_INSTALLED,13:b.STATE_INSTALLED,10:b.STATE_READY};b.UITYPE_WISE_NO_WIFI_BOOKING_DOWNLOAD="NO_SUPPORT";b.prototype._getDownloadParam=function(r){return{myAppConfig:this.args.useDownloaderIfExists?1:0,appid:this.args.sngAppId,url:this.args.url,packageName:this.args.packageName,actionCode:r,appName:this.args.alias||"",isAutoDownload:1,isAutoInstall:1,via:this.args.via||""}};var h=0,i=[],m=[],n=function(){h=0;var r=i;i=[];var s={};r.forEach(function(t){s[t.args.packageName]={name:"qqZoneAppList/getAppVersionCode",args:{appid:t.args.sngAppId,packageName:t.args.packageName,downloadUrl:""}}});a.multiCall(s,function(u){var t=[];r.forEach(function(v){var w=JSON.parse(u[v.args.packageName].replace(/downloadedVersionCode /g,"downloadedVersionCode"));if(w){if(w.installedVersionCode){v.installedVersion=w.installedVersionCode;if(!v.args.checkUpdate||!v.args.versionCode||v.args.versionCode<=v.installedVersion){v._callback(b.STATE_INSTALLED);return}}w.downloadedVersionCode&&(v.downloadedVersion=w.downloadedVersionCode)}t.push({appid:v.args.sngAppId,packageName:v.args.packageName})});b._dispatchInstallationStatusRetrievedEvent(r);if(t.length==0){b._dispatchDownloadingStatusRetrievedEvent(r);return}m.push(r);c("q_download/getQueryDownloadAction",a.isPublicAccount?{guid:0,infolist:t}:t)})};b.prototype._init=function(){b._instances[this.args.sngAppId]=this;this._callback(b.STATE_READY);i.push(this);!h&&(h=setTimeout(n,0))};b.prototype.start=function(){if(this.initializing){return}switch(this.state){case b.STATE_READY:case b.STATE_UPDATE:c("q_download/doDownloadAction",this._getDownloadParam(this.installedVersion?12:2));break;case b.STATE_PAUSED:c("q_download/doDownloadAction",this._getDownloadParam(2));break}};b.prototype.stop=function(){if(this.initializing){return}c("q_download/doDownloadAction",this._getDownloadParam(3))};b.prototype.install=function(){if(this.initializing){return}c("q_download/doDownloadAction",this._getDownloadParam(5))};b.prototype.open=function(){if(this.initializing){return}a.startApp(this.args.packageName)};function f(s){if(Object.prototype.toString.call(s)=="[object Array]"){var w={};for(var x=0,t;t=s[x];x++){f(t);var r=b._instances[t.appid]||b._instances[t.packagename];r&&(w[r.args.packageName]=1)}var u=m.shift();u&&(u.forEach(function(y){!w[y.args.packageName]&&y.state!=b.STATE_INSTALLED&&y._callback(b.STATE_READY)}),b._dispatchDownloadingStatusRetrievedEvent(u));return}var r=b._instances[s.appid]||b._instances[s.packagename];if(!r){return}if(s.state==9){a.getAppInstalledVersion(r.args.packageName,function(y){r.installedVersion=y[r.args.packageName]||0;r._callback(b.STATE_READY)});return}var v=b._getDownloadState(s.state);r.downloadedVersion&&r.args.versionCode&&r.args.versionCode>r.downloadedVersion&&v==b.STATE_DOWNLOADED&&(v=b.STATE_READY);s.pro&&(r.percentage=s.pro);r._callback(v)}b.callback=function(r){f(r)};e.publicAccountDownload={queryProcess:function(r){f(r)}};a.onReady(function(){c("q_download/registerDownloadCallBackListener","JsBridge.Download.callback")});a.getAppInstalledVersion=function(r,s){if({}.toString.call(r)!="[object Array]"){return a.getAppInstalledVersion([r],s)}c("qzone_app/getAppInfoBatch",r.join(","),",",false,false,false,function(t){t=t&&JSON.parse(t.data||t)||[];var v={};for(var w=0,u;u=t[w];w++){v[u[0]]=u[1]}s&&s(v)})};a.startApp=function(r){c("qqZoneAppList/startApp",r,"")};a.setDownloaderFirstOpenPage=function(r){c("q_download/setDownloaderFirstOpenPage",{url:r})};var o=a.callback;a.callback=function(s,r){s=="interface.getQueryDownloadAction"?r.r==0&&f(r.data):o(s,r)}}a._setShareInfo=function(){e.shareCallback=function(r){a.share(parseInt(r)+1)};c(a.iOS?"nav/addWebShareListener":"ui/setOnShareHandler",{callback:"shareCallback"});delete a._setShareInfo};a.share=function(t){t=t||a.SHARE_USER_SELECTION;var s=a._shareInfo,r={title:s.title,desc:s.summary,share_url:s.jumpUrl,image_url:s.iconUrl,back:s.back||false,sourceName:s.sourceName,srcName:s.sourceName,appid:"1101070898"};r.share_url&&(r.share_url=r.share_url.replace(/(\\?|#|&)(?:sid|3g_sid)=(?:[^&#?]*)(\\?|$|&|#)/i,"$1$2"));r.desc&&(r.desc=r.desc.length>50?r.desc.substr(0,50)+"...":r.desc);if(t==a.SHARE_USER_SELECTION){var u=function(){c("ui/showShareMenu")};a.iOS?(c("data/setShareInfo",{params:r}),u()):c("QQApi/setShareInfo",r,u)}else{r.share_type=t-1;a.iOS?c("nav/shareURLWebRichData",r):c("QQApi/shareMsg",r,s.callback)}};a.showPictures=function(s,r){c("troopNotice/showPicture",{imageIDs:s,index:isFinite(r)&&r>=0&&r<s.length?r:0})};a.openNewWindow=function(r,t,s){s=s||{};var u=/Agent\/\d+/.test(navigator.userAgent);a.iOS?c("nav/openLinkInNewWebView",{url:r,options:{styleCode:{1:4,2:2,3:5}[t]||1}}):u?c("qqZoneAppList/goUrl",JSON.stringify({url:r,titleInfo:[{name:s.name||"",tipNum:0}]})):c("qbizApi/openLinkInNewWebView",r,t||0)};var j=[],k=0;function p(){var r="jsbridge://qqZoneAppList/reportForViaBatch/0/"+encodeURIComponent(JSON.stringify(j));a._callWithScheme(r);j=[];k=0}a.report=function(r){j.push(r||{});!k&&(k=setTimeout(p,1000))};a.reportImmediate=function(r){c("report",r)};e.userFitCallback=function(){};a._readyCallback()})(window.JsBridge,window);(function(a,f,o){if(!a){return}if(a.SCENE==a.SCENE_NONE){var j=navigator.userAgent.match(/MicroMessenger\/([\d\.]+)/);j&&a._greaterThanOrEqual(j[1],"5.0")&&(a.SCENE=a.SCENE_WECHAT)}if(!(a.SCENE&a.SCENE_WECHAT)){return}var c=a._call=a.call=function(q,r,p){var s=[].slice.call(arguments,3);a.onReady(function(){WeixinJSBridge.invoke(q,r,function(t){t=p&&p(t);t&&typeof t=="object"&&c.apply(null,[t.name,t.args||{}].concat(s||[]))})})};a.multiCall=function(p,t){var q={callback:t,callbackChain:[].slice.call(arguments,2),count:0,result:{}};for(var r in p){var s=p[r];c(s.name,s.args,a._createMultiCallback(q,r));q.count++}};var d=a._on=a.on=function(q,p){a.onReady(function(){WeixinJSBridge.on(q,function(r){p&&p(r)})})};a._originalResumeCallback=function(p){p.state=="onResume"&&a._onResume()};d("activity:state_change",a._originalResumeCallback);a.callWithScheme=function(p,q){/MicroMessenger\/([0-5]\.|6\.[0-4]\.|6\.5\.[0-5]\.)/i.test(navigator.userAgent)?a._callWithScheme(p):c("launchApplication",{schemeUrl:p},function(r){r.err_msg=="launchApplication:fail"&&(q&&typeof q=="function"&&q(),a._callWithScheme(p))})};f.WeixinJSBridge?a._coreReadyCallback():document.addEventListener("WeixinJSBridgeReady",a._readyCallback,false);if(a.iOS){delete a.Download}else{var b=a.Download,g="JsBridge-Download";function l(q){if(f.localStorage){var p=localStorage.getItem(g);if(p){try{return JSON.parse(p)[q].downloadId||0}catch(r){}}}return 0}function k(u,s){if(f.localStorage){var p=localStorage.getItem(g),t=Date.now();if(p){try{var q=JSON.parse(p);p={};for(var r in q){t-q[r].timestamp<7*24*3600*1000&&(p[r]=q[r])}}catch(v){p={}}}else{p={}}s&&(p[u]={timestamp:t,downloadId:s});localStorage.setItem(g,JSON.stringify(p))}}b._stateMap={downloading:b.STATE_DOWNLOADING,download_succ:b.STATE_DOWNLOADED,download_fail:b.STATE_READY,"default":b.STATE_READY};b.UITYPE_WISE_NO_WIFI_BOOKING_DOWNLOAD="NO_SUPPORT";b.HAS_PERCENTAGE=false;var e=function(){this.pool=[];this.countInstallation=0;this.countDownloading=1};e.prototype.push=function(p){this.pool.push(p);this.countInstallation++};e.prototype.callbackInstallation=function(p){this.countInstallation--;this.countInstallation==0&&(p&&b._dispatchInstallationStatusRetrievedEvent(this.pool),this.callbackDownloading(p))};e.prototype.callbackDownloading=function(p){this.countDownloading--;this.countInstallation==0&&this.countDownloading==0&&p&&b._dispatchDownloadingStatusRetrievedEvent(this.pool)};var h=0,i=new e(),m=function(){h=0;i=new e()};b.prototype._init=function(){this._callback(b.STATE_READY);this._onResume(true)};b.prototype._onResume=function(r){if(!this.args.md5){var s=/[0-9A-F]{32}/i.exec(this.args.url);s&&(this.args.md5=s[0])}i.push(this);!h&&(h=setTimeout(m,0));var p=this,q=i;c("getInstallState",{packageName:p.args.packageName},function(t){if(t.err_msg.indexOf("get_install_state:yes")>=0){var u=t.err_msg.split("_");u.length>0&&(p.installedVersion=Number(u[u.length-1]))}t=null;if(p.installedVersion&&(!p.args.checkUpdate||!p.args.versionCode||p.args.versionCode<=p.installedVersion)){p._callback(b.STATE_INSTALLED)}else{var v=l(p.args.md5);v?(p._setIdentifier(v),q.countDownloading++,t={name:"queryDownloadTask",args:{download_id:v}}):p._callback(b.STATE_READY)}q.callbackInstallation(r);return t},function(t){if(t.err_msg.indexOf("query_download_task:ok")>=0){var u=b._getDownloadState(t.state);p._callback(u)}q.callbackDownloading(r)})};b.prototype.start=function(){if(this.initializing){return}switch(this.state){case b.STATE_READY:case b.STATE_UPDATE:var p=this;c("addDownloadTask",{task_name:this.args.alias,task_url:this.args.url,file_md5:this.args.md5,title:this.args.title||"",thumb_url:this.args.thumb_url||"",privacy_agreement_url:this.args.privacy_agreement_url||"",developer:this.args.developer||"",app_version:this.args.app_version||"",permission_url:this.args.permission_url||""},function(q){q.err_msg.indexOf("add_download_task:ok")>=0&&(p._setIdentifier(q.download_id),k(p.args.md5,q.download_id),p._callback(b.STATE_DOWNLOADING))});break}};b.prototype.stop=function(){if(this.initializing){return}var p=this;c("cancelDownloadTask",{download_id:this.identifier},function(q){p._setIdentifier();k(p.args.md5);p._callback(b.STATE_READY)})};b.prototype.install=function(){if(this.initializing){return}c("installDownloadTask",{download_id:this.identifier})};b.prototype.open=function(){if(this.initializing){return}a.startApp(this.args.packageName,this.args.signature)};d("wxdownload:state_change",function(q){var p=b._instances[q.download_id];if(!p){return}var r=b._getDownloadState(q.state);p._callback(r);r==b.STATE_DOWNLOADED&&p.install()});a.getAppInstalledVersion=function(p,q){if({}.toString.call(p)!="[object Array]"){return a.getAppInstalledVersion([p],q)}var r={};p.forEach(function(s){r[s]={name:"getInstallState",args:{packageName:s}}});a.multiCall(r,function(s){var t={};p.forEach(function(u){if(s[u].err_msg.indexOf("get_install_state:yes")>=0){var v=s[u].err_msg.split("_");v.length>0&&(t[u]=Number(v[v.length-1]))}});q&&q(t)})};a.startApp=function(p,q){c("launch3rdApp",{type:1,packageName:p,signature:q})};a.writeCommData=function(p,q){c("writeCommData",{packageName:p,data:JSON.stringify({starttime:Date.now(),endtime:Date.now()+3600000,url:q})})};a.setDownloaderFirstOpenPage=function(p){a.writeCommData("com.tencent.android.qqdownloader","tmast://webview?url="+encodeURIComponent(p))};var n=a._originalResumeCallback;a._originalResumeCallback=function(p){n(p);p.state=="onResume"&&b._instancePool.forEach(function(q){q._onResume()})};d("activity:state_change",a._originalResumeCallback)}a._showShareButton=function(){c("showOptionMenu")};a._hideShareButton=function(){c("hideOptionMenu")};a._setShareInfo=function(){d("menu:share:appmessage",function(){a.share(a.SAHRE_WECHAT)});d("menu:share:timeline",function(){a.share(a.SAHRE_WECHAT_TIMELINE)});d("menu:share:qq",function(){a.share(a.SHARE_MOBILEQ)});d("menu:share:QZone",function(){a.share(a.SAHRE_QZONE)});delete a._setShareInfo};a.share=function(q){console.log("share",q);var p=a._shareInfo;c(q==a.SAHRE_WECHAT?"sendAppMessage":q==a.SHARE_MOBILEQ?"shareQQ":q==a.SAHRE_QZONE?"shareQZone":"shareTimeline",{appid:"wx3909f6add1206543",img_url:p.iconUrl,link:p.jumpUrl,desc:p.summary,title:p.title},p.callback)};a.showPictures=function(q,p){c("imagePreview",{current:q[isFinite(p)&&p>=0&&p<q.length?p:0],urls:q})}})(window.JsBridge,window);